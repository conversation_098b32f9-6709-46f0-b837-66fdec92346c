<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shipping Screen Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }

        .shipping-page {
            max-width: 400px;
            margin: 0 auto;
            background: #f8f9fa;
        }

        .header {
            background: linear-gradient(135deg, #FFEA00 0%, #FFE066 100%);
            padding: 20px;
            border-radius: 0 0 30px 30px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(255, 234, 0, 0.25);
        }

        .header h1 {
            color: #000;
            font-size: 20px;
            font-weight: 600;
        }

        .search-add-section {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            padding: 0 20px;
        }

        .search-container {
            flex: 1;
            background: white;
            border-radius: 18px;
            padding: 12px 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .search-input {
            border: none;
            outline: none;
            width: 100%;
            font-size: 14px;
            color: #666;
        }

        .add-button {
            background: black;
            color: white;
            border: none;
            border-radius: 18px;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
        }

        .shipment-cards {
            padding: 0 20px;
        }

        .shipment-card {
            background: #ffffff;
            border-radius: 18px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            margin-bottom: 18px;
            transition: all 0.3s ease;
        }

        .shipment-card.featured {
            background: #FFEA00;
            box-shadow: 0 4px 15px rgba(255, 234, 0, 0.3);
        }

        .card-content {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .left-section {
            flex: 1;
            padding-right: 16px;
        }

        .ref-info {
            margin-bottom: 12px;
        }

        .ref-info .ref-label {
            display: block;
            font-size: 11px;
            color: #666;
            margin-bottom: 2px;
            font-weight: 500;
        }

        .ref-info .ref-number {
            font-size: 14px;
            font-weight: 700;
            color: #000;
        }

        .driver-info .driver-name,
        .driver-info .driver-phone {
            margin-bottom: 8px;
        }

        .right-section {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .action-icons {
            display: flex;
            flex-direction: column;
            gap: 4px;
            margin-bottom: 8px;
        }

        .icon-wrapper {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .location-icon-wrapper {
            background: #4CAF50;
        }

        .edit-icon-wrapper {
            background: #FF9800;
        }

        .delete-icon-wrapper {
            background: #f44336;
        }

        .icon {
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .location-info {
            text-align: right;
        }

        .location-info .from-location,
        .location-info .to-location,
        .location-info .etd-info {
            margin-bottom: 8px;
        }

        .label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .value {
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }

        .card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 8px;
        }

        .payment-section {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .status-section {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .heart-icon {
            font-size: 16px;
            color: #f44336;
        }

        .badge {
            font-size: 11px;
            font-weight: 600;
            padding: 3px 8px;
            border-radius: 6px;
            text-align: center;
            min-width: 50px;
            color: white;
        }

        .prepaid {
            background: #f44336;
        }

        .pending {
            background: #FF9800;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 20px;
            padding: 0 20px;
        }

        .page-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border: 1px solid #e0e0e0;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .page-btn.active {
            background: #FFEA00;
            border-color: #FFEA00;
            font-weight: 700;
        }
    </style>
</head>

<body>
    <div class="shipping-page">
        <!-- Header -->
        <div class="header">
            <h1>Shipment Management</h1>
        </div>

        <!-- Search and Add Section -->
        <div class="search-add-section">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search here...">
            </div>
            <button class="add-button">+ Add</button>
        </div>

        <!-- Shipment Cards -->
        <div class="shipment-cards">
            <!-- Featured Card (Yellow) -->
            <div class="shipment-card featured">
                <div class="card-content">
                    <div class="left-section">
                        <div class="ref-info">
                            <span class="ref-label">Ref. ID</span>
                            <span class="ref-number">#15486524</span>
                        </div>
                        <div class="driver-info">
                            <div class="driver-name">
                                <span class="label">Driver Name</span>
                                <span class="value">John Smith</span>
                            </div>
                            <div class="driver-phone">
                                <span class="label">Driver Phone</span>
                                <span class="value">(*************</span>
                            </div>
                        </div>
                    </div>
                    <div class="right-section">
                        <div class="action-icons">
                            <div class="icon-wrapper location-icon-wrapper">
                                <span class="icon">📍</span>
                            </div>
                            <div class="icon-wrapper edit-icon-wrapper">
                                <span class="icon">✏️</span>
                            </div>
                            <div class="icon-wrapper delete-icon-wrapper">
                                <span class="icon">🗑️</span>
                            </div>
                        </div>
                        <div class="location-info">
                            <div class="from-location">
                                <div class="label">From</div>
                                <div class="value">Alberta, CA</div>
                            </div>
                            <div class="to-location">
                                <div class="label">To</div>
                                <div class="value">Red Deer</div>
                            </div>
                            <div class="etd-info">
                                <div class="label">ETD</div>
                                <div class="value">25-03-25, 11:30pm</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="payment-section">
                        <span class="label">Payment Type</span>
                        <span class="badge prepaid">Prepaid</span>
                    </div>
                    <div class="status-section">
                        <span class="heart-icon">❤️</span>
                        <span class="badge pending">Pending</span>
                    </div>
                </div>
            </div>

            <!-- Regular Card (White) -->
            <div class="shipment-card">
                <div class="card-header">
                    <div class="ref-info">
                        <span class="ref-label">Ref. ID</span>
                        <span class="ref-number">#15486525</span>
                    </div>
                    <div class="action-icons">
                        <div class="icon-wrapper location-icon-wrapper">
                            <span class="icon">📍</span>
                        </div>
                        <div class="icon-wrapper edit-icon-wrapper">
                            <span class="icon">✏️</span>
                        </div>
                        <div class="icon-wrapper delete-icon-wrapper">
                            <span class="icon">🗑️</span>
                        </div>
                    </div>
                </div>

                <div class="driver-section">
                    <div class="driver-row">
                        <span class="label">Driver Name</span>
                        <span class="value">John Smith</span>
                    </div>
                    <div class="driver-row">
                        <span class="label">Driver Phone</span>
                        <span class="value">(*************</span>
                    </div>
                </div>

                <div class="location-section">
                    <div class="location-row">
                        <div class="location-item">
                            <div class="label">From</div>
                            <div class="value">Alberta, CA</div>
                        </div>
                        <div class="location-item to">
                            <div class="label">To</div>
                            <div class="value">Red Deer</div>
                        </div>
                    </div>
                    <div class="etd-row">
                        <span class="label">ETD</span>
                        <span class="value">25-03-25, 11:30pm</span>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="payment-info">
                        <span class="label">Payment Type</span>
                        <span class="badge prepaid">Prepaid</span>
                    </div>
                    <div class="status-info">
                        <span class="badge pending">Pending</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <div class="pagination">
            <div class="page-btn">‹</div>
            <div class="page-btn">1</div>
            <div class="page-btn">2</div>
            <div class="page-btn">3</div>
            <div class="page-btn active">4</div>
            <div class="page-btn">5</div>
            <div class="page-btn">6</div>
            <div class="page-btn">7</div>
            <div class="page-btn">›</div>
        </div>
    </div>
</body>

</html>