#!/bin/bash

echo "🧪 iOS Location Permission Test Script"
echo "======================================"

# Check if we're in the right directory
if [ ! -f "capacitor.config.ts" ]; then
    echo "❌ Error: Please run this script from the mobileApp directory"
    exit 1
fi

echo ""
echo "🔍 Checking iOS Configuration..."

# Check Info.plist permissions
echo ""
echo "1️⃣ Info.plist Location Permissions:"
if [ -f "ios/App/App/Info.plist" ]; then
    if grep -q "NSLocationWhenInUseUsageDescription" ios/App/App/Info.plist; then
        echo "   ✅ NSLocationWhenInUseUsageDescription: Found"
    else
        echo "   ❌ NSLocationWhenInUseUsageDescription: Missing"
    fi
    
    if grep -q "NSLocationAlwaysAndWhenInUseUsageDescription" ios/App/App/Info.plist; then
        echo "   ✅ NSLocationAlwaysAndWhenInUseUsageDescription: Found"
    else
        echo "   ❌ NSLocationAlwaysAndWhenInUseUsageDescription: Missing"
    fi
    
    if grep -q "location-services" ios/App/App/Info.plist; then
        echo "   ✅ location-services capability: Found"
    else
        echo "   ❌ location-services capability: Missing"
    fi
else
    echo "   ❌ Info.plist not found at ios/App/App/Info.plist"
fi

# Check Capacitor versions
echo ""
echo "2️⃣ Capacitor Versions:"
if command -v npx &> /dev/null; then
    echo "   📦 Capacitor CLI: $(npx cap --version 2>/dev/null || echo 'Not found')"
    
    if [ -f "package.json" ]; then
        echo "   📦 @capacitor/core: $(grep '"@capacitor/core"' package.json | sed 's/.*: *"\([^"]*\)".*/\1/')"
        echo "   📦 @capacitor/ios: $(grep '"@capacitor/ios"' package.json | sed 's/.*: *"\([^"]*\)".*/\1/')"
        echo "   📦 @capacitor/geolocation: $(grep '"@capacitor/geolocation"' package.json | sed 's/.*: *"\([^"]*\)".*/\1/')"
    fi
else
    echo "   ❌ npx not found"
fi

# Check if iOS project exists
echo ""
echo "3️⃣ iOS Project Status:"
if [ -d "ios/App" ]; then
    echo "   ✅ iOS project exists"
    if [ -f "ios/App/App.xcworkspace/contents.xcworkspacedata" ]; then
        echo "   ✅ Xcode workspace found"
    else
        echo "   ⚠️  Xcode workspace not found"
    fi
else
    echo "   ❌ iOS project not found"
fi

echo ""
echo "🚀 Recommended Testing Steps:"
echo ""
echo "1. Clean and rebuild iOS project:"
echo "   npm run build"
echo "   npx cap sync ios"
echo "   npx cap copy ios"
echo ""
echo "2. Open in Xcode:"
echo "   npx cap open ios"
echo ""
echo "3. In Xcode:"
echo "   - Product → Clean Build Folder"
echo "   - Product → Build"
echo "   - Deploy to PHYSICAL device (not simulator)"
echo ""
echo "4. Test permission flow with debug buttons:"
echo "   🛈 (Blue) - Check permission status"
echo "   🔑 (Green) - Request permissions normally"
echo "   ⚡ (Red) - Force permission via getCurrentPosition"
echo "   📍 (Yellow) - Fetch address"
echo ""
echo "🔍 Expected Results:"
echo ""
echo "First time (fresh install):"
echo "   🛈 → 'Location: prompt'"
echo "   🔑 → iOS permission dialog should appear"
echo "   After granting → 'Location: granted'"
echo "   📍 → Address should be fetched"
echo ""
echo "⚠️  If permission dialog doesn't appear:"
echo ""
echo "1. Reset device location permissions:"
echo "   Settings → General → Reset → Reset Location & Privacy"
echo ""
echo "2. Delete app and reinstall from Xcode"
echo ""
echo "3. Try the red flash button (⚡) - this sometimes works when normal request fails"
echo ""
echo "4. Check device settings:"
echo "   Settings → Privacy & Security → Location Services"
echo "   Make sure Location Services is ON"
echo ""
echo "5. Check Xcode console for error messages during permission request"
echo ""
echo "📋 Debug Information to Collect:"
echo "- iOS version on test device"
echo "- Xcode version"
echo "- Console logs when tapping permission buttons"
echo "- Whether testing on simulator or physical device"
echo "- Any error messages in Xcode console"
echo ""
echo "🆘 If still not working:"
echo "- Try creating a minimal test app with just location permissions"
echo "- Check Apple Developer Forums for recent iOS permission issues"
echo "- Consider updating to latest Capacitor version if available"
