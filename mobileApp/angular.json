{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"app": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "www", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["src/global.scss", "src/theme/variables.scss"], "scripts": [], "allowedCommonJsDependencies": ["localforage"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "20mb", "maximumError": "50mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "50kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "ci": {"progress": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "app:build:production"}, "development": {"buildTarget": "app:build:development"}, "ci": {"progress": false}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["src/global.scss", "src/theme/variables.scss"], "scripts": []}, "configurations": {"ci": {"progress": false, "watch": false}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@ionic/angular-toolkit"], "analytics": "04692041-1c08-4b8e-95f1-ca6376a3ec7e"}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}}