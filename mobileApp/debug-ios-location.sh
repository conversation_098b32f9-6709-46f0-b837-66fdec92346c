#!/bin/bash

echo "🔍 iOS Location Permission Debug Script"
echo "========================================"

# Check if we're in the right directory
if [ ! -f "capacitor.config.ts" ]; then
    echo "❌ Error: Please run this script from the mobileApp directory"
    exit 1
fi

echo ""
echo "📋 Checking iOS Configuration Files..."
echo ""

# Check Info.plist
echo "1️⃣ Checking Info.plist location permissions:"
if grep -q "NSLocationWhenInUseUsageDescription" ios/App/App/Info.plist; then
    echo "   ✅ NSLocationWhenInUseUsageDescription: Found"
else
    echo "   ❌ NSLocationWhenInUseUsageDescription: Missing"
fi

if grep -q "NSLocationAlwaysAndWhenInUseUsageDescription" ios/App/App/Info.plist; then
    echo "   ✅ NSLocationAlwaysAndWhenInUseUsageDescription: Found"
else
    echo "   ❌ NSLocationAlwaysAndWhenInUseUsageDescription: Missing"
fi

if grep -q "NSLocationUsageDescription" ios/App/App/Info.plist; then
    echo "   ✅ NSLocationUsageDescription: Found"
else
    echo "   ❌ NSLocationUsageDescription: Missing"
fi

echo ""
echo "2️⃣ Checking Capacitor configuration:"
if grep -q "Geolocation" capacitor.config.ts; then
    echo "   ✅ Geolocation plugin: Configured"
else
    echo "   ❌ Geolocation plugin: Not configured"
fi

echo ""
echo "3️⃣ Checking package dependencies:"
if grep -q "@capacitor/geolocation" package.json; then
    echo "   ✅ @capacitor/geolocation: Installed"
    grep "@capacitor/geolocation" package.json | sed 's/^/   /'
else
    echo "   ❌ @capacitor/geolocation: Not installed"
fi

echo ""
echo "🔧 Recommended Actions:"
echo ""
echo "1. Clean and rebuild iOS project:"
echo "   npm run build"
echo "   npx cap sync ios"
echo "   npx cap copy ios"
echo ""
echo "2. Open in Xcode and clean build:"
echo "   npx cap open ios"
echo "   Product → Clean Build Folder"
echo "   Product → Build"
echo ""
echo "3. Test on physical device (not simulator) for best results"
echo ""
echo "4. Check iOS device settings:"
echo "   Settings → Privacy & Security → Location Services"
echo "   Make sure Location Services is enabled"
echo ""
echo "5. Reset app permissions (if needed):"
echo "   Settings → General → Transfer or Reset iPhone → Reset → Reset Location & Privacy"
echo ""
echo "🧪 Debug Steps in App:"
echo ""
echo "1. Tap blue info button (🛈) to check current permission status"
echo "2. Tap green key button (🔑) to manually request permissions"
echo "3. Tap yellow location button to fetch address"
echo "4. Check browser/Xcode console for detailed logs"
echo ""
echo "📱 Expected Permission Flow:"
echo "1. First tap should show 'prompt' status"
echo "2. Manual request should trigger iOS permission dialog"
echo "3. After granting, status should show 'granted'"
echo "4. Location fetch should work normally"
echo ""
echo "🚨 If permission dialog still doesn't appear:"
echo "1. Check if app was previously denied (reset permissions)"
echo "2. Ensure you're testing on physical device"
echo "3. Verify iOS version compatibility (iOS 11+)"
echo "4. Check Xcode console for Capacitor errors"
