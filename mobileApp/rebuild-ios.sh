#!/bin/bash

# <PERSON><PERSON>t to rebuild iOS app with updated permissions
echo "🔄 Rebuilding iOS app with notification permissions..."

# Build the web assets
echo "📦 Building web assets..."
npm run build

# Sync with Capacitor
echo "🔄 Syncing with Capacitor..."
npx cap sync ios

# Copy updated files
echo "📋 Copying updated files..."
npx cap copy ios

echo "✅ iOS rebuild complete!"
echo ""
echo "📱 Next steps:"
echo "1. Open Xcode: npx cap open ios"
echo "2. Clean build folder (Product > Clean Build Folder)"
echo "3. Build and run on device"
echo "4. The app should now request notification permissions on first launch"
echo ""
echo "🔧 What was fixed:"
echo "- Added NSUserNotificationsUsageDescription to Info.plist"
echo "- Added notification entitlements to App.entitlements"
echo "- Fixed FCM service initialization in app.component.ts"
echo "3. Build and run the app"
echo "4. Test location permissions on device/simulator"
echo ""
echo "🔍 To test location permissions:"
echo "1. Tap the blue info button (🛈) to check permission status"
echo "2. Tap the yellow location button to fetch address"
echo "3. Check console logs for detailed debugging info"
