{"name": "ionic-app-base", "version": "0.0.0", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@capacitor-mlkit/barcode-scanning": "^7.2.1", "@capacitor/android": "7.2.0", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/device": "^7.0.1", "@capacitor/geolocation": "^7.1.2", "@capacitor/ios": "7.2.0", "@capacitor/push-notifications": "^7.0.2", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@capacitor/toast": "^7.0.1", "@capawesome/capacitor-android-edge-to-edge-support": "^7.2.2", "@capawesome/capacitor-badge": "^7.0.1", "@ionic/angular": "^8.0.0", "@ionic/storage-angular": "^4.0.0", "@jcesarmobile/ssl-skip": "0.4.0", "@maskito/angular": "^3.8.0", "@maskito/core": "^3.8.0", "@maskito/kit": "^3.8.0", "@maskito/phone": "^3.8.0", "@pantrist/capacitor-date-picker": "^7.0.0", "angular-feather": "^6.5.1", "capacitor-native-settings": "^7.0.2", "ionicons": "^7.0.0", "libphonenumber-js": "^1.12.8", "ngx-image-cropper": "^7.0.2", "ngx-mask": "^19.0.7", "rxjs": "~7.8.0", "signature_pad": "^5.0.9", "track-my-location": "file:track-my-location", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.0", "@angular-eslint/builder": "^19.0.0", "@angular-eslint/eslint-plugin": "^19.0.0", "@angular-eslint/eslint-plugin-template": "^19.0.0", "@angular-eslint/schematics": "^19.0.0", "@angular-eslint/template-parser": "^19.0.0", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@angular/language-service": "^19.0.0", "@ionic/angular-toolkit": "^12.0.0", "@types/google.maps": "^3.58.1", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.6.3"}}