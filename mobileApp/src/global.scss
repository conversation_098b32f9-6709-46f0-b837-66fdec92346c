/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;900&&family=Oswald:wght@200..700&family=Pacifico&display=swap");
@import "theme/variables.scss";
@import "theme/common.scss";
@import "theme/driver-dashboard.scss";
@import "theme/driver-register.scss";

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
/* @import "@ionic/angular/css/palettes/dark.class.css"; */

.loading-container {
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.2);
    position: fixed;
    z-index: 999999999;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Loader css */
.loader {
    width: fit-content;
    height: fit-content;
    display: flex;
    align-items: center;
    justify-content: center;
}

.truckWrapper {
    width: 200px;
    height: 100px;
    display: flex;
    align-items: center;
    flex-direction: column;
    position: relative;
    justify-content: flex-end;
    overflow-x: hidden;
}

/* truck upper body */
.truckBody {
    width: 130px;
    height: fit-content;
    margin-bottom: 6px;
    animation: motion 1s linear infinite;
}

/* truck suspension animation*/
@keyframes motion {
    0% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(3px);
    }

    100% {
        transform: translateY(0px);
    }
}

/* truck's tires */
.truckTires {
    width: 130px;
    height: fit-content;
    display: flex;
    justify-content: space-between;
    padding: 0px 10px 0px 15px;
    position: absolute;
    bottom: 0;
}

.truckTires svg {
    width: 24px;
}

.road {
    width: 100%;
    height: 1.5px;
    background-color: #282828;
    position: relative;
    bottom: 0;
    align-self: flex-end;
    border-radius: 3px;
}

.road::before {
    content: '';
    position: absolute;
    width: 20px;
    height: 100%;
    background-color: #282828;
    right: -50%;
    border-radius: 3px;
    animation: roadAnimation 1.4s linear infinite;
    border-left: 10px solid white;
}

.road::after {
    content: '';
    position: absolute;
    width: 10px;
    height: 100%;
    background-color: #282828;
    right: -65%;
    border-radius: 3px;
    animation: roadAnimation 1.4s linear infinite;
    border-left: 4px solid white;
}

.lampPost {
    position: absolute;
    bottom: 0;
    right: -90%;
    height: 90px;
    animation: roadAnimation 1.4s linear infinite;
}

@keyframes roadAnimation {
    0% {
        transform: translateX(0px);
    }

    100% {
        transform: translateX(-350px);
    }
}


/**
 * Mobile App Specific Styles
 * -----------------------------------------------------
 * Ensures proper mobile behavior and keyboard handling
 */

// Ensure proper mobile viewport
html,
body {
    height: 100vh;
    width: 100%;
    -webkit-overflow-scrolling: touch;
}

// Ensure ion-app takes full height
ion-app {
    height: 100vh;
}

// Fix for iOS keyboard issues
ion-content {
    --keyboard-offset: 0px;
}

// Prevent zoom on input focus (iOS)
input,
textarea,
select {
    font-size: 16px !important;
    -webkit-user-select: text;
    user-select: text;
}

// Smooth scrolling for mobile
* {
    -webkit-overflow-scrolling: touch;
}

// Fix for safe area on newer devices
.safe-area-top {
    padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
}

// Ensure proper touch behavior
.ion-page {
    touch-action: manipulation;
}

// Fix for keyboard overlay issues
ion-modal,
ion-popover {
    --backdrop-opacity: 0.4;
}

/**
 * Enhanced Ripple Effects
 * -----------------------------------------------------
 * Improved ripple effects for better user interaction
 */

// Enhanced ripple effect for better visibility
ion-ripple-effect {
    --ripple-color: transparent;
    opacity: 0;
    transition: none;
}

// Interactive elements with ripple effects
.interactive-element,
.add-shipping-container,
.shipping-tab-item,
.action-button,
.action-icons ion-icon {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;

    // Ensure ripple effect is visible
    ion-ripple-effect {
        z-index: 1;
        pointer-events: none;
    }
}

// Enhanced touch feedback
.interactive-element:active,
.add-shipping-container:active,
.shipping-tab-item:active,
.action-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

// Smooth transitions for all interactive elements
.interactive-element,
.add-shipping-container,
.shipping-tab-item,
.action-button,
.action-icons ion-icon {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

// Custom ripple colors for different elements - DISABLED to prevent black flash
.primary-ripple ion-ripple-effect {
    --ripple-color: transparent;
}

.secondary-ripple ion-ripple-effect {
    --ripple-color: transparent;
}

.dark-ripple ion-ripple-effect {
    --ripple-color: transparent;
}