import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AlertController, NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-client-quotation-cargo-list',
  templateUrl: './client-quotation-cargo-list.component.html',
  styleUrls: ['./client-quotation-cargo-list.component.scss'],
  standalone: false
})
export class ClientQuotationCargoListComponent implements OnInit {

  filter: any;
  currentPage = 4;
  quotationData: any;
  quotationCargoList: Array<any> = new Array<any>();
  searchQuery: string = '';  // Variable to hold the search query
  filteredCargoList: any[] = [];
  quotationDetails: any;
  user: any;
  hasQuotationDetailResponse: boolean = false;

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    public commonService: CommonService,
    private readonly route: ActivatedRoute,
    private readonly authService: AuthService,
    private alertController: AlertController
  ) {

  }

  ngOnInit() { }

  ionViewWillEnter() {
    this.quotationCargoList = [];
    this.filteredCargoList = [];
    this.searchQuery = '';

    this.user = this.authService.getUser();

    this.filter = {} as any;
    this.filter.offset = 1;
    this.filter.tabType = 'PENDING'; // default is PENDING

    this.route.queryParams.subscribe(params => {
      this.quotationData = params['quotationData'] || null;
    });

    this.getCargoList();
    this.getQuotationById();
  }

  ionViewDidLeave() {
    this.quotationCargoList = [];
    this.filteredCargoList = [];
    this.searchQuery = '';
  }

  getCargoList() {
    if (!this.quotationData) {
      this.toastService.show('Quotation data not found');
      return;
    }

    const payload = {
      filtering: {
        quotationId: this.quotationData
      }
    };

    this.loadingService.show();
    this.dataService.getClientQuotationCargoList(payload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const data = response.data || [];
        this.quotationCargoList = data;
        this.filteredCargoList = [...this.quotationCargoList];
      },
      error: (error) => {
        this.loadingService.hide();
        this.quotationCargoList = [];
        this.filteredCargoList = [];
        this.toastService.show(error.message || 'An error occurred while loading cargo list');
      }
    });
  }

  getQuotationById() {
    this.hasQuotationDetailResponse = false;
    this.loadingService.show();
    this.dataService.getQuotationById(this.quotationData).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.hasQuotationDetailResponse = true;

        const data = response.data;
        this.quotationDetails = data;
        this.localStorageService.setObject("OTHER_QUOTATION_DETAILS", data);
      },
      error: (error) => {
        this.hasQuotationDetailResponse = false;
        this.loadingService.hide();
        this.toastService.show(error.message);
      }
    });
  }

  onChangeStatusTab(status: string) {
    this.filter.tabType = status;
  }

  setPage(page: number) {
    this.currentPage = page;
  }

  goToPreviousPage() {
    if (this.currentPage > 1) {
      this.setPage(this.currentPage - 1);
    }
  }

  goToNextPage() {
    if (this.currentPage < 7) {
      this.setPage(this.currentPage + 1);
    }
  }

  searchCargoList() {
    this.filterCargoList();
  }

  filterCargoList() {
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      this.filteredCargoList = this.quotationCargoList.filter(cargo =>
        cargo.cargoType?.toLowerCase().includes(query) ||
        cargo.description?.toLowerCase().includes(query) ||
        cargo.rateType?.toLowerCase().includes(query) ||
        cargo.weightType?.toLowerCase().includes(query)
      );
    } else {
      this.filteredCargoList = [...this.quotationCargoList];
    }
  }

  add() {
    this.localStorageService.remove("QUOTATION_CARGO_DETAILS");
    this.localStorageService.remove("QUOTATION_CARGO_MODE");
    this.navController.navigateForward('/client/portal/quot/add/cargo/details', {
      queryParams: {
        quotationData: this.quotationData
      }, animated: true
    });
  }

  editCargoItem(item: any) {
    this.localStorageService.setObject("QUOTATION_CARGO_DETAILS", item);
    this.localStorageService.set("QUOTATION_CARGO_MODE", "edit");
    this.navController.navigateForward('/client/portal/quot/add/cargo/details', {
      queryParams: {
        quotationData: this.quotationData
      }, animated: true
    });
  }

  specialRequest() {
    if (this.quotationCargoList.length <= 0) {
      this.toastService.show("No cargo found. Please add a new cargo to continue.")
      return;
    }
    this.navController.navigateForward('/client/portal/quot/special/request', {
      queryParams: {
        quotationData: this.quotationData
      }, animated: true
    });
  }

  async deleteCargoItem(item: any) {
    const alert = await this.alertController.create({
      header: 'Confirm Delete',
      message: `Are you sure you want to delete this cargo?`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          role: 'destructive',
          handler: () => {
            this.performDelete(item.id);
          }
        }
      ]
    });
    await alert.present();
  }

  async performDelete(cargoId: string) {
    this.loadingService.show();
    this.dataService.deleteQuotationCargoItem(cargoId).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        this.filteredCargoList = this.filteredCargoList.filter(r => r.id !== cargoId);
        this.toastService.show('Cargo deleted successfully!');
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

}
