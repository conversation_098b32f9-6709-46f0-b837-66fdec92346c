 .client-quotation-cargo-page {
     --background: white;
     height: 100%;

     .quotation-detail-container {
         display: flex;
         flex-direction: column;
         justify-content: space-between;
         width: 100%;
         gap: 5px;
         padding: 10px 20px 0px 20px !important;

         .quotation-detail {
             display: flex;
             justify-content: space-between;
         }

         .quotation-label {
             font-size: 13px;
         }

         .quotation-value {
             font-size: 14px;
             font-weight: 600;
         }
     }

     .add-quotation-container {
         display: flex;
         justify-content: space-between;
         align-items: center;
         background: #fcd616;
         color: black;
         border-radius: 18px;
         padding: 14px 17px;
         gap: 5px;

         .add-quotation-icon {
             height: 22px;
             width: 32px;
         }

         .add-quotation-text {
             font-size: 15px;
             font-weight: 600;
         }
     }

     .client-quotation-cargo-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 355px);
         padding: 10px 20px 70px 20px !important;
         overflow-y: auto;

         .not-found-container {
             display: flex;
             justify-content: center;
             align-items: center;
             margin-top: 60px;
             gap: 10px;
             background: #FFEA00;
             border-radius: 23px;
             padding: 35px;
             justify-content: space-between;
             flex-direction: column;
             gap: 15px;

             .not-found-icon {
                 font-size: 40px;
             }

             .not-found-text {
                 font-size: 16px;
                 font-weight: 500;
             }
         }

         .quotation-list-wrapper {
             .quotation-card {
                 border-radius: 18px;
                 padding: 20px 20px;
                 margin-bottom: 18px;
                 background-color: #ffffff;
                 box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
                 color: #000;
                 transition: all 0.3s ease;

                 &.active {
                     background-color: #FFEA00;
                 }

                 .quotation-card-inner {
                     display: flex;
                     justify-content: space-between;
                     align-items: flex-start;

                     .left-section {
                         flex: 1;

                         .cargo-info {
                             display: flex;
                             align-items: center;
                             gap: 10px;
                             margin-bottom: 8px;

                             .cargo-icon {
                                 font-size: 22px;
                                 background: yellow;
                                 border-radius: 12px;
                                 padding: 8px;
                             }

                             .label {
                                 font-weight: 700;
                                 font-size: 12px;
                             }

                             .value {
                                 font-size: 11px;
                                 color: #888;
                                 margin-top: 4px;
                             }
                         }

                         .quotation-description {
                             .label {
                                 font-weight: 700;
                                 font-size: 13px;
                             }

                             .value {
                                 font-size: 11px;
                                 color: #888;
                             }
                         }

                         .quotation-details {
                             display: flex;
                             justify-content: space-between;
                             margin-top: 15px;

                             div {
                                 text-align: center;

                                 .label {
                                     font-size: 12px;
                                     color: black;
                                     font-weight: 700;
                                 }

                                 .value {
                                     font-size: 12px;
                                     color: #888;
                                 }
                             }
                         }
                     }

                     .right-section {
                         display: flex;
                         flex-direction: column;
                         align-items: flex-end;
                         justify-content: space-between;
                         min-width: 60px;
                         height: 100%;

                         .quotation-date {
                             font-size: 10px;
                             color: #555;
                             margin-bottom: 8px;
                         }

                         .card-actions {
                             display: flex;
                             gap: 10px;

                             ion-icon {
                                 font-size: 18px;
                                 cursor: pointer;

                                 &.edit-icon {
                                     color: #0044cc;
                                 }

                                 &.delete-icon {
                                     color: red;
                                 }
                             }
                         }
                     }
                 }
             }
         }

         .pagination-wrapper {
             display: flex;
             justify-content: center;
             align-items: center;
             gap: 8px;
             padding: 5px 0;

             .arrow-icon {
                 color: #000;
                 cursor: pointer;
                 border-radius: 50%;
                 width: 20px;
                 height: 20px;
                 display: flex;
                 justify-content: center;
                 align-items: center;
                 display: flex;
                 background: white;
                 padding: 6px;
                 border: 3px solid #f5f5f5;
                 box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);

                 &.arrow-image {
                     width: 38px;
                     height: 38px;
                 }
             }

             .page-number-container {
                 display: flex;
                 background: white;
                 padding: 4px;
                 border-radius: 23px;
                 border: 2px solid #f5f5f5;
                 box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
             }

             .page-number {
                 width: 30px;
                 height: 30px;
                 border-radius: 50%;
                 display: flex;
                 justify-content: center;
                 align-items: center;
                 font-weight: 500;
                 cursor: pointer;
                 color: #000;

                 &.active {
                     background-color: #FFEA00;
                     color: #000;
                     border-radius: 10px;
                     font-weight: bold;
                 }
             }
         }
     }

     .fixed-next-button {
         position: fixed;
         bottom: 2px;
         left: 20px;
         right: 20px;
         z-index: 99;

         .next-button {
             --background: black !important;
             --color: white !important;
             min-height: 54px;
             --border-radius: 18px;
             text-transform: uppercase;
             color: white !important;
             font-weight: 600;
             font-size: 14px;
             letter-spacing: 0.5px;

             // Ensure text and span are visible
             span {
                 color: white !important;
                 font-weight: 600;
                 font-size: 14px;
                 display: block;
                 text-transform: uppercase;
                 letter-spacing: 0.5px;
             }

             ion-label {
                 color: white !important;
             }
         }
     }

 }