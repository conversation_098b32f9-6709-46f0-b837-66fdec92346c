<ion-content class="client-quotation-cargo-page">
  <app-customer-header [innerPage]="true" [headingText]="'Cargo Details'" [rightAction]="false"
    [backUrl]="'/client/portal/quotation'" [hideBellIcon]="true"></app-customer-header>

  <div class="quotation-detail-container margin-top-20" *ngIf="hasQuotationDetailResponse">
    <div class="quotation-detail">
      <span class="quotation-label">Ref Id</span>
      <span class="quotation-value">{{ quotationDetails?.refID }}</span>
    </div>
    <div class="quotation-detail">
      <span class="quotation-label">Customer</span>
      <span class="quotation-value">
        {{ user?.firstName + " " + user?.lastName }}
      </span>
    </div>
    <div class="quotation-detail" *ngIf="user?.phoneNumber">
      <span class="quotation-label">Customer Phone</span>
      <span class="quotation-value">+1 {{ commonService.formatPhoneForDisplay(user?.phoneNumber)
        }}</span>
    </div>
  </div>

  <div class="fixed-search common-page-search">
    <div class="padding-top">
      <ion-item class="site-form-control" lines="none">
        <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
        <ion-input label="Search Here..." labelPlacement="floating" name="searchHere" [(ngModel)]="searchQuery"
          (ionInput)="searchCargoList()" [debounce]="500"></ion-input>
      </ion-item>
    </div>
    <div class="add-quotation-container" (click)="add()">
      <ion-icon class="add-quotation-icon" src="assets/images/svg/black-add-icon.svg" slot="start"></ion-icon>
      <span class="add-quotation-text">ADD</span>
    </div>
  </div>

  <div class="client-quotation-cargo-body-section">

    <div class="not-found-container" *ngIf="filteredCargoList.length <= 0">
      <ion-icon class="not-found-icon" src="/assets/images/svg/cargo-icon.svg"></ion-icon>
      <span class="not-found-text">No Cargo Found</span>
    </div>

    <div class="quotation-list-wrapper">
      <div class="quotation-card" *ngFor="let item of filteredCargoList"
        [ngClass]="{ active: item.status === 'ACTIVE' }">
        <div class="quotation-card-inner">
          <div class="left-section">
            <div class="cargo-info">
              <ion-icon class="cargo-icon" src="/assets/images/svg/cargo-icon.svg"></ion-icon>
              <div>
                <div class="label">{{ commonService.formatTextAllCapital(item.cargoType) }}</div>
                <div class="value">{{ commonService.formatText(item.rateType) }}</div>
              </div>
            </div>

            <div class="quotation-description margin-top-20">
              <div class="label">Description</div>
              <div class="value">{{ item.description }}</div>
            </div>

            <div class="quotation-details">
              <div>
                <div class="label">Volume</div>
                <div class="value">{{ item.volume || 0 }}</div>
              </div>
              <div>
                <div class="label">Weight (IN LBS)</div>
                <div class="value">{{ item.weight }}</div>
              </div>
              <div>
                <div class="label">Quantity</div>
                <div class="value">{{ item.quantity }}</div>
              </div>
            </div>
          </div>

          <div class="right-section">
            <div class="quotation-date">{{ commonService.formatDisplayDate(item.createdOn) }}</div>
            <div class="card-actions">
              <ion-icon class="edit-icon" src="/assets/images/svg/edit-icon.svg"
                (click)="editCargoItem(item)"></ion-icon>
              <ion-icon *ngIf="item.isDeletionAllowed" class="delete-icon" src="/assets/images/svg/delete-icon.svg"
                (click)="deleteCargoItem(item)"></ion-icon>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>

  <div class="fixed-next-button">
    <ion-button class="site-button next-button" expand="full" shape="round" type="submit" (click)="specialRequest()">
      <span>Next</span>
    </ion-button>
  </div>

</ion-content>