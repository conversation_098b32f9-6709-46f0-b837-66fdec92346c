<ion-content class="client-quotation-pickup-delivery-page">
  <app-customer-header [innerPage]="true" [headingText]="'Pickup & Delivery'" [rightAction]="false"
    [backUrl]="'/client/portal/quot/basic/info'" [hideBellIcon]="true"></app-customer-header>

  <div class="client-quotation-pickup-delivery-body-section">

    <div class="quotation-detail-container margin-top-20">
      <div class="quotation-detail">
        <span class="quotation-label">Ref Id</span>
        <span class="quotation-value">{{ quotationData.refID }}</span>
      </div>
      <div class="quotation-detail">
        <span class="quotation-label">Customer</span>
        <span class="quotation-value">
          {{ user?.firstName + " " + user?.lastName }}
        </span>
      </div>
      <div class="quotation-detail" *ngIf="user?.phoneNumber">
        <span class="quotation-label">Customer Phone</span>
        <span class="quotation-value">+1 {{ commonService.formatPhoneForDisplay(user?.phoneNumber)
          }}</span>
      </div>
    </div>

    <div class="margin-top-20">
      <span class="info-text">Quotation Pickup Location</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #pickupForm="ngForm" novalidate>

        <div class="margin-top-20 margin-bottom-10">
          <ng-container *ngIf="hasPickupDeliveryData">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'is-invalid': companyName.invalid && onClickValidation }">
              <ion-input label="Pickup Company Name" labelPlacement="floating" name="companyName" required
                [(ngModel)]="quotationData.pickupCompanyName" #companyName="ngModel" mode="md">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="companyName" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasPickupDeliveryData">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'is-invalid': contactName.invalid && onClickValidation }">
              <ion-input label="Pickup Contact Name" labelPlacement="floating" name="contactName" required
                [(ngModel)]="quotationData.pickupContactPersonName" #contactName="ngModel" maxlength="100"
                pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="contactName" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control phone-input-container" lines="none"
            [ngClass]="{ 'is-invalid': (userPhone.invalid || phoneInvalid) && onClickValidation }">
            <ion-icon src="/assets/images/svg/canada-flag-icon.svg" slot="start" class="start-icon"></ion-icon>
            <div class="phone-input-wrapper">
              <span class="phone-prefix">+1</span>
              <ion-input inputmode="tel" required name="userPhone" #userPhone="ngModel" [(ngModel)]="displayPickupPhone"
                (ionInput)="formatPhoneNumber($event, userPhone)" placeholder="Phone Number">
              </ion-input>
            </div>
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'"></app-validation-message>
          <!-- Show custom message if phone number is invalid length -->
          <div class="error-message" *ngIf="!userPhone.invalid && phoneInvalid && onClickValidation">
            Please provide a valid contact number.
          </div>
        </div>

        <div class="margin-top-10" (click)="openLocationSelectionPopup()">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': address.invalid && onClickValidation}">
            <ion-input name="address" #address="ngModel" [(ngModel)]="quotationData.pickupAddressDetail.address"
              required="required" mode="md" label="Address" labelPlacement="floating">
            </ion-input>
            <ion-button slot="end" fill="clear" class="fetch-address-btn">
              <ion-icon name="location-outline"></ion-icon>
            </ion-button>
          </ion-item>
          <app-validation-message [field]="address" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ng-container *ngIf="hasPickupDeliveryData">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': (city.invalid || isCityOnlySpaces) && onClickValidation}">
                <ion-input name="city" #city="ngModel" [(ngModel)]="quotationData.pickupAddressDetail.city"
                  required="required" maxlength="100" mode="md" label="City" labelPlacement="floating">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="city" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
              <div class="error-message margin-top-5" *ngIf="!city.invalid && isCityOnlySpaces && onClickValidation">
                City cannot be empty or just spaces.
              </div>
            </ng-container>
          </div>

          <div class="field-container large-field">
            <ng-container *ngIf="hasPickupDeliveryData">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': (province.invalid || isProvinceOnlySpaces) && onClickValidation}">
                <ion-input name="province" #province="ngModel" [(ngModel)]="quotationData.pickupAddressDetail.state"
                  required="required" maxlength="100" mode="md" label="Province" labelPlacement="floating">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="province" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
              <div class="error-message margin-top-5"
                *ngIf="!province.invalid && isProvinceOnlySpaces && onClickValidation">
                Province cannot be empty or just spaces.
              </div>
            </ng-container>
          </div>
        </div>

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ng-container *ngIf="hasPickupDeliveryData">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': (postalCode.invalid || isPostalOnlySpaces) && onClickValidation}">
                <ion-input name="postalCode" #postalCode="ngModel" [(ngModel)]="quotationData.pickupAddressDetail.pin"
                  required="required" mode="md" label="Postal Code" labelPlacement="floating" maxlength="10">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="postalCode" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Please enter a valid postal code.'">
              </app-validation-message>
              <div class="error-message margin-top-5"
                *ngIf="!postalCode.invalid && isPostalOnlySpaces && onClickValidation">
                Postal Code cannot be empty or just spaces.
              </div>
            </ng-container>
          </div>

          <div class="field-container large-field">
            <ng-container *ngIf="hasPickupDeliveryData">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': (country.invalid || isCountryOnlySpaces) && onClickValidation}">
                <ion-input name="country" #country="ngModel" [(ngModel)]="quotationData.pickupAddressDetail.country"
                  required="required" maxlength="100" mode="md" label="Country" labelPlacement="floating">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="country" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
              <div class="error-message margin-top-5"
                *ngIf="!country.invalid && isCountryOnlySpaces && onClickValidation">
                Country cannot be empty or just spaces.
              </div>
            </ng-container>
          </div>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="cancel()">
            <span>Back</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="submitProfile(pickupForm.form)">
            <span>Next</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
        </div>

      </form>
    </div>

  </div>
</ion-content>