import { Component, OnInit } from '@angular/core';
import { NgModel } from '@angular/forms';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { QuotationBasicInfo } from 'src/modals/quotation-info';
import { CommonService } from 'src/services/common.service';
import { GooglePlacesService } from 'src/services/google-places-service';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-client-quotation-pickup-delivery',
  templateUrl: './client-quotation-pickup-delivery.component.html',
  styleUrls: ['./client-quotation-pickup-delivery.component.scss'],
  standalone: false
})
export class ClientQuotationPickupDeliveryComponent implements OnInit {

  onClickValidation!: boolean;
  pickupDetails: any;
  quotationData: QuotationBasicInfo = new QuotationBasicInfo();
  activeSubscriptions: Subscription = new Subscription();
  hasPickupDeliveryData = true;
  displayPickupPhone: string = '';
  isLocationSelectionPopupOpen: boolean = false;
  searchLocation: string | null = null;
  availablePlaces: any[] = [];
  phoneInvalid: boolean = false;
  user: any;

  constructor(
    private readonly navController: NavController,
    public commonService: CommonService,
    private readonly localStorageService: LocalStorageService,
    private readonly toastService: ToastService,
    private googlePlaces: GooglePlacesService,
    public readonly authService: AuthService
  ) {

  }

  ngOnInit(): void {
    this.loadQuotationData();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;
    this.user = this.authService.getUser();

    this.loadQuotationData();
    this.getPickupDetails();
    this.evaluatePickupInfoVisibility();
  }

  private loadQuotationData(): void {
    const quotationData = this.localStorageService.getObject("QUOTATION_INFO");
    this.quotationData = quotationData ? QuotationBasicInfo.fromResponse(quotationData) : new QuotationBasicInfo();

    const mode = this.localStorageService.get("QUOTATION_MODE");
    if (mode === "edit" && this.quotationData.pickupContactPersonPhone) {
      const digits = this.quotationData.pickupContactPersonPhone.replace(/\D/g, '').slice(0, 10);
      this.quotationData.pickupContactPersonPhone = digits;
      this.displayPickupPhone = this.commonService.formatPhoneForDisplay(digits);
    }
  }

  private getPickupDetails(): void {
    this.pickupDetails = this.localStorageService.getObject("QUOTATION_PICKUP_DETAILS");

    if (this.pickupDetails) {
      const customerDetail = this.pickupDetails.customerDetail || {};
      const addressDetail = this.pickupDetails.addressDetail || {};

      // Only prefill if field is empty — don't overwrite existing input
      if (!this.quotationData.pickupCompanyName && customerDetail.companyName) {
        this.quotationData.pickupCompanyName = customerDetail.companyName || '';
      }

      if (!this.quotationData.pickupContactPersonName && customerDetail.keyContact) {
        this.quotationData.pickupContactPersonName = customerDetail.keyContact || '';
      }

      if (!this.quotationData.pickupContactPersonPhone && customerDetail.keyContactPhone) {
        const rawPhone = (customerDetail.keyContactPhone || '').replace(/\D/g, '').slice(0, 10);
        this.quotationData.pickupContactPersonPhone = rawPhone;
        this.displayPickupPhone = this.commonService.formatPhoneForDisplay(rawPhone);
      }

      // For address — only fill if not already set
      if (!this.quotationData.pickupAddressDetail.address) {
        this.quotationData.pickupAddressDetail.address = addressDetail.address || '';
      }
      if (!this.quotationData.pickupAddressDetail.city) {
        this.quotationData.pickupAddressDetail.city = addressDetail.city || '';
      }
      if (!this.quotationData.pickupAddressDetail.state) {
        this.quotationData.pickupAddressDetail.state = addressDetail.state || '';
      }
      if (!this.quotationData.pickupAddressDetail.pin) {
        this.quotationData.pickupAddressDetail.pin = addressDetail.pin || '';
      }
      if (!this.quotationData.pickupAddressDetail.country) {
        this.quotationData.pickupAddressDetail.country = addressDetail.country || '';
      }
      this.quotationData.pickupAddressDetail.latitude = addressDetail.latitude || '';
      this.quotationData.pickupAddressDetail.longitude = addressDetail.longitude || '';
    }
  }

  private evaluatePickupInfoVisibility(): void {
    const info = this.quotationData;

    const missingRequiredFields = !info.pickupCompanyName || !info.pickupContactPersonName;
    if (missingRequiredFields) {
      this.hasPickupDeliveryData = false;
      setTimeout(() => {
        this.hasPickupDeliveryData = true;
      }, 0);
    } else {
      this.hasPickupDeliveryData = true;
    }
  }

  openLocationSelectionPopup() {
    this.isLocationSelectionPopupOpen = true;
  }

  closeLocationSelectionPopup() {
    this.isLocationSelectionPopupOpen = false;
    this.searchLocation = null;
    this.availablePlaces = [];
  }

  async fetchPlaces(): Promise<void> {
    if (!this.searchLocation) {
      this.availablePlaces = [];
      return;
    }
    try {
      this.availablePlaces = await this.googlePlaces.fetchAutocomplete(this.searchLocation.trim());
    } catch (error: any) {
      this.availablePlaces = [];
    }
  }

  async onSelectLocation(place: any): Promise<void> {
    try {
      const result = await this.googlePlaces.fetchPlaceDetails(place.place_id);
      const components = result.address_components;

      const getComponent = (type: string) =>
        components.find((c: any) => c.types.includes(type))?.long_name || '';

      const lat = result.geometry?.location?.lat?.();
      const lng = result.geometry?.location?.lng?.();

      this.quotationData.pickupAddressDetail = {
        address: result.formatted_address,
        city: getComponent('locality') || getComponent('sublocality'),
        state: getComponent('administrative_area_level_1'),
        pin: getComponent('postal_code'),
        country: getComponent('country'),
        latitude: lat?.toString() || '',
        longitude: lng?.toString() || '',
      };
      this.searchLocation = this.quotationData.pickupAddressDetail.address;
      this.availablePlaces = [];
      this.toastService.show('Address fetched successfully!');
      this.closeLocationSelectionPopup();
    } catch (error: any) {
      this.toastService.show(error.message || 'Error fetching place details');
    }
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    const digits = value.replace(/\D/g, '').slice(0, 10);

    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.displayPickupPhone = formatted;
    input.value = formatted;

    this.quotationData.pickupContactPersonPhone = digits;

    // Only valid if exactly 10 digits
    this.phoneInvalid = digits.length !== 10;

    if (this.phoneInvalid) {
      userPhone.control.setErrors({ invalid: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  get isCityOnlySpaces(): boolean {
    const value = this.quotationData.pickupAddressDetail.city;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isProvinceOnlySpaces(): boolean {
    const value = this.quotationData.pickupAddressDetail.state;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isPostalOnlySpaces(): boolean {
    const value = this.quotationData.pickupAddressDetail.pin;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isCountryOnlySpaces(): boolean {
    const value = this.quotationData.pickupAddressDetail.country;
    return typeof value === 'string' && value.trim().length === 0;
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid || this.phoneInvalid) {
      return;
    }

    this.quotationData.pickupAddressDetail.city = this.quotationData.pickupAddressDetail.city?.trim() || null;
    this.quotationData.pickupAddressDetail.state = this.quotationData.pickupAddressDetail.state?.trim() || null;
    this.quotationData.pickupAddressDetail.pin = this.quotationData.pickupAddressDetail.pin?.trim() || null;
    this.quotationData.pickupAddressDetail.country = this.quotationData.pickupAddressDetail.country?.trim() || null;

    this.localStorageService.setObject("QUOTATION_INFO", this.quotationData);
    this.navController.navigateForward("/client/portal/quot/delivery/location", { animated: true });
  }

  cancel() {
    this.navController.navigateBack("/client/portal/quot/basic/info", { animated: true });
  }

}
