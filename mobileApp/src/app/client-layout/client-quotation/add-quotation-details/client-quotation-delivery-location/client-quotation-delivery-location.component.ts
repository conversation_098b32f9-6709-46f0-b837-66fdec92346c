import { Component, OnInit } from '@angular/core';
import { LoadingController, NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { Geolocation } from '@capacitor/geolocation';
import { ToastService } from 'src/shared/toast.service';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { NgModel } from '@angular/forms';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { CommonService } from 'src/services/common.service';
import { AuthService } from 'src/shared/authservice';
import { QuotationBasicInfo } from 'src/modals/quotation-info';
import { GooglePlacesService } from 'src/services/google-places-service';

@Component({
  selector: 'app-client-quotation-delivery-location',
  templateUrl: './client-quotation-delivery-location.component.html',
  styleUrls: ['./client-quotation-delivery-location.component.scss'],
  standalone: false
})
export class ClientQuotationDeliveryLocationComponent implements OnInit {

  onClickValidation!: boolean;
  isNoAddressFoundPopupOpen: boolean = false;
  quotationData: QuotationBasicInfo = new QuotationBasicInfo();
  activeSubscriptions: Subscription = new Subscription();
  hasDeliveryData = true;
  displayDeliveryPhone: string = '';
  isLocationSelectionPopupOpen: boolean = false;
  searchLocation: string | null = null;
  availablePlaces: any[] = [];
  phoneInvalid: boolean = false;
  user: any;

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly loadingController: LoadingController,
    public commonService: CommonService,
    private readonly authService: AuthService,
    private googlePlaces: GooglePlacesService,
  ) {

  }

  ngOnInit(): void {
    this.loadQuotationData();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;
    this.searchLocation = null;
    this.user = this.authService.getUser();

    this.loadQuotationData();
    this.evaluateDeliveryInfoVisibility();

    // Only auto-fetch if address is empty
    // if (!this.quotationData.deliveryAddressDetail.address) {
    //   this.fetchAddressFromCurrentLocation();
    // }
  }

  private loadQuotationData(): void {
    const quotationData = this.localStorageService.getObject("QUOTATION_INFO");
    this.quotationData = quotationData ? QuotationBasicInfo.fromResponse(quotationData) : new QuotationBasicInfo();

    const mode = this.localStorageService.get("QUOTATION_MODE");
    if (mode === "edit" && this.quotationData.deliveryContactPersonPhone) {
      const digits = this.quotationData.deliveryContactPersonPhone.replace(/\D/g, '').slice(0, 10);
      this.quotationData.deliveryContactPersonPhone = digits;
      this.displayDeliveryPhone = this.commonService.formatPhoneForDisplay(digits);
    }
  }

  private evaluateDeliveryInfoVisibility(): void {
    const info = this.quotationData;

    const missingRequiredFields = !info.deliveryCompanyName || !info.deliveryContactPersonName;
    if (missingRequiredFields) {
      this.hasDeliveryData = false;
      setTimeout(() => {
        this.hasDeliveryData = true;
      }, 0);
    } else {
      this.hasDeliveryData = true;
    }
  }

  openNoAddressFoundPopup() {
    this.isNoAddressFoundPopupOpen = true;
  }

  closeNoAddressFoundPopup() {
    this.isNoAddressFoundPopupOpen = false;
  }

  openLocationSelectionPopup() {
    this.isLocationSelectionPopupOpen = true;
  }

  closeLocationSelectionPopup() {
    this.isLocationSelectionPopupOpen = false;
    this.searchLocation = null;
    this.availablePlaces = [];
  }

  async fetchPlaces(): Promise<void> {
    if (!this.searchLocation) {
      this.availablePlaces = [];
      return;
    }
    try {
      this.availablePlaces = await this.googlePlaces.fetchAutocomplete(this.searchLocation.trim());
    } catch (error: any) {
      this.availablePlaces = [];
      //  this.toastService.show(error.message || 'Error fetching place suggestions');
    }
  }

  async onSelectLocation(place: any): Promise<void> {
    try {
      const result = await this.googlePlaces.fetchPlaceDetails(place.place_id);
      const components = result.address_components;

      const getComponent = (type: string) =>
        components.find((c: any) => c.types.includes(type))?.long_name || '';

      const lat = result.geometry?.location?.lat?.();
      const lng = result.geometry?.location?.lng?.();

      this.quotationData.deliveryAddressDetail = {
        address: result.formatted_address,
        city: getComponent('locality') || getComponent('sublocality'),
        state: getComponent('administrative_area_level_1'),
        pin: getComponent('postal_code'),
        country: getComponent('country'),
        latitude: lat?.toString() || '',
        longitude: lng?.toString() || '',
      };
      this.searchLocation = this.quotationData.deliveryAddressDetail.address;
      this.availablePlaces = [];
      this.toastService.show('Address fetched successfully!');
      this.closeLocationSelectionPopup();
    } catch (error: any) {
      this.toastService.show(error.message || 'Error fetching place details');
    }
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    const digits = value.replace(/\D/g, '').slice(0, 10);

    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.displayDeliveryPhone = formatted;
    input.value = formatted;

    this.quotationData.deliveryContactPersonPhone = digits;

    // Only valid if exactly 10 digits
    this.phoneInvalid = digits.length !== 10;

    if (this.phoneInvalid) {
      userPhone.control.setErrors({ invalid: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  retry() {
    this.fetchAddressFromCurrentLocation();
    this.closeNoAddressFoundPopup();
  }

  async fetchAddressFromCurrentLocation() {
    let activeLoading = await this.loadingController.create({
      message: 'Fetching address...',
      spinner: 'circles',
      cssClass: 'custom-loading-popup',
      backdropDismiss: false
    });

    await activeLoading.present();

    const dismissLoading = async () => {
      try {
        await activeLoading?.dismiss();
      } catch (e) {
        // Loading already dismissed
      }
    };

    try {
      const permissions = await Geolocation.checkPermissions();

      const locationPermissionGranted = permissions.location === 'granted';

      if (!locationPermissionGranted) {
        await dismissLoading();

        this.toastService.show('Please allow location access when prompted');
        await new Promise(resolve => setTimeout(resolve, 1000));

        const requestResult = await Geolocation.requestPermissions();

        if (requestResult.location !== 'granted') {
          this.toastService.show(
            requestResult.location === 'denied'
              ? 'Location permission denied. Please enable it in Settings > Privacy > Location Services.'
              : 'Location permission is required to fetch address'
          );
          return;
        }

        // Re-create loading
        activeLoading = await this.loadingController.create({
          message: 'Fetching address...',
          spinner: 'circles',
          cssClass: 'custom-loading-popup',
          backdropDismiss: false
        });
        await activeLoading.present();
      }

      // Fetch current location
      const position = await Geolocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 15000
      });

      const lat = position.coords.latitude;
      const lng = position.coords.longitude;

      // Get address from lat/lng using reverse geocoding
      const response = await this.getAddressFromCoords(lat, lng);

      if (response?.results?.length > 0) {
        const addressComponents = response.results[0].address_components;
        const getComponent = (type: string) =>
          addressComponents.find((c: { types: string[] }) => c.types.includes(type))?.long_name || '';

        const fullAddress = response.results[0].formatted_address;

        this.quotationData.deliveryAddressDetail = {
          address: fullAddress,
          city: getComponent('locality'),
          state: getComponent('administrative_area_level_1'),
          pin: getComponent('postal_code'),
          country: getComponent('country'),
          latitude: lat.toString(),
          longitude: lng.toString()
        };

        this.toastService.show('Address fetched successfully!');
      } else {
        this.toastService.show('Address not found for current location');
      }

    } catch (error: any) {

      const message = error?.message?.toLowerCase() || '';

      if (message.includes('permission')) {
        this.toastService.show('Location permission is required to fetch address');
      } else if (message.includes('timeout')) {
        this.toastService.show('Location request timed out. Please try again.');
        this.openNoAddressFoundPopup();
      } else if (message.includes('unavailable') || message.includes('position_unavailable')) {
        this.toastService.show('Location is currently unavailable. Please try again.');
        this.openNoAddressFoundPopup();
      } else {
        this.openNoAddressFoundPopup();
      }
    } finally {
      await dismissLoading();
    }
  }
  async getAddressFromCoords(lat: number, lng: number): Promise<any> {
    const apiKey = 'AIzaSyDjwD-3pwe36dk5kv_dkEzsYGirWZPWCiY';
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`;

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      if (data.status === 'OK') {
        return data;
      } else {
        throw new Error(`Geocoding failed: ${data.status}`);
      }
    } catch (e) {
      throw e;
    }
  }

  get isCityOnlySpaces(): boolean {
    const value = this.quotationData.deliveryAddressDetail.city;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isProvinceOnlySpaces(): boolean {
    const value = this.quotationData.deliveryAddressDetail.state;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isPostalOnlySpaces(): boolean {
    const value = this.quotationData.deliveryAddressDetail.pin;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isCountryOnlySpaces(): boolean {
    const value = this.quotationData.deliveryAddressDetail.country;
    return typeof value === 'string' && value.trim().length === 0;
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }
    if (this.quotationData.contactPersonPhone) {
      const phone = this.quotationData.contactPersonPhone.trim();
      this.quotationData.contactPersonPhone = this.commonService.formatPhoneNumberForApi(phone);
    }
    if (this.quotationData.pickupContactPersonPhone) {
      const phone = this.quotationData.pickupContactPersonPhone.trim();
      this.quotationData.pickupContactPersonPhone = this.commonService.formatPhoneNumberForApi(phone);
    }
    if (this.quotationData.deliveryContactPersonPhone) {
      const phone = this.quotationData.deliveryContactPersonPhone.trim();
      this.quotationData.deliveryContactPersonPhone = this.commonService.formatPhoneNumberForApi(phone);
    }
    this.quotationData.deliveryAddressDetail.city = this.quotationData.deliveryAddressDetail.city?.trim() || null;
    this.quotationData.deliveryAddressDetail.state = this.quotationData.deliveryAddressDetail.state?.trim() || null;
    this.quotationData.deliveryAddressDetail.pin = this.quotationData.deliveryAddressDetail.pin?.trim() || null;
    this.quotationData.deliveryAddressDetail.country = this.quotationData.deliveryAddressDetail.country?.trim() || null;

    this.loadingService.show();

    const mode = this.localStorageService.get("QUOTATION_MODE");
    const apiCall = (mode === "edit")
      ? this.dataService.updateClientQuotation(this.quotationData)
      : this.dataService.saveClientQuotation(this.quotationData);

    apiCall.subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.toastService.show(response.message);

        const data = response.data;
        this.redirectToCargoList(data);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  redirectToCargoList(data: any) {
    this.localStorageService.remove("QUOTATION_INFO");
    this.navController.navigateForward('/client/portal/quot/cargo/listing', {
      queryParams: {
        quotationData: data
      }, animated: true
    });
  }

  cancel() {
    this.navController.navigateBack("/client/portal/quot/pickup/delivery", { animated: true });
  }

}
