import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { QuotationCargoDetails } from 'src/modals/quotation-cargo-detail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-client-quotation-add-cargo',
  templateUrl: './client-quotation-add-cargo.component.html',
  styleUrls: ['./client-quotation-add-cargo.component.scss'],
  standalone: false
})
export class ClientQuotationAddCargoComponent implements OnInit {

  onClickValidation!: boolean;
  addCargoDetails: QuotationCargoDetails = new QuotationCargoDetails();
  activeSubscriptions: Subscription = new Subscription();
  cargoTypes: Array<{ display: string, value: string }> = [
    { display: 'Box', value: 'BOX' },
    { display: 'Pallet', value: 'PALLET' },
    { display: 'Crate', value: 'CRATE' },
    { display: 'Drum', value: 'DRUM' },
    { display: 'Bag', value: 'BAG' },
    { display: 'Sack', value: 'SACK' },
    { display: 'Roll', value: 'ROLL' },
    { display: 'Bundle', value: 'BUNDLE' },
    { display: 'Case', value: 'CASE' },
    { display: 'Bin', value: 'BIN' },
    { display: 'Half Skid', value: 'HALF_SKID' },
    { display: 'Full Skid', value: 'FULL_SKID' },
    { display: 'Container', value: 'CONTAINER' },
    { display: 'Cage', value: 'CAGE' },
    { display: 'Tray', value: 'TRAY' },
    { display: 'Cart', value: 'CART' },
  ];
  weightTypes: Array<{ display: string, value: string }> = [
    { display: 'Lbs', value: 'LBS' },
    { display: 'Kg', value: 'KGS' }
  ];
  rateTypes: Array<{ display: string, value: string }> = [
    { display: 'Weight', value: 'WEIGHT' },
    { display: 'Volume', value: 'VOLUME' },
    { display: 'Full Skid', value: 'FULL_SKID' },
    { display: 'Half Skid', value: 'HALF_SKID' }
  ];
  quotationData: any;
  backUrl!: string;
  hasCargoDetails = true;
  quotationDetails: any;
  user: any;

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    public commonService: CommonService,
    private readonly route: ActivatedRoute,
    public readonly authService: AuthService
  ) {

  }

  ngOnInit(): void {
    const storedData = this.localStorageService.getObject("QUOTATION_CARGO_DETAILS");
    this.addCargoDetails = storedData ? QuotationCargoDetails.fromResponse(storedData) : new QuotationCargoDetails();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;
    this.user = this.authService.getUser();

    this.quotationDetails = this.localStorageService.getObject("OTHER_QUOTATION_DETAILS");

    const storedData = this.localStorageService.getObject("QUOTATION_CARGO_DETAILS");
    this.addCargoDetails = storedData ? QuotationCargoDetails.fromResponse(storedData) : new QuotationCargoDetails();

    this.route.queryParams.subscribe(params => {
      this.quotationData = params['quotationData'] || null;
    });

    this.backUrl = `/client/portal/quot/cargo/listing?quotationData=${this.quotationData}`;

    this.addCargoDetails.quotation = this.quotationData;
    this.addCargoDetails.quantity = 1;
    this.addCargoDetails.rateType = 'WEIGHT';

    const mode = this.localStorageService.get("QUOTATION_CARGO_MODE");
    if (mode === "edit") {
      this.addCargoDetails.id = this.addCargoDetails.id;
    }

    this.evaluateCargoDetailsVisibility();
  }

  private evaluateCargoDetailsVisibility(): void {
    const d = this.addCargoDetails;
    if (!d.description || !d.cargoType || !d.weightType || !d.rateType || d.quantity === null) {
      this.hasCargoDetails = false;
      setTimeout(() => {
        this.hasCargoDetails = true;
      }, 0);
    } else {
      this.hasCargoDetails = true;
    }
  }

  onWeightChange(): void {
    const weight = this.addCargoDetails.weight ?? 0;

    const type = this.addCargoDetails.weightType?.toUpperCase();
    if (type === 'LBS') {
      this.addCargoDetails.weightInPounds = weight;
    } else {
      this.addCargoDetails.weightInPounds = +(weight * 2.20462).toFixed(2);
    }
  }

  calculateVolume(): void {
    const { length, width, height } = this.addCargoDetails;

    if (length && width && height) {
      this.addCargoDetails.volume = +(length * width * height).toFixed(2);
    } else {
      this.addCargoDetails.volume = null;
    }
  }

  incrementQuantity() {
    this.addCargoDetails.quantity = (this.addCargoDetails.quantity || 1) + 1;
  }

  decrementQuantity() {
    if (this.addCargoDetails.quantity !== null && this.addCargoDetails.quantity > 1) {
      this.addCargoDetails.quantity -= 1;
    }
  }

  get isDescriptionOnlySpaces(): boolean {
    const desc = this.addCargoDetails.description;
    return typeof desc === 'string' && desc.trim().length === 0;
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }

    this.addCargoDetails.description = this.addCargoDetails.description?.trim() || '';

    this.loadingService.show();
    const mode = this.localStorageService.get("QUOTATION_CARGO_MODE");
    const apiCall = (mode === "edit")
      ? this.dataService.updateClientQuotationCargoItem(this.addCargoDetails)
      : this.dataService.saveClientQuotationCargoItem(this.addCargoDetails);

    apiCall.subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.toastService.show(response.message);

        const data = response.data;
        this.redirectToCargoList(data);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  redirectToCargoList(data: any) {
    this.localStorageService.remove("QUOTATION_CARGO_DETAILS");
    this.localStorageService.remove("QUOTATION_CARGO_MODE");
    this.navController.navigateForward('/client/portal/quot/cargo/listing', {
      queryParams: {
        quotationData: this.quotationData
      }, animated: true
    });
  }

  cancel() {
    this.navController.navigateBack('/client/portal/quot/cargo/listing', {
      queryParams: {
        quotationData: this.quotationData
      }, animated: true
    });
  }

}
