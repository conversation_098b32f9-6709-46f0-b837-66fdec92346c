import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { QuotationSpecialRequestStates } from 'src/modals/quotation-cargo-detail';
import { CommonService } from 'src/services/common.service';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';

@Component({
  selector: 'app-client-quotation-calculation',
  templateUrl: './client-quotation-calculation.component.html',
  styleUrls: ['./client-quotation-calculation.component.scss'],
  standalone: false
})
export class ClientQuotationCalculationComponent implements OnInit {

  specialRequestData: QuotationSpecialRequestStates = new QuotationSpecialRequestStates();
  quotationData: any;
  backUrl!: string;
  activeSubscriptions: Subscription = new Subscription();
  calculationDetails: {
    totalItems: number | null;
    totalWeight: number | null;
    totalVolume: number | null;
    grandTotal: number | null;
  } = {
      totalItems: null,
      totalWeight: null,
      totalVolume: null,
      grandTotal: null,
    };
  quotationDetails: any;
  user: any;

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    public commonService: CommonService,
    public readonly authService: AuthService
  ) {

  }

  ngOnInit(): void {
    const shippingData = this.localStorageService.getObject("QUOTATION_SPECIAL_REQUEST");
    this.specialRequestData = shippingData ? QuotationSpecialRequestStates.fromResponse(shippingData) : new QuotationSpecialRequestStates();
  }

  ionViewWillEnter(): void {
    this.user = this.authService.getUser();
    this.quotationDetails = this.localStorageService.getObject("OTHER_QUOTATION_DETAILS");

    const shippingData = this.localStorageService.getObject("QUOTATION_SPECIAL_REQUEST");
    this.specialRequestData = shippingData ? QuotationSpecialRequestStates.fromResponse(shippingData) : new QuotationSpecialRequestStates();

    this.route.queryParams.subscribe(params => {
      this.quotationData = params['quotationData'] || null;
    });

    const storedCalculation = this.localStorageService.getObject("QUOTATION_CALCULATION_DETAILS");
    if (storedCalculation) {
      this.calculationDetails.totalItems = storedCalculation.totalItems ?? null;
      this.calculationDetails.totalWeight = storedCalculation.totalWeight ?? null;
      this.calculationDetails.totalVolume = storedCalculation.totalVolume ?? null;
      this.calculationDetails.grandTotal = storedCalculation.grandTotal ?? null;
    }

    this.backUrl = `/client/portal/quot/special/request?quotationData=${this.quotationData}`;
  }

  nextButton() {
    this.navController.navigateForward('/client/portal/quot/add/documents', {
      queryParams: {
        quotationData: this.quotationData
      }, animated: true
    });
  }

  cancel() {
    this.navController.navigateBack('/client/portal/quot/special/request', {
      queryParams: {
        quotationData: this.quotationData
      }, animated: true
    });
  }

  goToQuotationList() {
    this.navController.navigateRoot("/client/portal/quotation", { animated: true });
  }

}
