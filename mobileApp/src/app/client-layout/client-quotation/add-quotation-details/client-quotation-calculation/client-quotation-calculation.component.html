<ion-content class="client-quotation-calculation-page">
  <app-customer-header [innerPage]="true" [headingText]="'Customer Calculation'" [rightAction]="false"
    [backUrl]="backUrl" [homeAction]="true" (homeActionCallback)="goToQuotationList()"
    [hideBellIcon]="true"></app-customer-header>

  <div class="client-quotation-calculation-body-section">

    <div class="quotation-detail-container margin-top-20">
      <div class="quotation-detail">
        <span class="quotation-label">Ref Id</span>
        <span class="quotation-value">{{ quotationDetails?.refID }}</span>
      </div>
      <div class="quotation-detail">
        <span class="quotation-label">Customer</span>
        <span class="quotation-value">
          {{ user?.firstName + " " + user?.lastName || 'N/A' }}
        </span>
      </div>
      <div class="quotation-detail" *ngIf="user?.phoneNumber">
        <span class="quotation-label">Customer Phone</span>
        <span class="quotation-value">+1 {{ commonService.formatPhoneForDisplay(user?.phoneNumber)
          }}</span>
      </div>
    </div>

    <div class="margin-top-20">
      <span class="info-text">Cargo Details</span>
    </div>

    <div class="cargo-detail-card">
      <div class="cargo-detail-left">
        <div class="detail-row margin-top-15">
          <span class="label">Total Items</span>
          <span class="value">{{ calculationDetails.totalItems }}</span>
        </div>
        <div class="detail-row margin-top-15">
          <span class="label">Total Weight</span>
          <span class="value">{{ calculationDetails.totalWeight }}</span>
        </div>
        <div class="detail-row margin-top-15">
          <span class="label">Total Volume</span>
          <span class="value">{{ calculationDetails.totalVolume }}</span>
        </div>
        <div class="detail-row margin-top-15">
          <span class="label">Grand Total</span>
          <span class="value">{{ calculationDetails.grandTotal }}</span>
        </div>
      </div>
      <div class="cargo-detail-right margin-left-15">
        <img src="assets/images/svg/calculation-icon.svg" alt="Cargo Box">
      </div>
    </div>

    <!-- Buttons -->
    <div class="shippment-btn-container">
      <ion-button class="margin-top-35 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
        type="submit" (click)="cancel()">
        <span>Back</span>
        <ion-ripple-effect></ion-ripple-effect>
      </ion-button>
      <ion-button class="margin-top-35 site-button ship-submit-btn interactive-button" expand="full" shape="round"
        type="submit" (click)="nextButton()">
        <span>Next</span>
        <ion-ripple-effect></ion-ripple-effect>
      </ion-button>
    </div>

  </div>
</ion-content>