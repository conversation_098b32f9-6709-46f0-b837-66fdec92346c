import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { QuotationSpecialRequestStates } from 'src/modals/quotation-cargo-detail';
import { CommonService } from 'src/services/common.service';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-client-quotation-special-request',
  templateUrl: './client-quotation-special-request.component.html',
  styleUrls: ['./client-quotation-special-request.component.scss'],
  standalone: false
})
export class ClientQuotationSpecialRequestComponent implements OnInit {

  onClickValidation!: boolean;
  specialRequestData: QuotationSpecialRequestStates = new QuotationSpecialRequestStates();
  quotationData: any;
  backUrl!: string;
  activeSubscriptions: Subscription = new Subscription();
  showCalculations: any;
  quotationEditMode: any;
  quotationDetails: any;
  user: any;

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    public commonService: CommonService,
    public readonly authService: AuthService
  ) {

  }

  ngOnInit(): void {
    const shippingData = this.localStorageService.getObject("QUOTATION_SPECIAL_REQUEST");
    this.specialRequestData = shippingData ? QuotationSpecialRequestStates.fromResponse(shippingData) : new QuotationSpecialRequestStates();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;
    this.user = this.authService.getUser();

    this.quotationDetails = this.localStorageService.getObject("OTHER_QUOTATION_DETAILS");

    const shippingData = this.localStorageService.getObject("QUOTATION_SPECIAL_REQUEST");
    this.specialRequestData = shippingData ? QuotationSpecialRequestStates.fromResponse(shippingData) : new QuotationSpecialRequestStates();

    this.route.queryParams.subscribe(params => {
      this.quotationData = params['quotationData'] || null;
    });

    this.specialRequestData.id = this.quotationData;

    this.showCalculations = this.localStorageService.getBoolean("QUOTATION_SHOW_CALCULATIONS");
    this.quotationEditMode = this.localStorageService.get("QUOTATION_MODE");

    this.backUrl = `/client/portal/quot/cargo/listing?quotationData=${this.quotationData}`;
  }

  onViewChange(section: 'isOversize' | 'isRushRequest' | 'isEnclosed' | 'isFragile' | 'isPerishable' | 'isDangerousGoods', newValue: 'YES' | 'NO'): void {
    this.specialRequestData[section] = newValue === 'YES';
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }
    this.localStorageService.setObject("QUOTATION_SPECIAL_REQUEST", this.specialRequestData);
    if (this.showCalculations && this.quotationEditMode === "edit") {
      this.navController.navigateForward('/client/portal/quot/calculation', {
        queryParams: {
          quotationData: this.quotationData
        }, animated: true
      });
      return;
    }
    this.navController.navigateForward('/client/portal/quot/add/documents', {
      queryParams: {
        quotationData: this.quotationData
      }, animated: true
    });
  }

  cancel() {
    this.navController.navigateBack('/client/portal/quot/cargo/listing', {
      queryParams: {
        quotationData: this.quotationData
      }, animated: true
    });
  }

  goToQuotationList() {
    this.navController.navigateRoot("/client/portal/quotation", { animated: true });
  }

}
