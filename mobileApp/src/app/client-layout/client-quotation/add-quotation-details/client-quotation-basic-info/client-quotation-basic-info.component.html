<ion-content class="quotation-basic-info-page">
  <app-customer-header [innerPage]="true" [headingText]="'Basic information'" [rightAction]="false"
    [backUrl]="'/client/portal/quotation'" [hideBellIcon]="true"></app-customer-header>

  <div class="quotation-basic-info-body-section">
    <div class="margin-top-25">
      <span class="info-text">Enter information</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #quotationBasicForm="ngForm" novalidate>

        <div class="margin-top-20 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{ 'is-invalid': refId.invalid && onClickValidation }">
            <ion-input readonly label="Ref. ID" labelPlacement="floating" name="refId" required
              [(ngModel)]="quotationData.refID" #refId="ngModel" mode="md">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="refId" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasBasicInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': contactPerson.invalid && onClickValidation}">
              <ion-input name="contactPerson" #contactPerson="ngModel" [(ngModel)]="quotationData.contactPersonName"
                maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Contact Person Name"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="contactPerson" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </ng-container>
        </div>

        <!-- Email -->
        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasBasicInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'is-invalid': email.invalid && onClickValidation}">
              <ion-input label="Email" labelPlacement="floating" name="email" type="email"
                pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$" [(ngModel)]="quotationData.contactPersonEmail"
                #email="ngModel">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="email" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Please provide a valid email address.'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasBasicInfo">
            <ion-item class="site-form-control phone-input-container" lines="none"
              [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}">
              <ion-icon src="/assets/images/svg/canada-flag-icon.svg" slot="start" class="start-icon"></ion-icon>
              <div class="phone-input-wrapper">
                <span class="phone-prefix">+1</span>
                <ion-input inputmode="tel" name="userPhone" #userPhone="ngModel" [(ngModel)]="displayPhoneNumber"
                  (ionInput)="formatPhoneNumber($event, userPhone)" placeholder="Phone Number">
                </ion-input>
              </div>
            </ion-item>
            <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Please provide a valid contact number.'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasBasicInfo">
            <ion-item class="site-form-control" lines="none">
              <ion-textarea label="Summary" labelPlacement="floating" name="summary" #summary="ngModel"
                [(ngModel)]="quotationData.summary" maxlength="500">
              </ion-textarea>
            </ion-item>
          </ng-container>
        </div>

        <div class="shippment-btn-container no-margin">
          <ion-button class="margin-top-10 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="cancel()">
            <span>Back</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
          <ion-button class="margin-top-10 site-button ship-submit-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="submitProfile(quotationBasicForm.form)">
            <span>Next</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
        </div>

      </form>
    </div>

  </div>
</ion-content>