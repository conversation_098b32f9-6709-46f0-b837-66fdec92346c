import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { NgModel } from '@angular/forms';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ActivatedRoute } from '@angular/router';
import { AuthService } from 'src/shared/authservice';
import { QuotationBasicInfo } from 'src/modals/quotation-info';
import { RestResponse } from 'src/shared/auth.model';

@Component({
  selector: 'app-client-quotation-basic-info',
  templateUrl: './client-quotation-basic-info.component.html',
  styleUrls: ['./client-quotation-basic-info.component.scss'],
  standalone: false
})
export class ClientQuotationBasicInfoComponent implements OnInit {

  onClickValidation!: boolean;
  refIdData!: string;
  selectedCustomerId!: string;
  quotationData: QuotationBasicInfo = new QuotationBasicInfo();
  activeSubscriptions: Subscription = new Subscription();
  hasBasicInfo = true;
  displayPhoneNumber: string = '';

  constructor(
    private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    public commonService: CommonService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    private readonly authService: AuthService
  ) { }

  ngOnInit(): void {
    const quotationData = this.localStorageService.getObject("QUOTATION_INFO");
    this.quotationData = quotationData ? QuotationBasicInfo.fromResponse(quotationData) : new QuotationBasicInfo();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;

    this.route.queryParams.subscribe(params => {
      const shouldReset = params['reset'];

      if (shouldReset) {
        this.handleReset(params);
      } else {
        this.restoreFromStorage(params);
      }
    });
    this.getPickupLocationDetails();
  }

  private handleReset(params: any): void {
    this.quotationData = new QuotationBasicInfo();
    this.hasBasicInfo = false;
    this.displayPhoneNumber = '';
    this.selectedCustomerId = '';

    this.localStorageService.remove("QUOTATION_INFO");
    this.localStorageService.remove("QUOTATION_MODE");

    this.refIdData = params['refIdData'] || null;
    if (this.refIdData) {
      this.quotationData.refID = this.refIdData;
    }

    // Force UI refresh
    setTimeout(() => {
      this.hasBasicInfo = true;
    }, 0);
  }

  private restoreFromStorage(params: any): void {
    const storedQuotation = this.localStorageService.getObject("QUOTATION_INFO");
    this.quotationData = storedQuotation ? QuotationBasicInfo.fromResponse(storedQuotation) : new QuotationBasicInfo();

    if (this.quotationData.contactPersonPhone && this.quotationData.contactPersonPhone.trim() !== '') {
      const digits = this.quotationData.contactPersonPhone.replace(/\D/g, '').slice(0, 10);
      this.quotationData.contactPersonPhone = digits;
      this.displayPhoneNumber = this.commonService.formatPhoneForDisplay(digits);
    }

    const mode = this.localStorageService.get("QUOTATION_MODE");
    if (mode === "edit") {
      this.quotationData.id = this.quotationData.id;
      this.quotationData.contactPersonPhone = this.commonService.formatPhoneForDisplay(this.quotationData.contactPersonPhone);
    }

    this.refIdData = params['refIdData'] || null;
    if (this.refIdData) {
      this.quotationData.refID = this.refIdData;
    }
  }

  getPickupLocationDetails() {
    this.loadingService.show();
    this.activeSubscriptions.add(
      this.dataService.getPickupLocationDetail().subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data[0];
          this.localStorageService.setObject("QUOTATION_PICKUP_DETAILS", data);
          this.getPickupDetails(data);
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  getPickupDetails(data: any) {
    if (data) {
      const customerDetail = data.customerDetail || {};

      if (!this.quotationData.contactPersonName && customerDetail.keyContact) {
        this.quotationData.contactPersonName = customerDetail.keyContact || null;
      }

      if (!this.quotationData.contactPersonEmail && customerDetail.keyContactEmail) {
        this.quotationData.contactPersonEmail = customerDetail.keyContactEmail || null;
      }

      if (!this.quotationData.contactPersonPhone && customerDetail.keyContactPhone) {
        const rawPhone = (customerDetail.keyContactPhone || null).replace(/\D/g, '').slice(0, 10);
        this.quotationData.contactPersonPhone = rawPhone;
        this.displayPhoneNumber = this.commonService.formatPhoneForDisplay(rawPhone);
      }

    }
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    // Remove non-digits, limit to 10
    const digits = value.replace(/\D/g, '').slice(0, 10);

    // Format for display
    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.displayPhoneNumber = formatted;
    input.value = formatted;

    // Store only digits (no +1, no dashes, no spaces)
    this.quotationData.contactPersonPhone = digits;

    if (digits.length < 10) {
      userPhone.control.setErrors({ required: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;
    if (!form.valid) return;

    this.localStorageService.setObject("QUOTATION_INFO", this.quotationData);
    this.navController.navigateForward("/client/portal/quot/pickup/delivery", { animated: true });
  }

  cancel() {
    this.navController.navigateRoot("/client/portal/quotation", { animated: true });
  }

}
