import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Nav<PERSON>ontroller, ModalController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { QuotationSpecialRequestStates } from 'src/modals/quotation-cargo-detail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';
import { FullImageComponent } from 'src/shared/full-image/full-image.component';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-client-quotation-add-documents',
  templateUrl: './client-quotation-add-documents.component.html',
  styleUrls: ['./client-quotation-add-documents.component.scss'],
  standalone: false
})
export class ClientQuotationAddDocumentsComponent implements OnInit {

  specialRequestData: QuotationSpecialRequestStates = new QuotationSpecialRequestStates();
  quotationData: any;
  backUrl!: string;
  showCalculations: any;
  quotationEditMode: any;
  activeSubscriptions: Subscription = new Subscription();
  showValidationErrors = false;
  requestImage: string | null = null;
  uploadedImages: {
    id: string | null;
    url: string;
    secureUrl?: string;
    fileName?: string;
    mimeType?: string;
    size?: number;
    originalName?: string;
    path?: string;
    selected?: boolean;
  }[] = [];
  quotationDetails: any;
  user: any;

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    public commonService: CommonService,
    private changeDetectorRef: ChangeDetectorRef,
    private modalCtrl: ModalController,
    public readonly authService: AuthService
  ) {

  }

  ngOnInit(): void {
    const quotationData = this.localStorageService.getObject("QUOTATION_SPECIAL_REQUEST");
    this.specialRequestData = quotationData ? QuotationSpecialRequestStates.fromResponse(quotationData) : new QuotationSpecialRequestStates();
  }

  ionViewWillEnter(): void {
    this.user = this.authService.getUser();
    this.quotationDetails = this.localStorageService.getObject("OTHER_QUOTATION_DETAILS");

    const quotationData = this.localStorageService.getObject("QUOTATION_SPECIAL_REQUEST");
    this.specialRequestData = quotationData ? QuotationSpecialRequestStates.fromResponse(quotationData) : new QuotationSpecialRequestStates();

    this.route.queryParams.subscribe(params => {
      this.quotationData = params['quotationData'] || null;
    });

    this.specialRequestData.id = this.quotationData;

    const storedDocuments = this.localStorageService.getObject("QUOTATION_DOCUMENTS");
    if (Array.isArray(storedDocuments)) {
      this.uploadedImages = storedDocuments.map((doc: any) => ({
        id: doc.id ?? null,
        url: doc.secureUrl || doc.path || '', // For display
        secureUrl: doc.secureUrl || '',
        fileName: doc.filename || '',
        mimeType: doc.mimeType || 'image/png',
        size: doc.size || 0,
        originalName: doc.originalName || '',
        path: doc.path || '',
        selected: false // default state
      }));
    }

    this.showCalculations = this.localStorageService.getBoolean("QUOTATION_SHOW_CALCULATIONS");
    this.quotationEditMode = this.localStorageService.get("QUOTATION_MODE");

    if (this.showCalculations && this.quotationEditMode === "edit") {
      this.backUrl = `/client/portal/quot/calculation?quotationData=${this.quotationData}`;
      return;
    }
    this.backUrl = `/client/portal/quot/special/request?quotationData=${this.quotationData}`;
  }

  async upload() {
    try {
      const response = await Camera.getPhoto({
        quality: 50,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Prompt, // Prompt user to select from camera or files
      });
      if (response.base64String) {
        await this.processCropper(response.base64String);
      }
    } catch (error) {
      //  this.toastService.show("Something went wrong while uploading profile picture.");
    }
  }

  async processCropper(base64Image: string) {
    const modal = await this.modalCtrl.create({
      component: FileCropperComponent,
      componentProps: {
        file: { base64String: base64Image }
      },
      cssClass: "cropper-modal"
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role !== 'confirm') {
      this.toastService.show("Sorry, profile picture uploading has been cancelled.");
      return;
    }
    if (data && data.croppedFile) {
      await this.uploadImage(data.croppedFile);
    } else {
      this.toastService.show("No file selected after cropping.");
    }
  }

  async uploadImage(blob: Blob): Promise<void> {
    if (!blob) {
      this.toastService.show('No file selected. Please choose an image to upload.');
      return;
    }

    const file = new File([blob], 'cropped-image.png', { type: 'image/png' }); // Convert Blob to File

    const formData = new FormData();
    formData.append('file', file, file.name);

    this.loadingService.show(); // Show loading indicator
    this.dataService.uploadFile(formData).subscribe({
      next: (response: any) => {
        this.loadingService.hide();
        this.handleUploadResponse(response);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred while uploading the file');
      }
    });
  }

  private handleUploadResponse(response: any): void {
    const attachments = response?.data;

    if (Array.isArray(attachments) && attachments.length > 0) {
      attachments.forEach((attachment: any) => {
        // Use secureUrl for display (url field) but preserve clean path for API
        const displayUrl = attachment.secureUrl || attachment.path || attachment.fileName;
        this.uploadedImages.push({
          id: null,
          url: displayUrl, // For display purposes
          secureUrl: attachment.secureUrl || '',
          fileName: attachment.filename || '',
          mimeType: attachment.mimeType || 'image/png',
          size: attachment.size || 0,
          originalName: attachment.originalName || '',
          path: attachment.path || '' // Store clean path separately
        });

      });
      this.changeDetectorRef.detectChanges();
      this.toastService.show('File uploaded successfully.');
    } else {
      this.toastService.show('Failed to upload file.');
    }
  }

  toggleSelected(selectedImage: { url: string, selected?: boolean }) {
    this.uploadedImages.forEach(image => {
      if (image === selectedImage) {
        image.selected = !image.selected; // Toggle current image
      } else {
        image.selected = false; // Deselect others
      }
    });
  }

  onDeleteClick(event: Event, image: { url: string }) {
    event.stopPropagation(); // Prevent parent (click) from firing
    this.removeImage(image);
  }

  removeImage(image: { url: string }) {
    this.uploadedImages = this.uploadedImages.filter(img => img.url !== image.url);
  }

  async viewImage(imageUrl: string, fileName?: string, showDelete: boolean = false) {
    const modal = await this.modalCtrl.create({
      component: FullImageComponent,
      componentProps: { imageUrl, fileName, showDelete },
      cssClass: 'full-image-modal'
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role === 'remove' && data) {
      this.removeImage({ url: data }); // use existing method
    }
  }

  async submitProfile(form: any): Promise<void> {
    // if (this.uploadedImages.length === 0) {
    //   this.toastService.show('Please upload at least one image before submitting.');
    //   return;
    // }

    const payload = {
      ...this.specialRequestData,
      documents: this.generateAttachments()
    };

    this.loadingService.show();
    this.dataService.saveUpdateQuotationSpecials(payload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.toastService.show(response.message);

        this.removeLocalStorageDetails();
        this.navController.navigateForward('/client/portal/quotation', {
          queryParams: { refresh: 'true' },
          animated: true
        });
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  removeLocalStorageDetails() {
    this.localStorageService.remove("QUOTATION_MODE");
    this.localStorageService.remove("QUOTATION_SPECIAL_REQUEST");
    this.localStorageService.remove("QUOTATION_DOCUMENTS");
    this.localStorageService.remove("QUOTATION_CALCULATION_DETAILS");
    this.localStorageService.remove("QUOTATION_SHOW_CALCULATIONS");
    this.localStorageService.remove("OTHER_QUOTATION_DETAILS");
  }

  private generateAttachments() {
    return this.uploadedImages.map((image) => ({
      id: image.id ?? null,
      filename: image.fileName || '',
      mimeType: image.mimeType || 'image/png',
      size: image.size || 0,
      path: image.path || image.url, // Use clean path if available, fallback to url
      originalName: image.originalName || '',
      secureUrl: image.secureUrl
    }));
  }

  cancel() {
    if (this.showCalculations && this.quotationEditMode === "edit") {
      this.navController.navigateBack('/client/portal/quot/calculation', {
        queryParams: {
          quotationData: this.quotationData
        }, animated: true
      });
      return;
    }
    this.navController.navigateBack('/client/portal/quot/special/request', {
      queryParams: {
        quotationData: this.quotationData
      }, animated: true
    });
  }

  goToQuotationList() {
    this.navController.navigateRoot("/client/portal/quotation", { animated: true });
  }

}
