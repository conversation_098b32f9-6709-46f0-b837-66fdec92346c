<ion-content class="quotation-page">
  <app-customer-header [innerPage]="true" [headingText]="'Quotation Management'" [rightAction]="false"
    [backUrl]="'/client/portal/dashboard'" [hideBellIcon]="true"></app-customer-header>

  <div class="fixed-search common-page-search">
    <div class="padding-top">
      <ion-item class="site-form-control" lines="none">
        <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
        <ion-input label="Search by Ref ID/ Status" labelPlacement="floating" name="searchHere"
          [(ngModel)]="searchQuery" (ionInput)="searchQuotationList()" [debounce]="500"></ion-input>
      </ion-item>
    </div>
    <div class="add-shipping-container secondary-ripple margin-bottom-10" (click)="addQuotation()">
      <ion-icon class="add-shipping-icon" src="assets/images/svg/black-add-icon.svg" slot="start"></ion-icon>
      <span class="add-shipping-text">ADD</span>
      <ion-ripple-effect></ion-ripple-effect>
    </div>
  </div>

  <div class="quotation-page-body-section">

    <div class="quotation-list-wrapper">
      <div class="empty-state" *ngIf="quotations.length <= 0">
        <div class="empty-content">
          <ion-icon name="cube-outline" class="empty-icon"></ion-icon>
          <h3>No Quotations Found</h3>
        </div>
      </div>
      <!-- Quotation Cards -->
      <div class="quotation-card" *ngFor="let item of filteredQuotations" (click)="viewQuotationDetails(item)"
        [ngClass]="{'highlighted-quotation': item.id === quotationNotificationId}">
        <div class="quotation-card-header">
          <div class="ref-id">
            Ref. Id<br /><strong>{{ item.refId }}</strong>
          </div>
          <div class="action-icons">
            <ion-icon class="edit-icon dark-ripple" src="/assets/images/svg/edit-icon.svg"
              (click)="onEditClick(item); $event.stopPropagation()">
              <ion-ripple-effect></ion-ripple-effect>
            </ion-icon>
          </div>
        </div>

        <div class="customer-info quotation-card-header">
          <div class="customer-name ref-id">
            Contact Person Name<br /><strong>{{ item.contactPersonName }}</strong>
          </div>
          <div class="action-icons">
            <div class="from-container">From<br /><strong>{{ item.from }}</strong></div>
          </div>
        </div>

        <div class="customer-info quotation-card-header">
          <div class="customer-name ref-id">
            Contact Person Phone<br /><strong>{{ item.contactPersonPhone && item.contactPersonPhone !== 'N/A'
              ? this.commonService.formatPhoneForDisplay(item.contactPersonPhone)
              : 'N/A' }}</strong>
          </div>
          <div class="action-icons">
            <div class="from-container">To<br /><strong>{{ item.to }}</strong></div>
          </div>
        </div>

        <div class="customer-info quotation-card-header">
          <div class="customer-name ref-id">
            Status<br /><strong class="status-text" [ngClass]="'status-' + (item.status || '').toLowerCase()">{{
              commonService.formatText(item.status) }}</strong>
          </div>
        </div>


      </div>

    </div>

  </div>
</ion-content>