 .quotation-page {
     --background: white;
     height: 100%;

     .site-form-control {
         width: 100%;
         margin-bottom: 10px;

         ion-input,
         ion-select {
             font-size: 13px;
             --padding-start: 0px !important;
             --padding-end: 0px;
             --padding-top: 2px;
             --padding-bottom: 0px;
             font-weight: 500;
             min-height: 50px !important;
             width: 100%;
         }

         ion-select::part(icon) {
             display: none;
         }

         .dropdown-arrow-icon {
             margin-top: 19px;
             font-size: 16px;
         }

         .start-icon {
             margin-right: 13px;
             color: black;
             width: 18px;
             height: 18px;
         }

         .search-ion-input {
             font-size: 14px;
             --padding-start: 0px !important;
             --padding-end: 0px;
             --padding-top: 0px;
             --padding-bottom: 0px;
             font-weight: 500;
             min-height: 50px !important;
             width: 100%;
         }
     }

     .add-shipping-container {
         display: flex;
         justify-content: space-between;
         align-items: center;
         background: #fcd616;
         color: black;
         border-radius: 18px;
         padding: 14px 12px;
         gap: 5px;
         position: relative;
         overflow: hidden;
         cursor: pointer;
         transition: all 0.2s ease;
         user-select: none;

         &:active {
             transform: translateY(0);
             box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
         }

         .add-shipping-icon {
             height: 22px;
             width: 32px;
         }

         .add-shipping-text {
             font-size: 15px;
             font-weight: 600;
         }

         ion-ripple-effect {
             color: rgba(255, 255, 255, 0.3);
         }
     }

     // Fixed search section styling
     .fixed-search.quotation-page-search {
         padding: 0px 20px 10px 20px;
         background: white;

         .padding-top {
             padding-top: 10px;
         }
     }

     .quotation-page-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 295px); // Adjusted for additional dropdown
         padding: 10px 20px 10px 20px !important;
         overflow-y: auto;

         .quotation-list-wrapper {
             //   height: calc(100% - 53px);
             //  overflow: auto;
         }

         .quotation-card {
             background-color: #fff;
             border-radius: 18px;
             padding: 14px 16px;
             margin-bottom: 16px;
             box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
             position: relative;
             overflow: hidden;
             cursor: pointer;
             transition: all 0.3s ease;

             &:hover {
                 box-shadow: 0 5px 15px rgba(0, 0, 0, 0.12);
                 transform: translateY(-2px);
             }

             &.active {
                 background-color: #FFEA00;
             }

             &.highlighted-quotation {
                 background-color: #f0f8ff !important; // Ensure it overrides default styles
                 border: 2px solid #4caf50 !important; // Highlighting styles
             }

             .quotation-card-header {
                 display: flex;
                 justify-content: space-between;
                 align-items: start;
                 margin-bottom: 10px;
                 position: relative;
                 z-index: 1;

                 .ref-id {
                     font-size: 13px;
                     font-weight: 500;
                     color: #444;

                     strong {
                         font-weight: 700;
                         font-size: 14px;
                         color: #000;
                     }
                 }

                 .action-icons {
                     display: flex;
                     gap: 10px;

                     ion-icon {
                         font-size: 20px;
                         color: #000;
                         cursor: pointer;
                         position: relative;
                         overflow: hidden;
                         border-radius: 50%;
                         padding: 8px;
                         transition: all 0.2s ease;
                         user-select: none;

                         &:hover {
                             background: rgba(0, 0, 0, 0.1);
                             transform: scale(1.1);
                         }

                         &:active {
                             transform: scale(0.95);
                         }

                         &.edit-icon {
                             color: #000;

                             ion-ripple-effect {
                                 color: rgba(0, 0, 0, 0.2);
                             }
                         }

                         &.delete-icon {
                             color: red;

                             ion-ripple-effect {
                                 color: rgba(255, 0, 0, 0.2);
                             }
                         }
                     }

                     .from-container {
                         display: flex;
                         flex-direction: column;
                         align-items: end;
                         gap: 3px;
                         font-size: 13px;
                         font-weight: 400;
                         text-align: end;

                         strong {
                             font-weight: 700;
                             color: #000;
                             display: -webkit-box;
                             -webkit-box-orient: vertical;
                             overflow: auto;
                             text-overflow: ellipsis;
                             word-break: break-word;
                             width: 115px;
                         }
                     }

                     .from,
                     .to {
                         font-size: 13px;

                         strong {
                             font-weight: 700;
                             color: #000;
                         }
                     }
                 }
             }

             .customer-info {
                 margin-bottom: 12px;
                 padding: 2px 0px;
                 border-radius: 8px;
                 position: relative;
                 z-index: 1;

                 .customer-name {
                     font-size: 12px;
                     color: black;
                     font-weight: 400;
                     line-height: 1.4;

                     strong {
                         color: #000;
                         font-size: 13px;
                         font-weight: 700;
                     }
                 }
             }


         }

         .pagination-section {
             display: flex;
             align-items: center;
             justify-content: center;
             gap: 16px;
             padding: 30px 0 20px 0;

             .pagination-btn {
                 --color: #666;
                 --background: #ffffff;
                 width: 44px;
                 height: 44px;
                 border-radius: 50%;
                 border: 1px solid #e0e0e0;
                 box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);

                 ion-icon {
                     font-size: 20px;
                 }

                 &:disabled {
                     --color: #ccc;
                     opacity: 0.5;
                     cursor: not-allowed;
                     box-shadow: none;
                 }

                 &:not(:disabled):hover {
                     --background: #f8f9fa;
                     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                 }
             }

             .page-numbers {
                 display: flex;
                 gap: 8px;

                 .page-number {
                     width: 36px;
                     height: 36px;
                     display: flex;
                     align-items: center;
                     justify-content: center;
                     border-radius: 50%;
                     font-size: 15px;
                     font-weight: 500;
                     color: #666;
                     cursor: pointer;
                     background: #ffffff;
                     border: 1px solid #e0e0e0;
                     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
                     transition: all 0.2s ease;

                     &.active {
                         background: #FFEA00;
                         color: #000;
                         font-weight: 700;
                         border-color: #FFEA00;
                         box-shadow: 0 4px 12px rgba(255, 234, 0, 0.3);
                         transform: scale(1.1);
                     }

                     &.ellipsis {
                         cursor: default;
                         pointer-events: none;
                         background: transparent;
                         border: none;
                         box-shadow: none;
                         color: #999;
                     }

                     &:not(.ellipsis):not(.active):hover {
                         background: #f8f9fa;
                         border-color: #FFEA00;
                         box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
                         transform: translateY(-1px);
                     }
                 }
             }
         }

         .results-info {
             text-align: center;
             padding: 10px 0 20px 0;

             .results-text {
                 font-size: 14px;
                 color: #666;
                 margin: 0;
             }
         }

         // Status text colors
         .status-text {
             &.status-requested {
                 color: #0ba5ec !important;
             }

             &.status-clientapproval {
                 color: #ffc107 !important;
             }

             &.status-confirmed {
                 color: #28a745 !important;
             }

             &.status-rejected {
                 color: #ff4405 !important;
             }
         }

     }
 }

 // Parallax Animations
 @keyframes headerFloat {

     0%,
     100% {
         transform: translateY(0px) scale(1);
         box-shadow: 0 4px 20px rgba(255, 234, 0, 0.25);
     }

     50% {
         transform: translateY(-1px) scale(1.002);
         box-shadow: 0 6px 25px rgba(255, 234, 0, 0.3);
     }
 }

 @keyframes curvedFloat {

     0%,
     100% {
         transform: translateY(0px) scaleX(1);
         border-radius: 0 0 50% 50%;
     }

     50% {
         transform: translateY(-1px) scaleX(1.005);
         border-radius: 0 0 48% 48%;
     }
 }