import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription, take } from 'rxjs';
import { QuotationSpecialRequestStates } from 'src/modals/quotation-cargo-detail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-client-quotation',
  templateUrl: './client-quotation.component.html',
  styleUrls: ['./client-quotation.component.scss'],
  standalone: false
})
export class ClientQuotationComponent implements OnInit {

  quotations: Array<any> = new Array<any>();
  searchQuery: string | null = null; // Variable to hold the search query
  filteredQuotations: Array<any> = new Array<any>();
  private subscription: Subscription = new Subscription();
  specialRequestData: QuotationSpecialRequestStates = new QuotationSpecialRequestStates();
  calculationDetails: {
    totalItems: number | null;
    totalWeight: number | null;
    totalVolume: number | null;
    grandTotal: number | null;
  } = {
      totalItems: null,
      totalWeight: null,
      totalVolume: null,
      grandTotal: null,
    };
  quotationNotificationId!: string;

  constructor(private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly localStorageService: LocalStorageService,
    public readonly commonService: CommonService,
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
  ) {

  }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.searchQuery = null;

    this.route.queryParams.pipe(take(1)).subscribe((params: any) => {
      this.quotationNotificationId = params['targetId'];
      setTimeout(() => {
        this.cdr.detectChanges();
      }, 100);
    });
    this.getAllQuotations();
  }

  getAllQuotations() {
    const payload = {
    };
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getAllQuotations(payload).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data.map((q: any) => ({
            ...q,
            refId: q.refID, // normalize key for UI consistency
            from: q.pickupAddressDetail?.city ?? 'N/A',
            to: q.deliveryAddressDetail?.city ?? 'N/A',
            contactPersonName: q.contactPersonName ?? 'N/A',
            contactPersonPhone: q.contactPersonPhone ?? 'N/A',
            status: q.status ?? 'N/A',
            paymentType: q.paymentType ?? 'N/A',
            quotationDate: q.createdOn ? q.createdOn : 'N/A',
          }));
          this.quotations = data;
          this.filteredQuotations = [...this.quotations];
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  addQuotation() {
    this.getRefId();
  }

  getRefId() {
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getQuotationRefId().subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data;
          this.removeLocalStorageDetails();
          this.navController.navigateForward("/client/portal/quot/basic/info", {
            queryParams: {
              refIdData: data?.refID,
              reset: true
            }, animated: true
          });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  onEditClick(item: any) {
    this.removeLocalStorageDetails();
    this.getQuotationById(item.id);
  }

  getQuotationById(id: string) {
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getQuotationById(id).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          this.saveQuotationDetails(data);
          this.saveCalculationDetails(data);
          this.saveSpecialRequestDetails(data);
          this.saveDocuments(data);

          this.navController.navigateForward(`/client/portal/quot/basic/info`, {
            animated: true
          });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  saveQuotationDetails(data: any) {
    this.localStorageService.setObject("QUOTATION_INFO", data);
    this.localStorageService.set("QUOTATION_MODE", "edit");
  }

  saveCalculationDetails(data: any) {
    this.localStorageService.setBoolean("QUOTATION_SHOW_CALCULATIONS", data.showCalculations);

    this.calculationDetails.totalItems = data.totalItems;
    this.calculationDetails.totalWeight = data.totalWeight;
    this.calculationDetails.totalVolume = data.totalVolume;
    this.calculationDetails.grandTotal = data.grandTotal;

    this.localStorageService.setObject("QUOTATION_CALCULATION_DETAILS", this.calculationDetails);
  }

  saveSpecialRequestDetails(data: any) {
    this.specialRequestData.isOversize = data.isOversize;
    this.specialRequestData.isRushRequest = data.isRushRequest;
    this.specialRequestData.isEnclosed = data.isEnclosed;
    this.specialRequestData.isFragile = data.isFragile;
    this.specialRequestData.isPerishable = data.isPerishable;
    this.specialRequestData.isDangerousGoods = data.isDangerousGoods;

    this.localStorageService.setObject("QUOTATION_SPECIAL_REQUEST", this.specialRequestData);
  }

  saveDocuments(data: any) {
    this.localStorageService.setObject("QUOTATION_DOCUMENTS", data.documents);
  }

  removeLocalStorageDetails() {
    this.localStorageService.remove("QUOTATION_PICKUP_DETAILS");
    this.localStorageService.remove("QUOTATION_SHOW_CALCULATIONS");
    this.localStorageService.remove("QUOTATION_SPECIAL_REQUEST");
    this.localStorageService.remove("QUOTATION_CALCULATION_DETAILS");
    this.localStorageService.remove("QUOTATION_DOCUMENTS");
    this.localStorageService.remove("QUOTATION_CARGO_MODE");
    this.localStorageService.remove("QUOTATION_CARGO_DETAILS");
    this.localStorageService.remove("OTHER_QUOTATION_DETAILS");
  }

  searchQuotationList() {
    this.filterQuotationList();
  }

  filterQuotationList() {
    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      this.filteredQuotations = this.quotations.filter(quotation =>
        quotation.contactPersonName?.toLowerCase().includes(query) ||
        quotation.refId?.toLowerCase().includes(query) ||
        quotation.from?.toLowerCase().includes(query) ||
        quotation.to?.toLowerCase().includes(query) ||
        quotation.status?.toLowerCase().includes(query)
      );
    } else {
      this.filteredQuotations = [...this.quotations];
    }
  }

  viewQuotationDetails(quotation: any) {
    this.navController.navigateForward(`/client/portal/quot/details/${quotation.id}`, {
      queryParams: {
        status: quotation.status,
        quotationDate: quotation.quotationDate
      },
      animated: true
    });
  }

}
