.quotation-details-page {
  --background: white;
  height: 100%;

  .quotation-details-body-section {
    display: inline-block;
    width: 100%;
    height: calc(100vh - 210px);
    padding: 10px 20px 10px 20px !important;
    overflow-y: auto;
  }

  .expandable-section {
    background: white;
    border-radius: 12px;
    margin-bottom: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.06);
    overflow: hidden;
    border: 1px solid #f2f2f2;
    padding: 8px;

    .section-header {
      padding: 12px 15px;
      background: #FFEA00;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      user-select: none;
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      margin-bottom: 0;

      .title-left {
        display: flex;
        align-items: center;
        gap: 8px;

        ion-icon {
          font-size: 18px;
          color: #333;
        }
      }

      .section-title {
        margin: 0;
        font-size: 16px;
        font-weight: 700;
        color: #000;
      }

      .expand-icon {
        font-size: 18px;
        color: #333;
        transition: transform 0.3s ease;
      }
    }

    .section-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
      background: white;

      &.expanded {
        max-height: 2000px;
        transition: max-height 0.5s ease;
      }
    }
  }

  .detail-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    &:last-child {
      border-bottom: none;
    }

    .detail-label {
      font-size: 14px;
      color: #666;
      font-weight: 500;
      flex: 1;
      margin-right: 15px;
    }

    .detail-value {
      font-size: 14px;
      color: #333;
      font-weight: 600;
      flex: 1.5;
      text-align: right;
      word-break: break-word;

      &.status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        text-transform: uppercase;
        text-align: center;
        max-width: 120px;

        &.status-requested {
          background: rgba(11, 165, 236, 0.1);
          color: #0ba5ec;
        }

        &.status-clientapproval {
          background: rgba(255, 193, 7, 0.1);
          color: #ffc107;
        }

        &.status-confirmed {
          background: rgba(40, 167, 69, 0.1);
          color: #28a745;
        }

        &.status-rejected {
          background: rgba(255, 68, 5, 0.1);
          color: #ff4405;
        }
      }
    }
  }

  .cargo-list {
    .cargo-item {
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .cargo-header {
        padding: 15px 20px 10px;
        background: #f8f9fa;

        h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .cargo-details {
        .detail-item {
          padding: 10px 20px;
        }
      }
    }
  }

  .summary-grid {
    padding: 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;

    .summary-item {
      text-align: center;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;

      &.grand-total {
        grid-column: 1 / -1;
        background: #FFEA00;

        .summary-value {
          font-size: 20px;
          font-weight: 700;
          color: #333;
        }
      }

      .summary-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 5px;
        text-transform: uppercase;
        font-weight: 500;
      }

      .summary-value {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }
  }

  .documents-list {
    padding: 20px;

    .document-item {
      display: flex;
      align-items: center;
      padding: 15px 0;
      border-bottom: 1px solid #f0f0f0;
      transition: background-color 0.2s ease;

      &:last-child {
        border-bottom: none;
      }

      &.clickable {
        cursor: pointer;

        &:hover {
          background-color: #f8f9fa;
          border-radius: 8px;
          margin: 0 -10px;
          padding: 15px 10px;
        }
      }

      .document-icon {
        margin-right: 15px;

        ion-icon {
          font-size: 24px;
          color: #666;

          &[name="image-outline"] {
            color: #28a745;
          }

          &[name="document-text-outline"] {
            color: #dc3545;
          }
        }
      }

      .document-info {
        flex: 1;

        .document-name {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin-bottom: 2px;
          width: 190px;
        }

        .document-size {
          font-size: 12px;
          color: #666;
        }
      }

      .document-action {
        margin-left: 10px;

        .view-icon {
          font-size: 20px;
          color: #007bff;
        }
      }
    }
  }

  .empty-state {
    padding: 40px 20px;
    text-align: center;
    color: #666;

    .empty-icon {
      font-size: 48px;
      color: #ccc;
      margin-bottom: 15px;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #999;
    }
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #666;

    ion-spinner {
      margin-bottom: 20px;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .quotation-details-page {
    .detail-item {
      flex-direction: column;
      align-items: flex-start;

      .detail-label {
        margin-bottom: 5px;
        margin-right: 0;
      }

      .detail-value {
        text-align: left;
      }
    }

    .summary-grid {
      grid-template-columns: 1fr;
      gap: 10px;
    }
  }
}