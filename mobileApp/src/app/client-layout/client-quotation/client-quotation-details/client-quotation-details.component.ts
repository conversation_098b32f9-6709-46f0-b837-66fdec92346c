import { Component, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { CommonService } from 'src/services/common.service';
import { RestResponse } from 'src/shared/auth.model';
import { ModalService } from 'src/shared/modal.service';
import { FullImageComponent } from 'src/shared/full-image/full-image.component';

interface ExpandableSection {
  id: string;
  title: string;
  isExpanded: boolean;
}

@Component({
  selector: 'app-client-quotation-details',
  templateUrl: './client-quotation-details.component.html',
  styleUrls: ['./client-quotation-details.component.scss'],
  standalone: false
})
export class ClientQuotationDetailsComponent implements OnInit, OnD<PERSON>roy {

  private subscription: Subscription = new Subscription();
  quotationId: string = '';
  quotationDetails: any;
  quotationGrandTotalData: any;

  // Data passed from quotation list
  passedStatus: string = '';
  passedQuotationDate: string = '';

  // Cached cargo and documents lists
  private _cargoList: any[] = [];
  private _documentsList: any[] = [];

  sections: ExpandableSection[] = [
    { id: 'basic', title: 'Basic Details', isExpanded: true },
    { id: 'pickup', title: 'Pickup and Delivery', isExpanded: false },
    { id: 'cargo', title: 'Cargo Details', isExpanded: false },
    { id: 'summary', title: 'Summary', isExpanded: false },
    { id: 'documents', title: 'Uploaded Documents', isExpanded: false }
  ];

  constructor(
    private route: ActivatedRoute,
    private navController: NavController,
    private dataService: DataService,
    private loadingService: LoadingService,
    private toastService: ToastService,
    public commonService: CommonService,
    private modalService: ModalService,
    private cdr: ChangeDetectorRef
  ) {

  }

  ngOnInit() {
    this.route.params.subscribe(params => {
      this.quotationId = params['id'];
      if (this.quotationId) {
        this.loadQuotationDetails();
      }
    });

    // Capture query parameters passed from quotation list
    this.route.queryParams.subscribe(queryParams => {
      this.passedStatus = queryParams['status'] || '';
      this.passedQuotationDate = queryParams['quotationDate'] || '';
    });
  }

  loadQuotationDetails() {
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getQuotationDetails(this.quotationId).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          this.quotationDetails = response.data;

          // Update cached lists
          this.updateCachedData();

          // Load grand total data
          this.loadQuotationGrandTotal();

          // Force change detection to update the UI
          this.cdr.detectChanges();
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to load quotation details');
          this.goBack();
        }
      })
    );
  }

  loadQuotationGrandTotal() {
    this.subscription.add(
      this.dataService.getQuotationGrandTotal(this.quotationId).subscribe({
        next: (response: RestResponse) => {
          this.quotationGrandTotalData = response.data;
          // Force change detection to update the UI
          this.cdr.detectChanges();
        },
        error: (error: any) => {
          console.error('Failed to load quotation grand total:', error);
          // Don't show error toast as this is secondary data
        }
      })
    );
  }

  toggleSection(sectionId: string) {
    const section = this.sections.find(s => s.id === sectionId);
    if (section) {
      section.isExpanded = !section.isExpanded;
    }
  }

  isSectionExpanded(sectionId: string): boolean {
    const section = this.sections.find(s => s.id === sectionId);
    return section ? section.isExpanded : false;
  }

  goBack() {
    this.navController.navigateBack('/client/portal/quotation');
  }

  // Update cached data when quotation details are loaded
  private updateCachedData() {
    // Force recalculation of cargo and documents
    this._cargoList = [];
    this._documentsList = [];

    // Get fresh data and update cache
    this._cargoList = [...this.getCargoList()];
    this._documentsList = [...this.getDocuments()];
  }

  // Helper methods for displaying data
  getPickupAddress(): string {
    const addr = this.quotationDetails?.pickupAddressDetail ||
      this.quotationDetails?.pickupAddress ||
      this.quotationDetails?.pickup?.address;
    if (!addr) return 'N/A';
    return `${addr.address || ''}, ${addr.city || ''}, ${addr.province || addr.state || ''}, ${addr.postalCode || addr.zipCode || ''}`.replace(/^,\s*|,\s*$/g, '');
  }

  getDeliveryAddress(): string {
    const addr = this.quotationDetails?.deliveryAddressDetail ||
      this.quotationDetails?.deliveryAddress ||
      this.quotationDetails?.delivery?.address;
    if (!addr) return 'N/A';
    return `${addr.address || ''}, ${addr.city || ''}, ${addr.province || addr.state || ''}, ${addr.postalCode || addr.zipCode || ''}`.replace(/^,\s*|,\s*$/g, '');
  }

  getCargoList(): any[] {
    if (!this.quotationDetails) {
      return [];
    }

    // Try multiple possible field names - API uses "cargoDetail" (singular)
    const possibleFields = ['cargoDetail', 'cargoDetails', 'cargo', 'items', 'quotationItems', 'cargoItems'];
    let cargoData = [];

    for (const field of possibleFields) {
      if (this.quotationDetails[field]) {
        cargoData = this.quotationDetails[field];
        break;
      }
    }

    return Array.isArray(cargoData) ? cargoData : [];
  }

  getDocuments(): any[] {
    // Get documents array from the response
    const documentsData = this.quotationDetails?.documents || [];
    return Array.isArray(documentsData) ? documentsData : [];
  }

  getTotalWeight(): number {
    const cargoList = this.getCargoList();
    return cargoList.reduce((total, cargo) => total + (cargo.weightInPounds || 0), 0);
  }

  getTotalVolume(): number {
    const cargoList = this.getCargoList();
    return cargoList.reduce((total, cargo) => total + (cargo.volume || 0), 0);
  }

  getTotalItems(): number {
    const cargoList = this.getCargoList();
    return cargoList.reduce((total, cargo) => total + (cargo.totalCount || 1), 0);
  }

  getGrandTotal(): number {
    // First, try to get grand total from the dedicated API
    if (this.quotationGrandTotalData) {
      const possibleFields = [
        'grandTotal',
        'totalAmount',
        'total',
        'amount',
        'finalAmount',
        'totalCost',
        'quotationAmount',
        'calculatedTotal',
        'subTotal',
        'netAmount'
      ];

      for (const field of possibleFields) {
        const value = this.quotationGrandTotalData[field];
        if (value !== null && value !== undefined && value !== 0) {
          return Number(value) || 0;
        }
      }

      // Try calculation details from grand total API
      const calculation = this.quotationGrandTotalData.calculation || this.quotationGrandTotalData.calculationDetails;
      if (calculation) {
        return Number(calculation.grandTotal || calculation.totalAmount || calculation.total) || 0;
      }
    }

    // Fallback to original quotation details
    if (!this.quotationDetails) {
      return 0;
    }

    const possibleFields = [
      'grandTotal',
      'totalAmount',
      'total',
      'amount',
      'finalAmount',
      'totalCost',
      'quotationAmount',
      'calculatedTotal',
      'subTotal',
      'netAmount'
    ];

    for (const field of possibleFields) {
      const value = this.quotationDetails[field];
      if (value !== null && value !== undefined && value !== 0) {
        return Number(value) || 0;
      }
    }

    // If no direct field found, try to calculate from calculation details
    const calculation = this.quotationDetails.calculation || this.quotationDetails.calculationDetails;
    if (calculation) {
      return Number(calculation.grandTotal || calculation.totalAmount || calculation.total) || 0;
    }

    return 0;
  }

  // Helper method to check if we have data to display
  hasQuotationData(): boolean {
    return !!this.quotationDetails;
  }

  hasCargoData(): boolean {
    return this.getCargoList().length > 0;
  }

  hasDocumentsData(): boolean {
    return this.getDocuments().length > 0;
  }

  // Getter properties for template binding
  get cargoList(): any[] {
    // Always get fresh data if cache is empty
    if (this._cargoList.length === 0 && this.quotationDetails) {
      const freshData = this.getCargoList();
      if (freshData.length > 0) {
        this._cargoList = [...freshData];
      }
    }
    return this._cargoList.length > 0 ? this._cargoList : this.getCargoList();
  }

  get documentsList(): any[] {
    // Always get fresh data if cache is empty
    if (this._documentsList.length === 0 && this.quotationDetails) {
      const freshData = this.getDocuments();
      if (freshData.length > 0) {
        this._documentsList = [...freshData];
      }
    }
    return this._documentsList.length > 0 ? this._documentsList : this.getDocuments();
  }

  get hasCargoItems(): boolean {
    return this.cargoList.length > 0;
  }

  get hasDocumentItems(): boolean {
    return this.documentsList.length > 0;
  }

  // Helper method for template
  getObjectKeys(obj: any): string[] {
    return obj ? Object.keys(obj) : [];
  }

  isArray(value: any): boolean {
    return Array.isArray(value);
  }

  // Helper method to get cargo type color
  getCargoTypeColor(cargoType: string): string {
    if (!cargoType) return '#6c757d';

    const colors: { [key: string]: string } = {
      'PALLET': '#007bff',
      'BOX': '#28a745',
      'CONTAINER': '#ffc107',
      'BULK': '#dc3545',
      'LIQUID': '#17a2b8',
      'FRAGILE': '#fd7e14',
      'HAZARDOUS': '#e83e8c'
    };

    return colors[cargoType.toUpperCase()] || '#6c757d';
  }

  // Helper method to format text (capitalize first letter)
  formatText(text: string): string {
    if (!text) return '';
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  }

  // Helper method to format currency
  formatCurrency(amount: number): string {
    if (!amount || amount === 0) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatPhoneNumber(phone: string): string {
    return this.commonService.formatPhoneForDisplay(phone);
  }

  formatDate(date: string): string {
    return this.commonService.formatDisplayDate(date);
  }

  // Document viewing methods
  isImageFile(fileName: string): boolean {
    if (!fileName) return false;
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
    return imageExtensions.includes(extension);
  }

  isPdfFile(fileName: string): boolean {
    if (!fileName) return false;
    return fileName.toLowerCase().endsWith('.pdf');
  }

  getFileExtension(fileName: string): string {
    if (!fileName) return '';
    return fileName.substring(fileName.lastIndexOf('.') + 1).toUpperCase();
  }

  async viewDocument(document: any) {
    const fileName = document.fileName || document.name || document.originalName || document.title || 'Document';
    const fileUrl = document.secureUrl || document.fileUrl || document.url || document.path;

    if (!fileUrl) {
      this.toastService.show('Document URL not available');
      return;
    }

    if (this.isImageFile(fileName)) {
      // Open image in modal
      await this.viewImageInModal(fileUrl, fileName);
    } else if (this.isPdfFile(fileName)) {
      // Open PDF in browser
      this.openPdfInBrowser(fileUrl);
    } else {
      // For other file types, try to open in browser
      this.openFileInBrowser(fileUrl);
    }
  }

  async viewImageInModal(imageUrl: string, fileName: string) {
    const modal = await this.modalService.presentModal(FullImageComponent, {
      imageUrl: imageUrl,
      fileName: fileName,
      showDelete: false
    });

    this.modalService.onComplete(modal, (data: any) => {
      // Handle modal completion if needed
    });
  }

  openPdfInBrowser(pdfUrl: string) {
    window.open(pdfUrl, '_blank');
  }

  openFileInBrowser(fileUrl: string) {
    window.open(fileUrl, '_blank');
  }

  getDocumentIcon(fileName: string): string {
    if (this.isImageFile(fileName)) {
      return 'image-outline';
    } else if (this.isPdfFile(fileName)) {
      return 'document-text-outline';
    } else {
      return 'document-outline';
    }
  }

  // Helper methods to get status and date with fallback
  getQuotationStatus(): string {
    return this.passedStatus || this.quotationDetails?.status || 'N/A';
  }

  getQuotationDate(): string {
    return this.passedQuotationDate || this.quotationDetails?.createdOn || 'N/A';
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

}
