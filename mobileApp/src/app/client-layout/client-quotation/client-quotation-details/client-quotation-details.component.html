<ion-content class="quotation-details-page">
  <app-customer-header [innerPage]="true" [headingText]="'Quotation Details'" [rightAction]="false"
    [backUrl]="'/client/portal/quotation'" [hideBellIcon]="true"></app-customer-header>

  <div class="quotation-details-body-section" *ngIf="quotationDetails">
    <!-- Basic Details Section -->
    <div class="expandable-section">
      <div class="section-header" (click)="toggleSection('basic')">
        <div class="title-left">
          <ion-icon name="information-circle-outline"></ion-icon>
          <h3 class="section-title">{{ sections[0].title }}</h3>
        </div>
        <ion-icon [name]="isSectionExpanded('basic') ? 'chevron-up' : 'chevron-down'" class="expand-icon">
        </ion-icon>
      </div>
      <div class="section-content" [class.expanded]="isSectionExpanded('basic')">
        <div class="detail-item">
          <div class="detail-label">Ref ID</div>
          <div class="detail-value">{{ quotationDetails.refID || 'N/A' }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Contact Person Name</div>
          <div class="detail-value">{{ quotationDetails.contactPersonName || 'N/A' }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Contact Email</div>
          <div class="detail-value">{{ quotationDetails.contactPersonEmail || 'N/A' }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Contact Phone</div>
          <div class="detail-value">{{
            quotationDetails.contactPersonPhone ?
            formatPhoneNumber(quotationDetails.contactPersonPhone) : 'N/A'
            }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Status</div>
          <div class="detail-value status-badge" [class]="'status-' + (getQuotationStatus() || '').toLowerCase()">
            {{ formatText(getQuotationStatus()) || 'N/A' }}
          </div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Quotation Date</div>
          <div class="detail-value">{{ formatDate(getQuotationDate()) || 'N/A' }}</div>
        </div>
      </div>
    </div>

    <!-- Pickup Details Section -->
    <div class="expandable-section">
      <div class="section-header" (click)="toggleSection('pickup')">
        <div class="title-left">
          <ion-icon name="pin-outline"></ion-icon>
          <h3 class="section-title">{{ sections[1].title }}</h3>
        </div>
        <ion-icon [name]="isSectionExpanded('pickup') ? 'chevron-up' : 'chevron-down'" class="expand-icon">
        </ion-icon>
      </div>
      <div class="section-content" [class.expanded]="isSectionExpanded('pickup')">
        <div class="detail-item">
          <div class="detail-label">Pickup Contact Name</div>
          <div class="detail-value">{{
            quotationDetails.pickupContactPersonName ||
            quotationDetails.pickupContactName ||
            quotationDetails.pickup?.contactName || 'N/A'
            }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Pickup Company Name</div>
          <div class="detail-value">{{
            quotationDetails.pickupCompanyName ||
            quotationDetails.pickup?.companyName || 'N/A'
            }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Pickup Contact Phone</div>
          <div class="detail-value">{{
            (quotationDetails.pickupContactPersonPhone || quotationDetails.pickup?.phone) ?
            formatPhoneNumber(quotationDetails.pickupContactPersonPhone || quotationDetails.pickup?.phone) : 'N/A'
            }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Pickup Address</div>
          <div class="detail-value">{{ getPickupAddress() }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Delivery Contact Name</div>
          <div class="detail-value">{{
            quotationDetails.deliveryContactPersonName ||
            quotationDetails.deliveryContactName ||
            quotationDetails.delivery?.contactName || 'N/A'
            }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Delivery Company Name</div>
          <div class="detail-value">{{
            quotationDetails.deliveryCompanyName ||
            quotationDetails.delivery?.companyName || 'N/A'
            }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Delivery Contact Phone</div>
          <div class="detail-value">{{
            (quotationDetails.deliveryContactPersonPhone || quotationDetails.delivery?.phone) ?
            formatPhoneNumber(quotationDetails.deliveryContactPersonPhone || quotationDetails.delivery?.phone) : 'N/A'
            }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Delivery Address</div>
          <div class="detail-value">{{ getDeliveryAddress() }}</div>
        </div>
      </div>
    </div>

    <!-- Cargo Details Section -->
    <div class="expandable-section">
      <div class="section-header" (click)="toggleSection('cargo')">
        <div class="title-left">
          <ion-icon name="cube-outline"></ion-icon>
          <h3 class="section-title">{{ sections[2].title }}</h3>
        </div>
        <ion-icon [name]="isSectionExpanded('cargo') ? 'chevron-up' : 'chevron-down'" class="expand-icon">
        </ion-icon>
      </div>
      <div class="section-content" [class.expanded]="isSectionExpanded('cargo')">

        <!-- Summary for multiple cargo items -->
        <div class="cargo-summary" *ngIf="cargoList.length > 1"
          style="margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
          <h4 style="margin: 0 0 10px 0; color: #495057;">{{ cargoList.length }} Cargo Items - Summary</h4>
          <div class="summary-totals" style="display: flex; gap: 15px; flex-wrap: wrap;">
            <span class="summary-item"
              style="background: #e9ecef; padding: 5px 10px; border-radius: 3px; font-size: 12px;">
              <strong>Total Weight:</strong> {{ getTotalWeight() }} lbs
            </span>
            <span class="summary-item"
              style="background: #e9ecef; padding: 5px 10px; border-radius: 3px; font-size: 12px;">
              <strong>Total Volume:</strong> {{ getTotalVolume() }} cu inches
            </span>
            <span class="summary-item"
              style="background: #e9ecef; padding: 5px 10px; border-radius: 3px; font-size: 12px;">
              <strong>Total Items:</strong> {{ getTotalItems() }}
            </span>
          </div>
        </div>

        <div class="cargo-list" *ngIf="hasCargoItems; else noCargo">
          <div class="cargo-card" *ngFor="let cargo of cargoList; let i = index"
            style="margin-bottom: 10px; background: white; border: 1px solid #e0e0e0; border-radius: 8px; padding: 12px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">

            <!-- Compact Header -->
            <div class="cargo-card-header"
              style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
              <div class="cargo-title" style="display: flex; align-items: center; gap: 8px;">
                <span class="cargo-number"
                  style="background: #f0f0f0; color: #666; padding: 2px 6px; border-radius: 4px; font-size: 11px; font-weight: 600;">
                  <span *ngIf="cargoList.length === 1">#1</span>
                  <span *ngIf="cargoList.length > 1">#{{ i + 1 }}</span>
                </span>
                <span class="cargo-type-badge"
                  style="color: white; padding: 2px 8px; border-radius: 12px; font-size: 10px; font-weight: 600; text-transform: uppercase;"
                  [style.background]="getCargoTypeColor(cargo.cargoType)">
                  {{ cargo.cargoType || 'Unknown' }}
                </span>
              </div>
              <div class="cargo-cost" *ngIf="cargo.total && cargo.total > 0"
                style="color: #28a745; font-weight: 600; font-size: 14px;">
                {{ formatCurrency(cargo.total) }}
              </div>
            </div>

            <!-- Compact Details Grid -->
            <div class="cargo-details-grid"
              style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 12px;">
              <div class="detail-compact">
                <span style="color: #666; font-weight: 500;">Weight:</span>
                <span style="color: #333; font-weight: 600; margin-left: 4px;">{{ cargo.weightInPounds || 0 }}
                  lbs</span>
              </div>
              <div class="detail-compact">
                <span style="color: #666; font-weight: 500;">Volume:</span>
                <span style="color: #333; font-weight: 600; margin-left: 4px;">{{ cargo.volume || 0 }} cu inches</span>
              </div>
              <div class="detail-compact" *ngIf="cargo.totalCount && cargo.totalCount > 0">
                <span style="color: #666; font-weight: 500;">Qty:</span>
                <span style="color: #333; font-weight: 600; margin-left: 4px;">{{ cargo.totalCount }}</span>
              </div>
            </div>

            <!-- Description (if exists) -->
            <div class="cargo-description" *ngIf="cargo.description"
              style="margin-top: 8px; padding: 6px 8px; background: #f8f9fa; border-radius: 4px; font-size: 11px; color: #555; line-height: 1.3;">
              <ion-icon name="document-text-outline"
                style="font-size: 12px; margin-right: 4px; vertical-align: middle;"></ion-icon>
              {{ cargo.description }}
            </div>
          </div>
        </div>
        <ng-template #noCargo>
          <div class="empty-state">
            <p>No cargo items found</p>
          </div>
        </ng-template>
      </div>
    </div>

    <!-- Summary Section -->
    <div class="expandable-section">
      <div class="section-header" (click)="toggleSection('summary')">
        <div class="title-left">
          <ion-icon name="cash-outline"></ion-icon>
          <h3 class="section-title">{{ sections[3].title }}</h3>
        </div>
        <ion-icon [name]="isSectionExpanded('summary') ? 'chevron-up' : 'chevron-down'" class="expand-icon">
        </ion-icon>
      </div>
      <div class="section-content" [class.expanded]="isSectionExpanded('summary')">
        <div class="summary-grid">
          <div class="summary-item">
            <div class="summary-label">Total Items</div>
            <div class="summary-value">{{ getTotalItems() }}</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">Total Weight</div>
            <div class="summary-value">{{ getTotalWeight() }} lbs</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">Total Volume</div>
            <div class="summary-value">{{ getTotalVolume() }} cu inches</div>
          </div>
          <div class="summary-item grand-total">
            <div class="summary-label">Grand Total</div>
            <div class="summary-value">{{ formatCurrency(getGrandTotal()) }}</div>
          </div>
        </div>
        <div class="detail-item" *ngIf="quotationDetails.summary">
          <div class="detail-label">Description</div>
          <div class="detail-value">{{ quotationDetails.summary }}</div>
        </div>
      </div>
    </div>

    <!-- Uploaded Documents Section -->
    <div class="expandable-section">
      <div class="section-header" (click)="toggleSection('documents')">
        <div class="title-left">
          <ion-icon name="document-text-outline"></ion-icon>
          <h3 class="section-title">{{ sections[4].title }}</h3>
        </div>
        <ion-icon [name]="isSectionExpanded('documents') ? 'chevron-up' : 'chevron-down'" class="expand-icon">
        </ion-icon>
      </div>
      <div class="section-content" [class.expanded]="isSectionExpanded('documents')">

        <div class="documents-list" *ngIf="hasDocumentItems; else noDocuments">
          <div class="document-item clickable" *ngFor="let document of documentsList" (click)="viewDocument(document)">
            <div class="document-icon">
              <ion-icon
                [name]="getDocumentIcon(document.fileName || document.name || document.originalName || document.title)"></ion-icon>
            </div>
            <div class="document-info">
              <div class="document-name">{{
                document.fileName ||
                document.name ||
                document.originalName ||
                document.title || 'Document'
                }}</div>
              <div class="document-size">{{
                document.fileSize ||
                document.size ||
                'Unknown size'
                }}</div>
            </div>
            <div class="document-action">
              <ion-icon name="eye-outline" class="view-icon"></ion-icon>
            </div>
          </div>
        </div>
        <ng-template #noDocuments>
          <div class="empty-state">
            <ion-icon name="document-outline" class="empty-icon"></ion-icon>
            <p>No documents uploaded</p>
          </div>
        </ng-template>
      </div>
    </div>

  </div>

  <!-- Loading state -->
  <!-- <div class="loading-state" *ngIf="!quotationDetails">
    <ion-spinner></ion-spinner>
    <p>Loading quotation details...</p>
  </div> -->

</ion-content>