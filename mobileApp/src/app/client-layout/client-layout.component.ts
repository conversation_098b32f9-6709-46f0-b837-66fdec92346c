import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { NavController, Platform } from '@ionic/angular';
import { filter, Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { EventService } from 'src/shared/event.service';
import { FcmService } from 'src/shared/fcm.service';


@Component({
  selector: 'app-client-layout',
  templateUrl: './client-layout.component.html',
  styleUrls: ['./client-layout.component.scss'],
  standalone: false
})
export class ClientLayoutComponent implements OnInit, OnDestroy {

  showTabs = true;
  private routeSub!: Subscription;

  constructor(private readonly navController: NavController,
    private readonly router: Router,
    private readonly platform: Platform,
    public readonly commonService: CommonService,
    private readonly eventService: EventService,
    private readonly fcmService: FcmService,
    private readonly changeDetectorRef: ChangeDetectorRef
  ) {

  }

  ngOnInit() {
    this.platform.ready().then(() => {
      if (this.platform.is('cordova')) {
        this.fcmService.initializeFirebaseToken();
      }
    });
    this.routeSub = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.checkRoute();
      });

    this.eventService.event.subscribe((data) => {
      if (!data) {
        return;
      }
      if (data.key === "fcm:notification") {
        this.processNotifications(JSON.parse(JSON.stringify(data.value)))
      }
      data = undefined;
    });
  }

  ionViewWillEnter() {
  }

  processNotifications(data: any) {
    let queryParams: any = {
      targetId: data.targetId,
      targetType: data.targetType,
      message: data.message,
      status: data.status
    };

    if (data.targetType === "SHIPMENT") {
      if (data.status === "NEW" || data.status === "ASSIGNED") {
        queryParams.refresh = 'true';
        queryParams.goTab = 'PENDING';
      } else if (data.status === "IN_TRANSIT") {
        queryParams.refresh = 'true';
        queryParams.goTab = 'IN_TRANSIT_TAB';
      } else if (data.status === "DELIVERED" || data.status === "COMPLETED") {
        queryParams.refresh = 'true';
        queryParams.goTab = 'COMPLETED';
      }
      this.navController.navigateForward('/client/portal/shipping', {
        queryParams,
        animated: true
      });
    } else if (data.targetType === "QUOTATION") {
      this.navController.navigateForward('/client/portal/quotation', {
        queryParams,
        animated: true
      });
    }

    setTimeout(() => this.changeDetectorRef.detectChanges(), 200);
  }

  private checkRoute() {
    const currentRoute = this.router.url;

    const hideTabRoutes = [
      '/client/portal/dashboard',
      '/client/portal/shipping',
      '/client/portal/quotation',
      '/client/portal/calendar',
      '/client/portal/account',
      '/client/portal/address',
      '/client/portal/basic/info',
      '/client/portal/other/info',
      '/client/portal/pickup/delivery',
      '/client/portal/delivery/location',
      '/client/portal/cargo/listing',
      '/client/portal/add/cargo/details',
      '/client/portal/special/request',
      '/client/portal/calculation',
      '/client/portal/quot/basic/info',
      '/client/portal/quot/pickup/delivery',
      '/client/portal/quot/delivery/location',
      '/client/portal/quot/cargo/listing',
      '/client/portal/quot/add/cargo/details',
      '/client/portal/quot/special/request',
      '/client/portal/quot/calculation',
      '/client/portal/quot/details',
      '/client/portal/shipment/details'
    ];

    this.showTabs = hideTabRoutes.some(route => currentRoute.startsWith(route));

    // Set active tab manually
    if (currentRoute.startsWith('/client/portal/shipping') || currentRoute.startsWith('/client/portal/basic/info') || currentRoute.startsWith('/client/portal/other/info') ||
      currentRoute.startsWith('/client/portal/pickup/delivery') || currentRoute.startsWith('/client/portal/delivery/location')
      || currentRoute.startsWith('/client/portal/cargo/listing') || currentRoute.startsWith('/client/portal/add/cargo/details')
      || currentRoute.startsWith('/client/portal/special/request') || currentRoute.startsWith('/client/portal/calculation') ||
      currentRoute.startsWith('/client/portal/shipment/details')) {
      this.commonService.activeTab = 'shipping';
    } else if (currentRoute.startsWith('/client/portal/quotation') || currentRoute.startsWith('/client/portal/quot/basic/info') || currentRoute.startsWith('/client/portal/quot/pickup/delivery') ||
      currentRoute.startsWith('/client/portal/quot/delivery/location') || currentRoute.startsWith('/client/portal/quot/cargo/listing')
      || currentRoute.startsWith('/client/portal/quot/add/cargo/details') || currentRoute.startsWith('/client/portal/quot/special/request')
      || currentRoute.startsWith('/client/portal/quot/calculation') || currentRoute.startsWith('/client/portal/quot/details')) {
      this.commonService.activeTab = 'quotation';
    } else if (currentRoute.startsWith('/client/portal/calendar')) {
      this.commonService.activeTab = 'calendar';
    } else if (currentRoute.startsWith('/client/portal/account') || currentRoute.startsWith('/client/portal/address')) {
      this.commonService.activeTab = 'account';
    } else {
      this.commonService.activeTab = '';
    }
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }

}
