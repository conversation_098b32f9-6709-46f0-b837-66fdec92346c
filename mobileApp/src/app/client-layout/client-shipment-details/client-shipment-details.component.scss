@import '../../driver-layout/shipment-details/shipment-details.component.scss';

.shipment-details-page {
  .image-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; }
  .image-item { position: relative; border-radius: 8px; overflow: hidden; border: 1px solid #eee; }
  .image-item img { width: 100%; height: 90px; object-fit: cover; display: block; }

  // Make titles clickable layout consistent
  .section-title { justify-content: space-between; padding: 10px 12px; }
  .section-title .title-left { display: inline-flex; align-items: center; gap: 6px; }
  .section-title.clickable { cursor: pointer; }
  .section-title .chev { font-size: 18px; }

  // Improve grid and field wrapping in client view
  .info-grid { grid-template-columns: 1fr !important; gap: 10px; }
  .info-card { align-items: flex-start; }
  .info-card > div { min-width: 0; }
  .label { white-space: nowrap; }
  .value { word-break: break-word; overflow-wrap: anywhere; }
}


