import { Routes } from "@angular/router";
import { ClientLayoutComponent } from "./client-layout.component";
import { ClientHomeComponent } from "./client-home/client-home.component";
import { ClientShippingComponent } from "./client-shipping/client-shipping.component";
import { ClientQuotationComponent } from "./client-quotation/client-quotation.component";
import { ClientQuotationBasicInfoComponent } from "./client-quotation/add-quotation-details/client-quotation-basic-info/client-quotation-basic-info.component";
import { ClientQuotationDetailsComponent } from "./client-quotation/client-quotation-details/client-quotation-details.component";
import { ClientCalendarComponent } from "./client-calendar/client-calendar.component";
import { ClientAccountComponent } from "./client-account/client-account.component";
import { AddCargoDetailsComponent } from "../driver-layout/driver-shipping/add-shipping-details/add-cargo-details/add-cargo-details.component";
import { AddDocumentsComponent } from "../driver-layout/driver-shipping/add-shipping-details/add-documents/add-documents.component";
import { BasicInfoComponent } from "../driver-layout/driver-shipping/add-shipping-details/basic-info/basic-info.component";
import { DeliveryLocationComponent } from "../driver-layout/driver-shipping/add-shipping-details/delivery-location/delivery-location.component";
import { OtherInfoComponent } from "../driver-layout/driver-shipping/add-shipping-details/other-info/other-info.component";
import { PickupDeliveryComponent } from "../driver-layout/driver-shipping/add-shipping-details/pickup-delivery/pickup-delivery.component";
import { ShipmentCargoListingComponent } from "../driver-layout/driver-shipping/add-shipping-details/shipment-cargo-listing/shipment-cargo-listing.component";
import { SpecialRequestComponent } from "../driver-layout/driver-shipping/add-shipping-details/special-request/special-request.component";
import { CustomerCalculationComponent } from "./client-shipping/customer-calculation/customer-calculation.component";
import { ClientQuotationAddCargoComponent } from "./client-quotation/add-quotation-details/client-quotation-add-cargo/client-quotation-add-cargo.component";
import { ClientQuotationAddDocumentsComponent } from "./client-quotation/add-quotation-details/client-quotation-add-documents/client-quotation-add-documents.component";
import { ClientQuotationCargoListComponent } from "./client-quotation/add-quotation-details/client-quotation-cargo-list/client-quotation-cargo-list.component";
import { ClientQuotationDeliveryLocationComponent } from "./client-quotation/add-quotation-details/client-quotation-delivery-location/client-quotation-delivery-location.component";
import { ClientQuotationPickupDeliveryComponent } from "./client-quotation/add-quotation-details/client-quotation-pickup-delivery/client-quotation-pickup-delivery.component";
import { ClientQuotationSpecialRequestComponent } from "./client-quotation/add-quotation-details/client-quotation-special-request/client-quotation-special-request.component";
import { ClientQuotationCalculationComponent } from "./client-quotation/add-quotation-details/client-quotation-calculation/client-quotation-calculation.component";
import { ViewDocumentsComponent } from "../driver-layout/view-documents/view-documents.component";
import { ClientAccountAddressComponent } from "./client-account/client-account-address/client-account-address.component";
import { ClientShipmentDetailsComponent } from "./client-shipment-details/client-shipment-details.component";
import { NotificationListComponent } from "../driver-layout/notification-list/notification-list.component";
import { CustomerChangePasswordComponent } from "./client-account/customer-change-password/customer-change-password.component";

export const CLIENTLAYOUTROUTING: Routes = [
    {
        path: "",
        component: ClientLayoutComponent,
        children: [
            {
                path: "dashboard",
                component: ClientHomeComponent
            },
            {
                path: "shipping",
                component: ClientShippingComponent
            },
            {
                path: "quotation",
                component: ClientQuotationComponent
            },
            {
                path: "quot/details/:id",
                component: ClientQuotationDetailsComponent
            },
            {
                path: "quot/basic/info",
                component: ClientQuotationBasicInfoComponent
            },
            {
                path: "quot/pickup/delivery",
                component: ClientQuotationPickupDeliveryComponent
            },
            {
                path: "quot/delivery/location",
                component: ClientQuotationDeliveryLocationComponent
            },
            {
                path: "quot/cargo/listing",
                component: ClientQuotationCargoListComponent
            },
            {
                path: "quot/add/cargo/details",
                component: ClientQuotationAddCargoComponent
            },
            {
                path: "quot/special/request",
                component: ClientQuotationSpecialRequestComponent
            },
            {
                path: 'quot/calculation',
                component: ClientQuotationCalculationComponent,
            },
            {
                path: "quot/add/documents",
                component: ClientQuotationAddDocumentsComponent
            },
            {
                path: "calendar",
                component: ClientCalendarComponent
            },
            {
                path: "account",
                component: ClientAccountComponent
            },
            {
                path: "address",
                component: ClientAccountAddressComponent
            },
            {
                path: 'basic/info',
                component: BasicInfoComponent,
            },
            {
                path: 'other/info',
                component: OtherInfoComponent,
            },
            {
                path: 'pickup/delivery',
                component: PickupDeliveryComponent,
            },
            {
                path: 'delivery/location',
                component: DeliveryLocationComponent,
            },
            {
                path: 'cargo/listing',
                component: ShipmentCargoListingComponent,
            },
            {
                path: 'add/cargo/details',
                component: AddCargoDetailsComponent,
            },
            {
                path: 'special/request',
                component: SpecialRequestComponent,
            },
            {
                path: 'add/documents',
                component: AddDocumentsComponent,
            },
            {
                path: 'view/documents',
                component: ViewDocumentsComponent,
            },
            {
                path: 'calculation',
                component: CustomerCalculationComponent,
            },
            {
                path: 'shipment/details',
                component: ClientShipmentDetailsComponent,
            },
            {
                path: 'change/password',
                component: CustomerChangePasswordComponent,
            },
            {
                path: 'notifications',
                component: NotificationListComponent,
            },
        ]
    }
];
