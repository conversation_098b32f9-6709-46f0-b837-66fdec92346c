import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { RestResponse } from 'src/shared/auth.model';
import { CommonService } from 'src/services/common.service';

import { NavController } from '@ionic/angular';
import {ShipmentData, ClientShipmentItem } from 'src/modals/shipping-info';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { AuthService } from 'src/shared/authservice';

// Shipment status options with color mapping
const SHIPMENT_STATUS_OPTIONS = [
  { id: 'NEW', name: 'New', color: 'red' },
  { id: 'ASSIGNED', name: 'Assigned', color: 'red' },
  { id: 'IN_TRANSIT', name: 'In Transit', color: 'yellow' },
  { id: 'COMPLETED', name: 'Completed', color: 'green' },
  { id: 'DELIVERED', name: 'Delivered', color: 'green' },
  { id: 'CANCELLED', name: 'Cancelled', color: 'green' }
];

interface CalendarShipment {
  id: string;
  refId: string;
  title: string;
  time: string;
  status: string;
  statusColor: string;
  customerName: string;
  from: string;
  to: string;
  etd: string;
}
@Component({
  selector: 'app-client-calendar',
  templateUrl: './client-calendar.component.html',
  styleUrls: ['./client-calendar.component.scss'],
  standalone: false
})
export class ClientCalendarComponent implements OnInit, OnDestroy  {

currentDate: Date = new Date();
weeks: Date[][] = [];
selectedDate: Date | null = null;

// Shipment data
allShipments: ClientShipmentItem[] = [];
shipmentsByDate: { [key: string]: CalendarShipment[] } = {};
selectedDateShipments: CalendarShipment[] = [];

// Modal properties
isModalOpen: boolean = false;
selectedShipment: CalendarShipment | null = null;

// Special request data for editing
specialRequestData: any = {
  isOversize: false,
  isRushRequest: false,
  isEnclosed: false,
  isFragile: false,
  isPerishable: false,
  isDangerousGoods: false
};

// Calculation details for editing
calculationDetails: any = {
  totalItems: 0,
  totalWeight: 0,
  totalVolume: 0,
  grandTotal: 0
};

private subscription = new Subscription();

constructor(
  private readonly navController: NavController,
  private readonly dataService: DataService,
  private readonly loadingService: LoadingService,
  private readonly toastService: ToastService,
  public readonly commonService: CommonService,
  private readonly localStorageService: LocalStorageService,
  private readonly authService: AuthService) {
        this.generateCalendar();
  }

ngOnInit() {
  this.selectedDate = new Date();
  this.loadShipments();
}
ngOnDestroy(): void {
  this.subscription.unsubscribe();
}

private formatMonthForAPI(date: Date): string {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  return `${year}-${month}`;
}
ionViewWillEnter() {
  this.currentDate = new Date();
  this.selectedDate = new Date();
  this.generateCalendar();
  this.loadShipments();
}
loadShipments(): void {
  this.loadingService.show();

  const monthFilter = this.formatMonthForAPI(this.currentDate);
  const apiPayload = {
    filtering: {
      month: monthFilter
    }
  };

  this.subscription.add(
    this.dataService.getClientCalendarShipments(apiPayload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const data = response.data;
        if (Array.isArray(data)) {
          this.allShipments = this.transformApiDataToUIModel(data);
          this.organizeShipmentsByDate();
          
          if (!this.selectedDate) {
            this.selectedDate = new Date();
          }
          
          this.updateSelectedDateShipments();
          
          setTimeout(() => {
            this.updateSelectedDateShipments();
          }, 100);
        } else {
          this.toastService.show('Failed to load shipments');
        }
      },
      error: (error: any) => {
        this.loadingService.hide();
        console.error('Calendar API Error:', error);
        this.toastService.show(error.message || 'Failed to load calendar data');
        // Reset data on error
        this.allShipments = [];
        this.shipmentsByDate = {};
        this.selectedDateShipments = [];
      }
    })
  );
}

  private transformApiDataToUIModel(apiData: ShipmentData[]): any[] {
    return apiData.map((item, index) => {
      const transformedItem = {
        id: item.id,
        refId: item.refID,
        paymentType: this.formatPaymentType(item.paymentType),
        from: item.pickupAddressDetail?.city || 'N/A',
        to: item.deliveryAddressDetail?.city || 'N/A',
        etd: item.etd || item.createdOn || '',
        createdOn: item.createdOn,
        formattedEtd: this.commonService.formatDate(item.etd || item.createdOn),
        status: this.formatStatus(item.status),
        originalStatus: item.status,
        customerName: item.customerUserDetail?.fullName || 'N/A',
        grandTotal: item.grandTotal || 0,
        shipmentType: item.shipmentType || 'N/A',
        step: item.step,
        isCargoAdded: item.isCargoAdded || false
      };

      return transformedItem;
    });
  }

  private formatPaymentType(paymentType: string): string {
    switch (paymentType?.toUpperCase()) {
      case 'CASH':
        return 'Cash';
      case 'CARD':
        return 'Card';
      case 'ONLINE':
        return 'Online';
      default:
        return paymentType || 'N/A';
    }
  }

  private formatStatus(status: string): string {
    switch (status?.toUpperCase()) {
      case 'NEW':
        return 'New';
      case 'ASSIGNED':
        return 'Assigned';
      case 'IN_TRANSIT':
        return 'In Transit';
      case 'COMPLETED':
        return 'Completed';
      case 'DELIVERED':
        return 'Delivered';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status || 'Unknown';
    }
  }

  private organizeShipmentsByDate(): void {
    this.shipmentsByDate = {};

    this.allShipments.forEach((shipment, index) => {
      const dateToUse = shipment.etd || (shipment as any).createdOn;

      if (dateToUse && dateToUse.trim() !== '') {
        const parsedDate = this.parseETDDate(dateToUse);

        if (parsedDate) {
          const dateKey = this.format(parsedDate);

          if (!this.shipmentsByDate[dateKey]) {
            this.shipmentsByDate[dateKey] = [];
          }

          const calendarShipment: CalendarShipment = {
            id: shipment.id,
            refId: shipment.refId,
            title: this.getShipmentTitle(shipment),
            time: this.getShipmentTime(shipment),
            status: shipment.status,
            statusColor: this.getStatusColor(shipment.originalStatus || shipment.status),
            customerName: shipment.customerName,
            from: shipment.from,
            to: shipment.to,
            etd: dateToUse
          };

          this.shipmentsByDate[dateKey].push(calendarShipment);
        }
      }
    });

    const datesWithShipments = Object.keys(this.shipmentsByDate);
    datesWithShipments.forEach(dateKey => {
      const shipments = this.shipmentsByDate[dateKey];
      const statusCounts = shipments.reduce((acc, ship) => {
        acc[ship.statusColor] = (acc[ship.statusColor] || 0) + 1;
        return acc;
      }, {} as any);
    });
  }

  private parseETDDate(etd: string): Date | null {
    if (!etd || etd.trim() === '') {
      return null;
    }

    try {
      const cleanEtd = etd.trim();
      let date = new Date(cleanEtd);
      if (!isNaN(date.getTime())) {
        return date;
      }

      if (cleanEtd.includes('-')) {
        const parts = cleanEtd.split(/[T,]/)[0].trim();
        const dateParts = parts.split('-');

        if (dateParts.length === 3) {
          let year, month, day;

          if (dateParts[0].length === 4) {
            year = parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1;
            day = parseInt(dateParts[2]);
          } else if (dateParts[2].length === 4) {
            day = parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1;
            year = parseInt(dateParts[2]);
          } else {
            day = parseInt(dateParts[0]);
            month = parseInt(dateParts[1]) - 1;
            year = 2000 + parseInt(dateParts[2]);
          }

          const parsedDate = new Date(year, month, day);
          return parsedDate;
        }
      }

      if (cleanEtd.includes('/')) {
        const parts = cleanEtd.split(/[T,]/)[0].trim();
        const dateParts = parts.split('/');

        if (dateParts.length === 3) {
          const month = parseInt(dateParts[0]) - 1;
          const day = parseInt(dateParts[1]);
          const year = parseInt(dateParts[2]);

          const parsedDate = new Date(year, month, day);
          return parsedDate;
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  private getShipmentTitle(shipment: ClientShipmentItem): string {
    const status = shipment.originalStatus || shipment.status;
    switch (status.toUpperCase()) {
      case 'NEW':
      case 'ASSIGNED':
        return 'Shipment Pickup';
      case 'IN_TRANSIT':
        return 'Shipment In Transit';
      case 'COMPLETED':
      case 'DELIVERED':
        return 'Shipment Delivered';
      default:
        return 'Shipment';
    }
  }

  private getShipmentTime(shipment: ClientShipmentItem): string {
    return `${shipment.refId} • ${shipment.from} → ${shipment.to}`;
  }

  private getStatusColor(status: string): string {
    const statusOption = SHIPMENT_STATUS_OPTIONS.find(
      option => option.id.toUpperCase() === status.toUpperCase()
    );
    return statusOption ? statusOption.color : 'green';
  }

  private updateSelectedDateShipments(): void {
    if (this.selectedDate) {
      const dateKey = this.format(this.selectedDate);
      this.selectedDateShipments = this.shipmentsByDate[dateKey] || [];
      console.log('Updated selected date shipments:', {
        selectedDate: this.selectedDate,
        dateKey: dateKey,
        shipmentsCount: this.selectedDateShipments.length,
        allShipmentsCount: this.allShipments.length,
        shipmentsByDateKeys: Object.keys(this.shipmentsByDate)
      });
    } else {
      this.selectedDateShipments = [];
      console.log('No selected date, cleared shipments');
    }
  }

  getIconColor(statusColor: string): string {
    switch (statusColor) {
      case 'red':
        return '#f44336';
      case 'yellow':
        return '#ffc107';
      case 'green':
        return '#4caf50';
      default:
        return '#000000';
    }
  }

  generateCalendar() {
    const firstDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
    const lastDay = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);

    const weeks: Date[][] = [];
    let currentWeek: Date[] = [];

    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());

    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      currentWeek.push(new Date(d));
      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
    }

    this.weeks = weeks;
  }

  format(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const formatted = `${year}-${month}-${day}`;
    return formatted;
  }

  getStatus(date: Date): string[] {
    const dateKey = this.format(date);
    const dayShipments = this.shipmentsByDate[dateKey] || [];

    if (dayShipments.length === 0) {
      return [];
    }

    const statusColors = new Set<string>();
    dayShipments.forEach(shipment => {
      statusColors.add(shipment.statusColor);
    });

    const uniqueColors = Array.from(statusColors);
    return uniqueColors;
  }

  isSameMonth(date: Date): boolean {
    return date.getMonth() === this.currentDate.getMonth();
  }

  isSelected(date: Date): boolean {
    return !!this.selectedDate && date.toDateString() === this.selectedDate?.toDateString();
  }

  selectDate(date: Date) {
    this.selectedDate = new Date(date);
    this.updateSelectedDateShipments();
  }

  refreshSchedule(): void {
    if (this.selectedDate) {
      this.updateSelectedDateShipments();
    } else {
      this.selectedDate = new Date();
      this.updateSelectedDateShipments();
    }
  }

  // Pull to refresh functionality
  doRefresh(event: any): void {
    console.log('Pull to refresh triggered');

    const monthFilter = this.formatMonthForAPI(this.currentDate);
    const apiPayload = {
      filtering: {
        month: monthFilter
      }
    };

    console.log('Pull to refresh API payload:', apiPayload);

    this.subscription.add(
      this.dataService.getClientCalendarShipments(apiPayload).subscribe({
        next: (response: RestResponse) => {
          console.log('Pull to refresh API response:', response);

          const data = response.data;
          if (Array.isArray(data)) {
            console.log('Processing refresh data, count:', data.length);

            this.allShipments = this.transformApiDataToUIModel(data);
            this.organizeShipmentsByDate();

            // Ensure selectedDate is set
            if (!this.selectedDate) {
              this.selectedDate = new Date();
              console.log('Set selectedDate to today:', this.selectedDate);
            }

            // Update the selected date shipments
            this.updateSelectedDateShipments();

            // Add a small delay to ensure UI updates
            setTimeout(() => {
              this.updateSelectedDateShipments();
              console.log('Final refresh update completed');
            }, 100);

          } else {
            console.log('Invalid data format received:', data);
            this.toastService.show('Failed to load shipments');
          }
          event.target.complete();
        },
        error: (error: any) => {
          console.error('Pull-to-refresh API Error:', error);
          this.toastService.show(error.message || 'Failed to refresh calendar data');
          event.target.complete();
        }
      })
    );
  }

  goToPreviousMonth(): void {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(this.currentDate.getMonth() - 1);
    this.currentDate = newDate;
    this.generateCalendar();
    this.loadShipments();
  }

  goToNextMonth(): void {
    const newDate = new Date(this.currentDate);
    newDate.setMonth(this.currentDate.getMonth() + 1);
    this.currentDate = newDate;
    this.generateCalendar();
    this.loadShipments();
  }

  showApiResponse(): void {
    this.loadingService.show();

    const monthFilter = this.formatMonthForAPI(this.currentDate);
    const apiPayload = {
      filtering: {
        month: monthFilter
      }
    };

    this.dataService.getClientCalendarShipments(apiPayload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const summary = {
          status: response.status,
          message: response.message,
          dataType: typeof response.data,
          isArray: Array.isArray(response.data),
          count: response.data?.length || 0,
          firstShipment: response.data?.[0] || null
        };

        alert(`API Response Summary:\n\nStatus: ${summary.status}\nMessage: ${summary.message}\nData Type: ${summary.dataType}\nIs Array: ${summary.isArray}\nCount: ${summary.count}\n\nFirst Shipment ETD: ${summary.firstShipment?.etd}\nFirst Shipment Status: ${summary.firstShipment?.status}`);
      },
      error: (error) => {
        this.loadingService.hide();
        alert(`API Error: ${error.message || 'Unknown error'}`);
      }
    });
  }

  // Modal functionality
  openShipmentDetails(shipment: CalendarShipment): void {
    // First set the selected shipment
    this.selectedShipment = shipment;

    // Then open the modal with a slight delay to ensure data is ready
    setTimeout(() => {
      this.isModalOpen = true;
    }, 50);
  }

  closeModal(): void {
    this.isModalOpen = false;

    // Clear the selected shipment after modal is closed
    setTimeout(() => {
      this.selectedShipment = null;
    }, 300);
  }

  // Check if shipment is editable (NEW or ASSIGNED status)
  isShipmentEditable(status: string): boolean {
    const editableStatuses = ['NEW', 'ASSIGNED'];
    return editableStatuses.includes(status?.toUpperCase());
  }

  // Navigate to edit shipment page (following the same pattern as client shipping listing)
  editShipment(): void {
    if (this.selectedShipment) {
      // Close the modal first
      this.closeModal();

      // Remove existing local storage details and fetch shipment data for editing
      this.removeLocalStorageDetails();
      this.getShipmentById(this.selectedShipment.id);
    }
  }

  // Get shipment by ID for editing (same as client shipping listing)
  getShipmentById(id: string): void {
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getCustomerShipmentById(id).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          this.saveShipmentDetails(data);
          this.saveCalculationDetails(data);
          this.saveSpecialRequestDetails(data);
          this.saveDocuments(data);

          // Set the shipping tab as active
          this.commonService.activeTab = 'shipping';

          // Navigate to basic info page for editing
          this.navController.navigateForward(`/client/portal/basic/info`, {
            animated: true
          });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to load shipment details');
        }
      })
    );
  }

  // Save shipment details to local storage for editing
  saveShipmentDetails(data: any): void {
    this.localStorageService.setObject("SHIPPING_INFO", data);
    this.localStorageService.set("SHIPPING_MODE", "edit");
  }

  // Save calculation details to local storage
  saveCalculationDetails(data: any): void {
    this.calculationDetails.totalItems = data.totalItems;
    this.calculationDetails.totalWeight = data.totalWeight;
    this.calculationDetails.totalVolume = data.totalVolume;
    this.calculationDetails.grandTotal = data.grandTotal;

    this.localStorageService.setObject("SHIPPING_CALCULATION_DETAILS", this.calculationDetails);
  }

  // Save special request details to local storage
  saveSpecialRequestDetails(data: any): void {
    this.specialRequestData.isOversize = data.isOversize;
    this.specialRequestData.isRushRequest = data.isRushRequest;
    this.specialRequestData.isEnclosed = data.isEnclosed;
    this.specialRequestData.isFragile = data.isFragile;
    this.specialRequestData.isPerishable = data.isPerishable;
    this.specialRequestData.isDangerousGoods = data.isDangerousGoods;

    this.localStorageService.setObject("SPECIAL_REQUEST", this.specialRequestData);
  }

  // Save documents to local storage
  saveDocuments(data: any): void {
    this.localStorageService.setObject("SHIPPING_DOCUMENTS", data.documents);
  }

  // Remove local storage details before editing
  removeLocalStorageDetails(): void {
    this.localStorageService.remove("CUSTOMER_SHIPPING_PICKUP_DETAILS");
    this.localStorageService.remove("SPECIAL_REQUEST");
    this.localStorageService.remove("SHIPPING_CALCULATION_DETAILS");
    this.localStorageService.remove("SHIPPING_DOCUMENTS");
    this.localStorageService.remove("CARGO_MODE");
    this.localStorageService.remove("CARGO_DETAILS");
  }

}
