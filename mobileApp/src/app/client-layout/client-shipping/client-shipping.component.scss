 .client-shipping-tab-page {
     --background: white;
     height: 100%;

     // Dropdown styling to match basic-info component
     .site-form-control {
         width: 100%;
         margin-bottom: 10px;

         ion-input,
         ion-select {
             font-size: 13px;
             --padding-start: 0px !important;
             --padding-end: 0px;
             --padding-top: 2px;
             --padding-bottom: 0px;
             font-weight: 500;
             min-height: 50px !important;
             width: 100%;
         }

         ion-select::part(icon) {
             display: none;
         }

         .dropdown-arrow-icon {
             margin-top: 19px;
             font-size: 16px;
         }

         .start-icon {
             margin-right: 13px;
             color: black;
             width: 18px;
             height: 18px;
         }

         .search-ion-input {
             font-size: 14px;
             --padding-start: 0px !important;
             --padding-end: 0px;
             --padding-top: 0px;
             --padding-bottom: 0px;
             font-weight: 500;
             min-height: 50px !important;
             width: 100%;
         }
     }

     .add-shipping-container {
         display: flex;
         justify-content: space-between;
         align-items: center;
         background: #fcd616;
         color: black;
         border-radius: 18px;
         padding: 14px 12px;
         gap: 5px;
         position: relative;
         overflow: hidden;
         cursor: pointer;
         transition: all 0.2s ease;
         user-select: none;

         //  &:hover {
         //      background: #333;
         //      transform: translateY(-1px);
         //      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
         //  }

         &:active {
             transform: translateY(0);
             box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
         }

         .add-shipping-icon {
             height: 22px;
             width: 32px;
         }

         .add-shipping-text {
             font-size: 15px;
             font-weight: 600;
         }

         ion-ripple-effect {
             color: rgba(255, 255, 255, 0.3);
         }
     }

     // Fixed search section styling
     .fixed-search.common-page-search {
         padding: 0px 20px 0px 20px;
         background: white;

         .padding-top {
             padding-top: 10px;
         }
     }

     .client-shipping-tab-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 295px); // Adjusted for additional dropdown
         padding: 10px 20px 10px 20px !important;
         overflow-y: auto;

         .shipping-tabs-items-container {
             display: flex;
             justify-content: center;
             align-items: center;
             padding-top: 2px;
             border-radius: 14px;
             background: #F9F9F9;

             .shipping-tabs-items {
                 display: flex;
                 border-radius: 20px;
                 border: 3px solid #F9F9F9;

                 .shipping-tab-item {
                     //  width: 160px;
                     width: 105px;
                     text-align: center;
                     padding: 12px 0;
                     font-size: 13px;
                     font-weight: 600;
                     color: #29385b;
                     cursor: pointer;
                     position: relative;
                     transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease;
                     border-radius: 14px;
                     overflow: hidden;
                     user-select: none;

                     &:hover {
                         background: rgba(255, 234, 0, 0.1);
                         transform: translateY(-1px);
                     }

                     &:active {
                         transform: translateY(0);
                     }

                     &.active {
                         border-top: 3px solid #FFEA00;
                         border-bottom: 3px solid #FFEA00;
                         background: #FFEA00;
                         color: #000;
                         font-weight: 700;
                     }

                     ion-ripple-effect {
                         color: transparent;
                     }
                 }
             }
         }

         .shipping-action-list {
             display: flex;
             overflow-x: auto;
             padding: 10px 0;
             gap: 12px; // smaller gap for tighter scroll
             margin-top: 12px;

             &::-webkit-scrollbar {
                 display: none;
             }

             .shipping-action-item {
                 flex: 0 0 auto;
                 display: flex;
                 align-items: center;
                 justify-content: flex-start;
                 padding: 0px 15px 0px 0px;
                 background-color: #f8f8f8;
                 border-radius: 14px;
                 cursor: pointer;
                 gap: 10px;

                 .icon-container {
                     background-color: #FFEA00;
                     border-radius: 50%;
                     padding: 10px;
                     display: flex;
                     align-items: center;
                     justify-content: center;
                 }

                 ion-icon {
                     font-size: 18px;
                     color: #000;
                 }

                 span {
                     font-size: 13px;
                     font-weight: 600;
                     color: #000;
                     white-space: nowrap;
                 }
             }
         }

         .shipping-list-wrapper {
             height: calc(100% - 53px);
             overflow: auto;
         }

         .shipping-card {
             background-color: #fff;
             border-radius: 18px;
             padding: 14px 16px;
             margin-bottom: 16px;
             box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
             position: relative;
             overflow: hidden;

             &.active {
                 background-color: #FFEA00;
             }

             &.highlighted-shipping {
                 background-color: #f0f8ff !important; // Ensure it overrides default styles
                 border: 2px solid #4caf50 !important; // Highlighting styles
             }

             .status-container {
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
                 margin-left: var(--margin-left, 10px);

                 .status {
                     font-size: 12px;
                 }

                 .action-icons {
                     // display: flex;
                     //  gap: 10px;

                     ion-icon {
                         font-size: 18px;
                         color: #000;
                         cursor: pointer;
                         position: relative;

                         &:hover {
                             background: rgba(0, 0, 0, 0.1);
                             transform: scale(1.1);
                         }

                         &:active {
                             transform: scale(0.95);
                         }

                         &.edit-icon {
                             color: #000;

                             ion-ripple-effect {
                                 color: rgba(0, 0, 0, 0.2);
                             }
                         }

                         &.delete-icon {
                             color: red;

                             ion-ripple-effect {
                                 color: rgba(255, 0, 0, 0.2);
                             }
                         }
                     }
                 }
             }

             .shipping-card-header {
                 display: flex;
                 justify-content: space-between;
                 align-items: start;
                 margin-top: 7px;
                 position: relative;
                 z-index: 1;

                 .ref-id {
                     font-size: 12px;
                     font-weight: 500;
                     color: #444;

                     strong {
                         font-weight: 700;
                         font-size: 13px;
                         color: #000;
                     }

                     &.shipping-status {
                         text-align: right;
                     }
                 }
             }

             .complete-shipping-card-header {
                 display: flex;
                 justify-content: space-between;
                 font-size: 12px;
                 font-weight: 500;
                 color: #444;

                 .complete-shipping {
                     display: flex;
                     flex-direction: column;
                 }

                 strong {
                     font-weight: 700;
                     font-size: 13px;
                     color: #000;
                     text-align: center;
                 }
             }

             .payment-type-container {
                 display: flex;
                 justify-content: space-between;

                 .payment-type {
                     //  flex: 1;
                     font-size: 12px;
                     position: relative;
                     z-index: 1;

                     &.complete-payment-type {
                         text-align: center;
                     }

                     strong {
                         font-weight: 700;
                     }
                 }
             }

             .customer-info {
                 margin-bottom: 12px;
                 padding: 8px 12px;
                 background: #f8f9fa;
                 border-radius: 8px;
                 position: relative;
                 z-index: 1;

                 .customer-name {
                     font-size: 12px;
                     color: #666;
                     line-height: 1.4;

                     strong {
                         color: #000;
                         font-size: 13px;
                         font-weight: 600;
                     }
                 }
             }

             .shipping-details {
                 display: flex;
                 align-items: center;
                 justify-content: space-between;

                 .location-timeline {
                     display: flex;
                     flex-direction: column;
                     align-items: end;
                     justify-content: center;
                     //   position: absolute;
                     width: 100%;
                     top: 0;
                     bottom: 0;
                     height: 100%;
                     z-index: 0;
                     /* left: -10px; */
                     right: 6px;

                     .dot {
                         width: 16px;
                         height: 16px;
                         display: flex;
                         align-items: center;
                         justify-content: center;
                         border-radius: 50%;
                     }

                     .top-dot ion-icon,
                     .bottom-dot ion-icon {
                         width: 14px;
                         height: 14px;
                     }

                     .status-progression {
                         display: flex;
                         flex-direction: row;
                         align-items: center;
                         justify-content: space-between;
                         padding: 2px 0px;
                         position: relative;
                         z-index: 2;
                         width: 100%;
                         min-height: auto;

                         .status-step {
                             display: flex;
                             /* flex-direction: column; */
                             align-items: center;
                             flex: 1;
                             position: relative;
                             width: 100%;
                             min-width: 88px;

                             .status-icon {
                                 width: 20px;
                                 height: 20px;
                                 border-radius: 50%;
                                 background: white;
                                 display: flex;
                                 align-items: center;
                                 justify-content: center;
                                 border: 2px solid #ddd;
                                 opacity: 0.5;
                                 z-index: 3;
                                 position: relative;

                                 ion-icon {
                                     font-size: 14px;
                                 }

                                 &.active {
                                     opacity: 1;
                                     transform: scale(1.2);
                                 }

                                 &.completed {
                                     opacity: 1;

                                 }
                             }

                             .connecting-line {
                                 height: 2px;
                                 width: 100%;
                                 margin-top: 4px;
                                 border-top: 2px dotted #ddd;

                                 &.completed {
                                     border-top: 2px solid #28a745;
                                 }
                             }

                             &:last-child .connecting-line {
                                 display: none;
                             }
                         }
                     }

                     &.status-new .vertical-line {
                         border-color: #dcdcdc;
                         border-style: dotted;
                     }

                     &.status-assigned .vertical-line {
                         border-color: #28a745;
                         border-style: solid;
                     }

                     &.status-in-transit .vertical-line {
                         border-color: #28a745;
                         border-style: solid;
                     }

                     &.status-completed .vertical-line {
                         border-color: #28a745;
                         border-style: solid;
                     }
                 }
             }

             .locations {
                 display: flex;
                 flex-direction: column;
                 z-index: 2;

                 .from,
                 .to {
                     font-size: 13px;

                     strong {
                         font-weight: 700;
                         color: #000;
                         display: -webkit-box;
                         -webkit-box-orient: vertical;
                         overflow: auto;
                         text-overflow: ellipsis;
                         word-break: break-word;
                         //   width: 115px;
                     }
                 }

                 .from-container {
                     display: flex;
                     justify-content: space-between;
                 }

                 .pickup-detail-container {
                     display: flex;
                     justify-content: flex-start;
                     gap: 14px;
                     font-weight: 500;
                     color: #000;

                     .phone-container {
                         display: flex;
                         align-items: center;
                         gap: 3px;

                         .phone-icon {
                             max-width: 15px;
                         }

                         .phone-text {
                             color: #007bff;
                             text-decoration: underline;
                             font-size: 12px;
                         }
                     }
                 }
             }

             .shipping-footer {
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
                 position: relative;
                 z-index: 1;
                 text-align: center;

                 .etd {
                     font-size: 12px;
                     font-weight: 500;

                     strong {
                         color: #000;
                     }

                     &.customer-details {
                         display: flex;
                         flex-direction: column;
                     }
                 }

                 .pickup-address-title {
                     font-size: 13px;
                     color: blue;
                     text-decoration: underline;
                 }

                 .status-tag {
                     font-size: 12px;
                     background-color: #FFEA00;
                     padding: 12px 22px;
                     border-radius: 8px;
                     font-weight: 600;
                     color: #000;
                 }

                 .status-container {
                     font-size: 11px;
                     background-color: #FFEA00;
                     padding: 8px 15px;
                     border-radius: 8px;
                     font-weight: 600;
                     color: #000;
                     margin-top: 15px;
                     display: flex;
                     gap: 5px;
                     align-items: center;
                     justify-content: center;

                     ion-icon {
                         font-size: 15px;
                         color: #000;
                     }
                 }

                 .total-amount {
                     font-size: 12px;
                     font-weight: 500;
                     text-align: right;

                     strong {
                         color: #000;
                         font-weight: 700;
                         font-size: 13px;
                     }
                 }

                 .action-button {
                     display: flex;
                     align-items: center;
                     gap: 8px;
                     background-color: #000;
                     color: white;
                     padding: 8px 16px;
                     border-radius: 20px;
                     font-size: 12px;
                     font-weight: 600;
                     cursor: pointer;
                     transition: all 0.3s ease;
                     margin-top: 10px;
                     position: relative;
                     overflow: hidden;
                     user-select: none;
                     box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);

                     &:hover {
                         background-color: #333;
                         transform: translateY(-2px);
                         box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                     }

                     &:active {
                         transform: translateY(-1px);
                         box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
                     }

                     .action-icon {
                         display: flex;
                         align-items: center;
                         justify-content: center;

                         ion-icon {
                             font-size: 16px;
                             color: white;
                         }
                     }

                     span {
                         white-space: nowrap;
                     }

                     ion-ripple-effect {
                         color: rgba(255, 255, 255, 0.3);
                     }
                 }
             }
         }

         .pagination-wrapper {
             display: flex;
             justify-content: center;
             align-items: center;
             gap: 8px;
             padding: 5px 0;

             .arrow-icon {
                 color: #000;
                 cursor: pointer;
                 border-radius: 50%;
                 width: 20px;
                 height: 20px;
                 display: flex;
                 justify-content: center;
                 align-items: center;
                 display: flex;
                 background: white;
                 padding: 6px;
                 border: 3px solid #f5f5f5;
                 box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);

                 &.arrow-image {
                     width: 38px;
                     height: 38px;
                 }
             }

             .page-number-container {
                 display: flex;
                 background: white;
                 padding: 4px;
                 border-radius: 23px;
                 border: 2px solid #f5f5f5;
                 box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
             }

             .page-number {
                 width: 30px;
                 height: 30px;
                 border-radius: 50%;
                 /* background-color: #f5f5f5; */
                 display: flex;
                 justify-content: center;
                 align-items: center;
                 font-weight: 500;
                 cursor: pointer;
                 color: #000;

                 &.active {
                     background-color: #FFEA00;
                     color: #000;
                     border-radius: 10px;
                     font-weight: bold;
                 }
             }
         }
     }

     // Filter Styles
     .search-actions {
         display: flex;
         gap: 10px;
         align-items: center;

         .filter-button {
             background-color: #FFEA00;
             border-radius: 50%;
             width: 45px;
             height: 45px;
             display: flex;
             align-items: center;
             justify-content: center;
             position: relative;
             cursor: pointer;

             .filter-icon {
                 font-size: 20px;
                 color: #000;
             }
         }

         .add-shipping-container {
             flex: 1;
         }
     }

     // Filter Modal Styles (matching calendar modal design)
     .modal-overlay {
         position: fixed;
         top: 0;
         left: 0;
         width: 100%;
         height: 100%;
         background-color: rgba(0, 0, 0, 0.5);
         display: flex;
         justify-content: center;
         align-items: flex-end;
         z-index: 10000;
         animation: fadeIn 0.3s ease-in-out;

         .filter-modal {
             background: white;
             width: 100%;
             max-height: 80vh; // Dynamic height - max 80% of viewport
             min-height: 60vh; // Minimum height for smaller devices
             height: auto; // Auto height based on content
             border-radius: 20px 20px 0 0;
             box-shadow: 0 -10px 25px rgba(0, 0, 0, 0.2);
             animation: slideUp 0.3s ease-out;
             overflow: hidden;
             display: flex;
             flex-direction: column;

             // Responsive height adjustments
             @media (max-height: 600px) {
                 max-height: 85vh; // Larger percentage for smaller screens
                 min-height: 70vh;
             }

             @media (min-height: 900px) {
                 max-height: 70vh; // Smaller percentage for larger screens
                 min-height: 50vh;
             }

             .modal-header {
                 background: #FFEA00;
                 padding: 15px 20px;
                 display: flex;
                 justify-content: space-between;
                 align-items: center;
                 border-bottom: 1px solid #e0e0e0;

                 h3 {
                     margin: 0;
                     color: black;
                     font-weight: 600;
                     font-size: 18px;
                 }

                 ion-button {
                     --color: black;
                     --padding-start: 8px;
                     --padding-end: 8px;

                     ion-icon {
                         color: black;
                         font-size: 24px;
                     }
                 }
             }

             .modal-content {
                 flex: 1;
                 display: flex;
                 flex-direction: column;
                 overflow: hidden;

                 .filter-content {
                     flex: 1;
                     padding: 15px 20px 5px 20px;
                     overflow-y: auto;

                     .filter-item {
                         display: flex;
                         flex-direction: column;
                         align-items: flex-start;
                         padding: 12px 0;
                         //  border-bottom: 1px solid #f0f0f0;

                         &:last-child {
                             border-bottom: none;
                         }

                         .filter-label {
                             font-size: 16px;
                             color: #666;
                             font-weight: 500;
                             margin-bottom: 8px;
                             text-transform: capitalize;
                         }

                         .filter-select-container {
                             width: 100%;

                             .filter-select {
                                 --placeholder-color: #333;
                                 font-size: 16px;
                                 color: #333;
                                 font-weight: 600;
                                 width: 100%;
                             }
                         }

                         .loading-text {
                             color: #666;
                             font-size: 14px;
                             font-style: italic;
                             margin-top: 4px;
                         }

                         .data-count {
                             color: #28a745;
                             font-size: 12px;
                             font-weight: 500;
                             margin-top: 4px;
                         }

                         .error-text {
                             color: #dc3545;
                             font-size: 12px;
                             font-weight: 500;
                             margin-top: 4px;
                         }

                         // Custom dropdown styles
                         .custom-select-container {
                             position: relative;
                             width: 100%;
                         }

                         .custom-select {
                             display: flex;
                             align-items: center;
                             justify-content: space-between;
                             padding: 12px 16px;
                             border: 1px solid #ddd;
                             border-radius: 8px;
                             background: white;
                             cursor: pointer;
                             min-height: 48px;

                             .select-text {
                                 flex: 1;
                                 font-size: 13px;
                                 color: #333;

                                 &.placeholder {
                                     color: #999;
                                     font-style: normal;
                                 }
                             }

                             .dropdown-icon {
                                 color: #666;
                                 font-size: 16px;
                                 transition: transform 0.3s ease;

                                 &.rotated {
                                     transform: rotate(180deg);
                                 }
                             }

                             .select-icon {
                                 color: #666;
                                 font-size: 16px;
                             }

                             &:hover {
                                 border-color: #007bff;
                             }
                         }

                         .custom-dropdown {
                             position: absolute;
                             top: 100%;
                             left: 0;
                             right: 0;
                             background: white;
                             border: 1px solid #ddd;
                             border-radius: 8px;
                             box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                             z-index: 1000;
                             max-height: 200px; // Reduced height (was 300px)
                             overflow: hidden;

                             // For bottom dropdowns, open upward
                             &.dropdown-up {
                                 top: auto;
                                 bottom: 100%;
                                 margin-bottom: 4px; // Small gap between button and dropdown
                             }

                             .dropdown-search {
                                 --background: #f8f9fa;
                                 --border-radius: 0;
                                 margin: 0;
                                 padding: 8px;
                             }

                             .simple-scroll-list {
                                 height: 120px;
                                 overflow-y: auto;
                                 overflow-x: hidden;
                                 background: white;
                                 border: none;
                                 margin: 0;
                                 padding: 0;

                                 // Simple scrolling
                                 -webkit-overflow-scrolling: touch;

                                 // Visible scrollbar
                                 &::-webkit-scrollbar {
                                     width: 6px;
                                 }

                                 &::-webkit-scrollbar-thumb {
                                     background: #007bff;
                                     border-radius: 3px;
                                 }

                                 &::-webkit-scrollbar-track {
                                     background: #f1f1f1;
                                 }
                             }

                             .scroll-item {
                                 padding: 10px 16px;
                                 border-bottom: 1px solid #eee;
                                 cursor: pointer;
                                 background: white;
                                 min-height: 40px;
                                 display: flex;
                                 align-items: center;

                                 &:hover {
                                     background: #f8f9fa;
                                 }

                                 &:active {
                                     background: #e9ecef;
                                 }

                                 &:last-child {
                                     border-bottom: none;
                                 }

                                 &.no-results {
                                     color: #999;
                                     font-style: italic;
                                     cursor: default;

                                     &:hover {
                                         background: white;
                                     }
                                 }
                             }

                             // Always show scrollbar
                             scrollbar-width: thin;
                             scrollbar-color: #007bff #f8f9fa;

                             // Webkit scrollbar styling
                             &::-webkit-scrollbar {
                                 width: 8px;
                                 background: #f8f9fa;
                             }

                             &::-webkit-scrollbar-track {
                                 background: #f8f9fa;
                                 border-radius: 4px;
                             }

                             &::-webkit-scrollbar-thumb {
                                 background: #007bff;
                                 border-radius: 4px;
                                 border: 1px solid #f8f9fa;

                                 &:hover {
                                     background: #0056b3;
                                 }
                             }

                             .dropdown-item {
                                 padding: 12px 16px;
                                 min-height: 40px;
                                 font-size: 14px;
                                 border-bottom: 1px solid #f0f0f0;
                                 cursor: pointer;
                                 display: flex;
                                 align-items: center;
                                 background: white;
                                 transition: background-color 0.2s;

                                 &:hover {
                                     background-color: #f8f9fa;
                                 }

                                 &:active {
                                     background-color: #e9ecef;
                                 }

                                 &:last-child {
                                     border-bottom: none;
                                 }
                             }
                         }
                     }

                     .filter-select-container {
                         position: relative;
                     }
                 }
             }

             .filter-actions {
                 display: flex;
                 gap: 12px;
                 padding: 20px;
                 background: white;
                 flex-shrink: 0;
                 margin-bottom: env(safe-area-inset-bottom, 0px);

                 ion-button {
                     --border-radius: 12px;
                     height: 50px;
                     font-weight: 600;
                     text-transform: uppercase;
                     font-size: 14px;
                     letter-spacing: 0.5px;
                     flex: 1;

                     // Apply button (primary/black) - like NEXT button
                     &[color="primary"] {
                         --background: #000000;
                         --color: #ffffff;
                         --border-color: #000000;
                         --border-width: 0px;
                     }

                     // Clear button (outline/white) - like CANCEL button
                     &[fill="outline"] {
                         --background: #ffffff;
                         --color: #000000;
                         --border-color: #000000;
                         --border-width: 1px;
                         --border-style: solid;
                     }
                 }
             }
         }
     }
 }

 @keyframes fadeIn {
     from {
         opacity: 0;
     }

     to {
         opacity: 1;
     }
 }

 @keyframes slideUp {
     from {
         transform: translateY(100%);
         opacity: 0;
     }

     to {
         transform: translateY(0);
         opacity: 1;
     }
 }