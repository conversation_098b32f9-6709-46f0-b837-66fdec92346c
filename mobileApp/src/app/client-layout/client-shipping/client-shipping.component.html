<ion-content class="client-shipping-tab-page">
  <app-customer-header [innerPage]="true" [headingText]="'Shipment Management'" [rightAction]="true"
    [backUrl]="'/client/portal/dashboard'" [rightActionIcon]="'funnel-outline'"
    (rightActionCallback)="onHeaderRightAction()" [hideBellIcon]="true"></app-customer-header>

  <div class="fixed-search common-page-search">
    <div class="padding-top">
      <ion-item class="site-form-control" lines="none">
        <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
        <ion-input label="Search by Ref ID/ Status" labelPlacement="floating" name="searchHere" [debounce]="500"
          [(ngModel)]="searchTerm" (ionInput)="onSearchChange($event)"></ion-input>
      </ion-item>
    </div>
    <div class="add-shipping-container secondary-ripple" (click)="add()">
      <ion-icon class="add-shipping-icon" src="assets/images/svg/black-add-icon.svg" slot="start"></ion-icon>
      <span class="add-shipping-text">ADD</span>
      <ion-ripple-effect></ion-ripple-effect>
    </div>
  </div>

  <div class="client-shipping-tab-body-section">
    <div class="shipping-tabs-items-container">
      <div class="shipping-tabs-items">
        <div class="shipping-tab-item primary-ripple" [ngClass]="{'active': filter?.tabType === 'PENDING'}"
          (click)="onChangeStatusTab('PENDING')">
          Assigned
          <ion-ripple-effect></ion-ripple-effect>
        </div>
        <div class="shipping-tab-item margin-left-5 primary-ripple"
          [ngClass]="{'active': filter.tabType === 'IN_TRANSIT_TAB'}" (click)="onChangeStatusTab('IN_TRANSIT_TAB')">
          In Transit
          <ion-ripple-effect></ion-ripple-effect>
        </div>
        <div class="shipping-tab-item primary-ripple" [ngClass]="{'active': filter?.tabType === 'COMPLETED'}"
          (click)="onChangeStatusTab('COMPLETED')">
          Completed
          <ion-ripple-effect></ion-ripple-effect>
        </div>
      </div>
    </div>
    <div class="shipping-list-wrapper">
      <!-- Empty State -->
      <div class="empty-state" *ngIf="shippingList.length <= 0">
        <div class="empty-content">
          <ion-icon name="cube-outline" class="empty-icon"></ion-icon>
          <!-- <h3>No Shipments Found</h3> -->
          <p>
            {{
            filter.tabType === 'PENDING' ? 'No pending shipments found' :
            filter.tabType === 'IN_TRANSIT_TAB' ? 'No in-transit shipments found' :
            'No completed shipments found'
            }}
          </p>
        </div>
      </div>

      <!-- Shipment Cards -->
      <div class="shipping-card margin-top-20" *ngFor="let item of shippingList"
        [ngClass]="{'highlighted-shipping': item.id === shippingNotificationId}">

        <div class="status-container" *ngIf="filter?.tabType === 'PENDING' || filter?.tabType === 'IN_TRANSIT_TAB'"
          [style.--margin-left]="getTimelineMargin(item)">
          <div class="status margin-bottom-3" (click)="openDetails(item)">
            <strong [style.color]="getStatusColor(item)">
              {{ item.status }}
            </strong>
          </div>
          <div class="action-icons margin-right-5" *ngIf="filter?.tabType === 'PENDING'">
            <ion-icon class="edit-icon dark-ripple" src="/assets/images/svg/edit-icon.svg" (click)="onEditClick(item)">
              <ion-ripple-effect></ion-ripple-effect>
            </ion-icon>
          </div>
        </div>

        <div class="shipping-details" (click)="openDetails(item)">
          <div class="location-timeline" *ngIf="filter.tabType === 'PENDING' || filter?.tabType === 'IN_TRANSIT_TAB'"
            [ngClass]="getTimelineClass(item)" [style.--timeline-height]="getTimelineHeight(item)">
            <!-- Status progression timeline -->
            <div class="status-progression">
              <!-- NEW Status -->
              <div class="status-step">
                <div class="status-icon" [class.active]="isCurrentStatus(item, 'NEW')"
                  [class.completed]="isStatusReached(item, 'NEW')">
                  <ion-icon [src]="getStepIcon('NEW', item)"></ion-icon>
                </div>
                <div class="connecting-line" [class.completed]="isStatusReached(item, 'ASSIGNED')"></div>
              </div>

              <!-- ASSIGNED Status -->
              <div class="status-step">
                <div class="status-icon" [class.active]="isCurrentStatus(item, 'ASSIGNED')"
                  [class.completed]="isStatusReached(item, 'ASSIGNED')">
                  <ion-icon [src]="getStepIcon('ASSIGNED', item)"></ion-icon>
                </div>
                <div class="connecting-line" [class.completed]="isStatusReached(item, 'IN_TRANSIT')"></div>
              </div>

              <!-- IN_TRANSIT Status -->
              <div class="status-step">
                <div class="status-icon" [class.active]="isCurrentStatus(item, 'IN_TRANSIT')"
                  [class.completed]="isStatusReached(item, 'IN_TRANSIT')">
                  <ion-icon [src]="getStepIcon('IN_TRANSIT', item)"></ion-icon>
                </div>
                <div class="connecting-line" [class.completed]="isStatusReached(item, 'COMPLETED')"></div>
              </div>

              <!-- COMPLETED Status -->
              <div class="status-step">
                <div class="status-icon" [class.active]="isCurrentStatus(item, 'COMPLETED')"
                  [class.completed]="isStatusReached(item, 'COMPLETED')">
                  <ion-icon [src]="getStepIcon('COMPLETED', item)"></ion-icon>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="shipping-card-header" *ngIf="filter.tabType === 'PENDING' || filter?.tabType === 'IN_TRANSIT_TAB'"
          (click)="openDetails(item)">
          <div class="ref-id">
            Ref. Id<br /><strong>{{ item.refId }}</strong>
          </div>
          <!-- <div class="shipping-footer margin-right-10"
            *ngIf="filter?.tabType === 'PENDING' || filter?.tabType === 'IN_TRANSIT_TAB'">
            <div class="etd margin-bottom-15"><strong [style.color]="getStatusColor(item)">{{ item.status
                }}</strong></div>
          </div> -->
          <div class="shipping-footer" *ngIf="item.customerName && item.customerName !== 'N/A'">
            <div class="etd customer-details margin-bottom-15">Customer
              <strong>{{ item.customerName }}</strong>
            </div>
          </div>
        </div>

        <div class="complete-shipping-card-header" *ngIf="filter?.tabType === 'COMPLETED'" (click)="openDetails(item)">
          <div class="complete-shipping">
            <span>Ref. Id</span>
            <strong>{{ item.refId }}</strong>
          </div>
          <div class="complete-shipping">
            <strong [style.color]="getStatusColor(item)">{{ item.status }}</strong>
            <strong *ngIf="item.customerName && item.customerName !== 'N/A'">
              {{ item.customerName }}
            </strong>
          </div>
        </div>

        <div class="payment-type-container" (click)="openDetails(item)">
          <div class="payment-type margin-bottom-10 margin-top-10">
            Payment Type<br />
            <strong>{{ item.paymentType }}</strong>
          </div>
          <div class="payment-type complete-payment-type margin-bottom-10 margin-top-10"
            [ngClass]="filter.tabType === 'COMPLETED' ? 'margin-right-15' : 'margin-right-10'"
            *ngIf="item.grandTotal > 0">
            Total<br />
            <strong>${{ item.grandTotal.toFixed(2) }}</strong>
          </div>
        </div>

        <div class="locations">
          <div class="from">
            <div class="from-container" (click)="openDetails(item)">
              From
            </div>
            <div class="pickup-detail-container"
              *ngIf="filter?.tabType === 'PENDING' || filter?.tabType === 'COMPLETED'">
              <span (click)="openDetails(item)">{{ item.pickupContactPersonName }}</span>
              <div class="phone-container" *ngIf="item.pickupContactPersonPhone">
                <img src="/assets/images/icons/phone-icon.png" class="phone-icon">
                <a class="phone-text" [href]="'tel:' + item.pickupContactPersonPhone"
                  [style.color]="getStatusColor(item)">
                  {{ commonService.formatPhoneForDisplay(item.pickupContactPersonPhone) }}
                </a>
              </div>
            </div>
            <strong (click)="openDetails(item)">{{ item.from }}</strong>
          </div>
          <div class="to margin-top-10" (click)="openDetails(item)">To<br />
            <div class="pickup-detail-container"
              *ngIf="filter?.tabType === 'IN_TRANSIT_TAB' || filter?.tabType === 'COMPLETED'">
              <span (click)="openDetails(item)">{{ item.deliveryContactPersonName }}</span>
              <div class="phone-container" *ngIf="item.deliveryContactPersonPhone">
                <img src="/assets/images/icons/phone-icon.png" class="phone-icon">
                <a class="phone-text" [href]="'tel:' + item.deliveryContactPersonPhone"
                  [style.color]="getStatusColor(item)">
                  {{ commonService.formatPhoneForDisplay(item.deliveryContactPersonPhone) }}
                </a>
              </div>
            </div>
            <strong (click)="openDetails(item)">{{ item.to }}</strong>
          </div>
        </div>

        <div class="shipping-footer">
          <div class="etd" (click)="openDetails(item)">
            {{ filter.tabType === 'PENDING' || filter.tabType === 'IN_TRANSIT_TAB' ? 'ETD' : 'Delivered On' }}<br />
            <strong>{{ item.etd }}</strong>
          </div>
          <!-- <div class="total-amount" *ngIf="item.grandTotal > 0">
            Total<br /><strong>${{ item.grandTotal.toFixed(2) }}</strong>
          </div> -->
          <div class="action-button secondary-ripple" (click)="handleShipmentAction(item)">
            <div class="action-icon">
              <ion-icon [name]="getActionIcon(item)" slot="start"></ion-icon>
            </div>
            <span>{{ getActionText(item) }}</span>
            <ion-ripple-effect></ion-ripple-effect>
          </div>
        </div>
      </div>

    </div>
  </div>

  <!-- Filter Modal -->
  <div class="modal-overlay" *ngIf="showFilterModal" (click)="closeFilterModal()">
    <div class="filter-modal" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h3>Filter Shipments</h3>
        <ion-button fill="clear" (click)="closeFilterModal()">
          <ion-icon name="close-outline"></ion-icon>
        </ion-button>
      </div>

      <div class="modal-content">
        <div class="filter-content" (click)="onModalContentClick($event)">
          <!-- Pickup City Filter -->
          <div class="filter-item">
            <div class="filter-label">Pickup City</div>
            <div class="filter-select-container">
              <div class="custom-select" (click)="togglePickupCityDropdown()">
                <div class="select-text" [class.placeholder]="!selectedPickupCity">
                  {{ selectedPickupCity ? (selectedPickupCity.name + (selectedPickupCity.province ? ', ' +
                  selectedPickupCity.province : '')) : 'Pickup City' }}
                </div>
                <ion-icon name="chevron-down" class="select-icon"></ion-icon>
              </div>

              <!-- Custom Dropdown -->
              <div class="custom-dropdown" *ngIf="showPickupCityDropdown">
                <ion-searchbar placeholder="Search cities..." [(ngModel)]="pickupCitySearchTerm"
                  (ionInput)="filterPickupCities($event)" class="dropdown-search">
                </ion-searchbar>
                <div class="simple-scroll-list">
                  <div *ngFor="let city of filteredPickupCities; trackBy: trackByCity" (click)="selectPickupCity(city)"
                    class="scroll-item">
                    {{ city.name }}{{ city.province ? ', ' + city.province : '' }}
                  </div>
                  <div *ngIf="filteredPickupCities.length === 0" class="scroll-item no-results">
                    No cities found
                  </div>
                </div>
              </div>
            </div>
            <div class="error-text" *ngIf="!loadingCities && cities.length === 0">No cities found</div>
          </div>

          <!-- Delivery City Filter -->
          <div class="filter-item">
            <div class="filter-label">Delivery City</div>
            <div class="filter-select-container">
              <div class="custom-select" (click)="toggleDeliveryCityDropdown()">
                <div class="select-text" [class.placeholder]="!selectedDeliveryCity">
                  {{ selectedDeliveryCity ? (selectedDeliveryCity.name + (selectedDeliveryCity.province ? ', ' +
                  selectedDeliveryCity.province : '')) : 'Select Delivery City' }}
                </div>
                <ion-icon name="chevron-down" class="select-icon"></ion-icon>
              </div>

              <!-- Custom Dropdown -->
              <div class="custom-dropdown" *ngIf="showDeliveryCityDropdown">
                <ion-searchbar placeholder="Search cities..." [(ngModel)]="deliveryCitySearchTerm"
                  (ionInput)="filterDeliveryCities($event)" class="dropdown-search">
                </ion-searchbar>
                <div class="simple-scroll-list">
                  <div *ngFor="let city of filteredDeliveryCities; trackBy: trackByCity"
                    (click)="selectDeliveryCity(city)" class="scroll-item">
                    {{ city.name }}{{ city.province ? ', ' + city.province : '' }}
                  </div>
                  <div *ngIf="filteredDeliveryCities.length === 0" class="scroll-item no-results">
                    No cities found
                  </div>
                </div>
              </div>
            </div>
            <div class="error-text" *ngIf="!loadingCities && cities.length === 0">No cities found</div>
          </div>

          <!-- Shipment Type Filter -->
          <div class="filter-item">
            <div class="filter-label">Shipment Type</div>
            <div class="custom-select-container">
              <div class="custom-select" (click)="toggleShipmentTypeDropdown()">
                <span class="select-text" [class.placeholder]="!selectedShipmentType">{{ selectedShipmentType?.label ||
                  'Select Shipment Type' }}</span>
                <ion-icon name="chevron-down-outline" class="dropdown-icon"
                  [class.rotated]="showShipmentTypeDropdown"></ion-icon>
              </div>

              <!-- Custom Dropdown -->
              <div class="custom-dropdown" *ngIf="showShipmentTypeDropdown">
                <div class="simple-scroll-list">
                  <div *ngFor="let type of shipmentTypes; trackBy: trackByShipmentType"
                    (click)="selectShipmentType(type)" class="scroll-item">
                    {{ type.label }}
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>

        <!-- Filter Actions - Fixed at bottom -->
        <div class="filter-actions">
          <ion-button expand="block" color="primary" (click)="applyFilters()">
            APPLY
          </ion-button>
          <ion-button expand="block" fill="outline" color="medium" (click)="clearFiltersAndReload()">
            CLEAR
          </ion-button>
        </div>

      </div>
    </div>
  </div>

</ion-content>