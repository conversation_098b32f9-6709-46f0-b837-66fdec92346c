import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { SpecialRequestStates } from 'src/modals/cargoDetail';
import { CommonService } from 'src/services/common.service';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';

@Component({
  selector: 'app-customer-calculation',
  templateUrl: './customer-calculation.component.html',
  styleUrls: ['./customer-calculation.component.scss'],
  standalone: false
})
export class CustomerCalculationComponent implements OnInit {

  specialRequestData: SpecialRequestStates = new SpecialRequestStates();
  shipmentData: any;
  backUrl!: string;
  activeSubscriptions: Subscription = new Subscription();
  calculationDetails: {
    totalItems: number | null;
    totalWeight: number | null;
    totalVolume: number | null;
    grandTotal: number | null;
  } = {
      totalItems: null,
      totalWeight: null,
      totalVolume: null,
      grandTotal: null,
    };
  shippingDetails: any;
  user: any;

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    public commonService: CommonService,
    public readonly authService: AuthService
  ) {

  }

  ngOnInit(): void {
    const shippingData = this.localStorageService.getObject("SPECIAL_REQUEST");
    this.specialRequestData = shippingData ? SpecialRequestStates.fromResponse(shippingData) : new SpecialRequestStates();
  }

  ionViewWillEnter(): void {
    this.user = this.authService.getUser();
    this.shippingDetails = this.localStorageService.getObject("OTHER_SHIPMENT_DETAILS");

    const shippingData = this.localStorageService.getObject("SPECIAL_REQUEST");
    this.specialRequestData = shippingData ? SpecialRequestStates.fromResponse(shippingData) : new SpecialRequestStates();

    this.route.queryParams.subscribe(params => {
      this.shipmentData = params['shipmentData'] || null;
    });

    const storedCalculation = this.localStorageService.getObject("SHIPPING_CALCULATION_DETAILS");
    if (storedCalculation) {
      this.calculationDetails.totalItems = storedCalculation.totalItems ?? null;
      this.calculationDetails.totalWeight = storedCalculation.totalWeight ?? null;
      this.calculationDetails.totalVolume = storedCalculation.totalVolume ?? null;
      this.calculationDetails.grandTotal = storedCalculation.grandTotal ?? null;
    }

    this.backUrl = `/client/portal/special/request?shipmentData=${this.shipmentData}`;
  }

  nextButton() {
    this.navController.navigateForward('/client/portal/add/documents', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

  cancel() {
    this.navController.navigateBack('/client/portal/special/request', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

  goToShippingList() {
    this.navController.navigateRoot('/client/portal/shipping', { animated: true });
  }

}
