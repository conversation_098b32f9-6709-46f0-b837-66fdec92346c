<ion-content class="client-calculation-page">
  <app-customer-header [innerPage]="true" [headingText]="'Customer Calculation'" [rightAction]="false"
    [backUrl]="backUrl" [homeAction]="true" [hideBellIcon]="true"
    (homeActionCallback)="goToShippingList()"></app-customer-header>

  <div class="client-calculation-body-section">

    <div class="shipment-detail-container margin-top-20">
      <div class="shipment-detail">
        <span class="shipment-label">Ref Id</span>
        <span class="shipment-value">{{ shippingDetails?.refID }}</span>
      </div>
      <div class="shipment-detail">
        <span class="shipment-label">Customer</span>
        <span class="shipment-value">
          {{
          authService.isDriver()
          ? (shippingDetails?.customerUserDetail?.fullName || 'N/A')
          : (user?.firstName + " " + user?.lastName || 'N/A')
          }}
        </span>
      </div>
      <div class="shipment-detail" *ngIf="authService.isDriver() && shippingDetails?.contactPersonPhone">
        <span class="shipment-label">Customer Phone</span>
        <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(shippingDetails?.contactPersonPhone)
          }}</span>
      </div>
      <div class="shipment-detail" *ngIf="authService.isCustomer() && user?.phoneNumber">
        <span class="shipment-label">Customer Phone</span>
        <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(user?.phoneNumber)
          }}</span>
      </div>
    </div>

    <div class="margin-top-20">
      <span class="info-text">Cargo Details</span>
    </div>

    <div class="cargo-detail-card">
      <div class="cargo-detail-left">
        <div class="detail-row margin-top-15">
          <span class="label">Total Items</span>
          <span class="value">{{ calculationDetails.totalItems }}</span>
        </div>
        <div class="detail-row margin-top-15">
          <span class="label">Total Weight</span>
          <span class="value">{{ calculationDetails.totalWeight }}</span>
        </div>
        <div class="detail-row margin-top-15">
          <span class="label">Total Volume</span>
          <span class="value">{{ calculationDetails.totalVolume }}</span>
        </div>
        <div class="detail-row margin-top-15">
          <span class="label">Grand Total</span>
          <span class="value">{{ calculationDetails.grandTotal }}</span>
        </div>
      </div>
      <div class="cargo-detail-right margin-left-15">
        <img src="assets/images/svg/calculation-icon.svg" alt="Cargo Box">
      </div>
    </div>

    <!-- Buttons -->
    <div class="shippment-btn-container">
      <ion-button class="margin-top-35 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
        type="submit" (click)="cancel()">
        <span>Back</span>
        <ion-ripple-effect></ion-ripple-effect>
      </ion-button>
      <ion-button class="margin-top-35 site-button ship-submit-btn interactive-button" expand="full" shape="round"
        type="submit" (click)="nextButton()">
        <span>Next</span>
        <ion-ripple-effect></ion-ripple-effect>
      </ion-button>
    </div>

  </div>
</ion-content>