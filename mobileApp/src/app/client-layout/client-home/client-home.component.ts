import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { IonInput, NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import { RestResponse } from 'src/shared/auth.model';
import { SpecialRequestStates } from 'src/modals/cargoDetail';
import { BarcodeScannerService } from 'src/services/barcode-scanner.service';

@Component({
  selector: 'app-client-home',
  templateUrl: './client-home.component.html',
  styleUrls: ['./client-home.component.scss'],
  standalone: false,
  encapsulation: ViewEncapsulation.None
})
export class ClientHomeComponent implements OnInit, OnDestroy {

  user: any;
  private subscriptions = new Subscription();
  isLogoutModalOpen: boolean = false;
  dashboardData: any;
  isRefIdSelectionPopupOpen: boolean = false;
  searchTerm: string | null = null;
  availableShipments: any[] = [];
  selectedShipment: any;
  isModalOpen: boolean = false;
  specialRequestData: SpecialRequestStates = new SpecialRequestStates();
  calculationDetails: {
    totalItems: number | null;
    totalWeight: number | null;
    totalVolume: number | null;
    grandTotal: number | null;
  } = {
      totalItems: null,
      totalWeight: null,
      totalVolume: null,
      grandTotal: null,
    };
  fromScanTrigger: boolean = false;
  @ViewChild('refIdInput') refIdInput!: IonInput;

  constructor(
    private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly authService: AuthService,
    private readonly dataService: DataService,
    public commonService: CommonService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly barcodeService: BarcodeScannerService
  ) {
  }

  ngOnInit() {

  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();
    this.searchTerm = null;
    this.availableShipments = [];
    this.getDashboardData();
  }

  getDashboardData(): void {
    this.loadingService.show();
    this.subscriptions.add(
      this.dataService.getCustomerDashboardData().subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          this.dashboardData = data;
        },
        error: (error) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  get newShipmentCount(): number {
    return this.dashboardData?.shipmentStatusCount?.find((s: { status: string; }) => s.status === 'NEW')?.count || 0;
  }

  get inAssignedShipmentCount(): number {
    return this.dashboardData?.shipmentStatusCount?.find((s: { status: string; }) => s.status === 'ASSIGNED')?.count || 0;
  }

  get InTransitShipmentCount(): number {
    return this.dashboardData?.shipmentStatusCount?.find((s: { status: string; }) => s.status === 'IN_TRANSIT')?.count || 0;
  }

  get completedShipmentCount(): number {
    return this.dashboardData?.shipmentStatusCount?.find((s: { status: string; }) => s.status === 'COMPLETED')?.count || 0;
  }

  goToShipments() {
    this.navController.navigateForward('/client/portal/shipping', {
      queryParams: {
        refresh: 'true',
        goTab: 'PENDING'
      }
    });
  }

  goToShipmentsOnInTransitStatus() {
    this.navController.navigateForward('/client/portal/shipping', {
      queryParams: {
        refresh: 'true',
        goTab: 'IN_TRANSIT_TAB'
      }
    });
  }

  goToShipmentsOnCompletedStatus() {
    this.navController.navigateForward('/client/portal/shipping', {
      queryParams: {
        refresh: 'true',
        goTab: 'COMPLETED'
      }
    });
  }

  async onScanBarcodeClick() {
    const scanned = await this.barcodeService.scanBarcode();
    if (scanned) {
      this.searchTerm = scanned;
      this.fromScanTrigger = true; // indicate that this is from a scan
      this.onSearchChange();
    }
  }

  onSearchChange() {
    if (!this.searchTerm) {
      return;
    }

    if (this.searchTerm.length > 2) {
      const searchPayload = {
        filtering: {
          searchText: this.searchTerm
        }
      };
      this.loadingService.show();
      this.subscriptions.add(
        this.dataService.searchCustomerShipment(searchPayload).subscribe({
          next: (response: RestResponse) => {
            this.loadingService.hide();

            const data = response.data;
            if (!data || (Array.isArray(data) && data.length === 0)) {
              this.searchTerm = null;
              this.toastService.show("No data found. Try a different search term.");
              return;
            }

            this.availableShipments = data;
            if (this.fromScanTrigger && data.length > 0) {
              this.onSelectShipment(data[0]); // Pass the first shipment only
            }

            this.fromScanTrigger = false;
          },
          error: (error) => {
            this.loadingService.hide();
            this.toastService.show(error.message);
          }
        })
      );
    }
  }

  openRefIdSelectionPopup() {
    this.isRefIdSelectionPopupOpen = true;
  }

  // Handle modal presentation completion for proper iOS focus
  onModalDidPresent() {
    // iOS-specific fix for cursor positioning
    setTimeout(async () => {
      if (this.refIdInput) {
        try {
          // Force a layout recalculation before focusing
          const element = await this.refIdInput.getInputElement();
          if (element) {
            // Trigger a reflow to fix iOS cursor positioning
            element.style.display = 'none';
            element.offsetHeight; // Force reflow
            element.style.display = '';

            // Set focus with additional delay for iOS
            setTimeout(async () => {
              await this.refIdInput.setFocus();
            }, 150);
          }
        } catch (error) {
          // Fallback focus without element manipulation
          setTimeout(async () => {
            await this.refIdInput.setFocus();
          }, 200);
        }
      }
    }, 100);
  }

  closeRefIdLocationSelectionPopup() {
    this.isRefIdSelectionPopupOpen = false;
    this.searchTerm = null;
    this.availableShipments = [];
  }

  onSelectShipment(shipment: any) {
    this.selectedShipment = {
      id: shipment.id,
      status: this.formatStatus(shipment.status),
      refId: shipment.refID,
      customerName: shipment.customerUserDetail?.fullName || '',
      from: shipment.pickupAddressDetail?.city || '',
      to: shipment.deliveryAddressDetail?.city || '',
      etd: shipment.etd
    };

    this.searchTerm = this.selectedShipment.refId;
    this.isModalOpen = true;
    setTimeout(() => {
      this.closeRefIdLocationSelectionPopup();
    }, 50);
  }

  // Close shipment details modal
  closeModal(): void {
    this.isModalOpen = false;

    setTimeout(() => {
      this.searchTerm = null;
      this.selectedShipment = null;
      this.availableShipments = [];
    }, 300);
  }

  formatStatus(status: string): string {
    switch (status?.toUpperCase()) {
      case 'NEW':
        return 'New';
      case 'ASSIGNED':
        return 'Assigned';
      case 'IN_TRANSIT':
        return 'In Transit';
      case 'COMPLETED':
        return 'Completed';
      case 'DELIVERED':
        return 'Delivered';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status || 'Unknown';
    }
  }

  editShipment(): void {
    if (this.selectedShipment) {
      this.closeModal(); // Close the modal first

      this.removeLocalStorageDetails();
      this.getShipmentById(this.selectedShipment.id);
    }
  }

  getShipmentById(id: string): void {
    this.loadingService.show();
    this.subscriptions.add(
      this.dataService.getCustomerShipmentById(id).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          this.saveShipmentDetails(data);
          this.saveCalculationDetails(data);
          this.saveSpecialRequestDetails(data);
          this.saveDocuments(data);

          // Set the shipping tab as active
          this.commonService.activeTab = 'shipping';

          this.navController.navigateForward(`/client/portal/basic/info`, {
            animated: true
          });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to load shipment details');
        }
      })
    );
  }

  saveShipmentDetails(data: any) {
    this.localStorageService.setObject("SHIPPING_INFO", data);
    this.localStorageService.set("SHIPPING_MODE", "edit");
  }

  saveCalculationDetails(data: any) {
    this.calculationDetails.totalItems = data.totalItems;
    this.calculationDetails.totalWeight = data.totalWeight;
    this.calculationDetails.totalVolume = data.totalVolume;
    this.calculationDetails.grandTotal = data.grandTotal;

    this.localStorageService.setObject("SHIPPING_CALCULATION_DETAILS", this.calculationDetails);
  }

  saveSpecialRequestDetails(data: any) {
    this.specialRequestData.isOversize = data.isOversize;
    this.specialRequestData.isRushRequest = data.isRushRequest;
    this.specialRequestData.isEnclosed = data.isEnclosed;
    this.specialRequestData.isFragile = data.isFragile;
    this.specialRequestData.isPerishable = data.isPerishable;
    this.specialRequestData.isDangerousGoods = data.isDangerousGoods;

    this.localStorageService.setObject("SPECIAL_REQUEST", this.specialRequestData);
  }

  saveDocuments(data: any) {
    this.localStorageService.setObject("SHIPPING_DOCUMENTS", data.documents);
  }

  removeLocalStorageDetails() {
    this.localStorageService.remove("CUSTOMER_SHIPPING_PICKUP_DETAILS");
    this.localStorageService.remove("SPECIAL_REQUEST");
    this.localStorageService.remove("SHIPPING_CALCULATION_DETAILS");
    this.localStorageService.remove("SHIPPING_DOCUMENTS");
    this.localStorageService.remove("CARGO_MODE");
    this.localStorageService.remove("CARGO_DETAILS");
  }

  // Check if shipment is editable (NEW or ASSIGNED status)
  isShipmentEditable(status: string): boolean {
    return status.toUpperCase() === 'NEW' || status.toUpperCase() === 'ASSIGNED';
  }

  openNotifications() {
    this.navController.navigateForward(`/client/portal/notifications`, {
      animated: true
    });
  }

  closeLogoutModal() {
    this.isLogoutModalOpen = false;
  }

  openLogoutModal() {
    this.isLogoutModalOpen = true;
  }

  logoutButton() {
    this.openLogoutModal();
  }

  async logout(): Promise<void> {
    this.closeLogoutModal();

    setTimeout(async () => {
      try {
        this.loadingService.show();

        const deviceId = this.localStorageService.get('device-uuid') || '';
        const logoutPayload = {
          deviceId: deviceId
        };
        await this.dataService.logout(logoutPayload).toPromise();

        this.authService.logout();

        this.loadingService.hide();
        await this.navController.navigateRoot('/account/login', {
          animated: true,
          animationDirection: 'back'
        });
        this.toastService.show('Logged out successfully');
      } catch (error) {
        this.loadingService.hide();

        this.authService.logout();
        await this.navController.navigateRoot('/account/login', {
          animated: true,
          animationDirection: 'back'
        });
      }
    }, 200); // Delay after modal close
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

}
