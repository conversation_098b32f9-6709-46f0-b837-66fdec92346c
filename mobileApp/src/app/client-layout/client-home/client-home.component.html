<ion-content class="home-page" [scrollY]="false">

    <div class="dashboard-header-section">
        <div class="dashboard-header-container">
            <div class="dashboard-icon-container">
                <div class="dashboard-user-info">
                    <span class="username">Hello,<strong> {{user?.firstName}} {{user?.lastName}} </strong> </span>
                    <!-- <span class="location">Location</span> -->
                </div>
                <div class="dashboard-icon-background margin-right-10" (click)="openNotifications()">
                    <ion-icon class="bell-icon" src="assets/images/svg/bell.svg"></ion-icon>
                </div>
                <div class="dashboard-icon-background" (click)="logoutButton()">
                    <ion-icon class="logout-icon" src="assets/images/svg/logout-icon.svg"></ion-icon>
                </div>
            </div>

            <div class="fixed-search home-page-tracking margin-top-15">
                <div class="padding-top" (click)="openRefIdSelectionPopup()">
                    <ion-item class="site-form-control search-form-control" lines="none">
                        <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
                        <ion-input label="Search by Ref ID" labelPlacement="floating" name="trackingNumber"
                            [debounce]="500" [(ngModel)]="searchTerm"></ion-input>
                    </ion-item>
                </div>
                <div class="tracking-container margin-right-10" (click)="onScanBarcodeClick()">
                    <ion-icon class="tracking-icon" src="assets/images/svg/barcode.svg"></ion-icon>
                </div>
            </div>
        </div>
    </div>

    <div class="home-page-body-section">
        <span class="logistics-text">Shipments</span>
        <div class="logistics-management-container">
            <!-- Left Column -->
            <div class="logistics-management">
                <div class="total-shipment-container" (click)="goToShipments()">
                    <ion-icon class="shipment-icon" src="assets/images/svg/ship-delivery.svg"></ion-icon>
                    <span class="shipment-text">New</span>
                    <span class="shipment-count">{{ newShipmentCount }}</span>
                </div>
                <div class="total-shipment-container complete-shipments" (click)="goToShipmentsOnInTransitStatus()">
                    <ion-icon class="shipment-icon" src="assets/images/svg/in-transit.svg"></ion-icon>
                    <span class="shipment-text">In Transit</span>
                    <span class="shipment-count">{{ InTransitShipmentCount }}</span>
                </div>
                <div class="total-shipment-container progress-shipments">
                    <ion-icon class="shipment-icon" src="assets/images/svg/ship-approve.svg"></ion-icon>
                    <span class="shipment-text">Total Shipment</span>
                    <span class="shipment-count">{{ dashboardData?.totalShipments || 0 }}</span>
                </div>
            </div>

            <!-- Right Column (offset down) -->
            <div class="logistics-management">
                <div class="total-shipment-container upcoming-shipments" (click)="goToShipments()">
                    <ion-icon class="shipment-icon" src="assets/images/svg/half-time.svg"></ion-icon>
                    <span class="shipment-text">Assigned</span>
                    <span class="shipment-count">{{ inAssignedShipmentCount }}</span>
                </div>
                <div class="total-shipment-container progress-shipments" (click)="goToShipmentsOnCompletedStatus()">
                    <ion-icon class="shipment-icon" src="assets/images/svg/sent.svg"></ion-icon>
                    <span class="shipment-text">Completed</span>
                    <span class="shipment-count">{{ completedShipmentCount }}</span>
                </div>
            </div>
        </div>

    </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #logoutModal [isOpen]="isLogoutModalOpen"
    [backdropDismiss]="false">
    <ng-template>
        <div class="site-custom-popup-container heading-setting">
            <div class="site-custom-popup-header no-padding-bottom padding-top-10">
                <i-feather name="X" (click)="closeLogoutModal()"></i-feather>
                <h1>Confirmation!</h1>
            </div>
            <div class="site-custom-popup-body popup-normal-heading ion-padding no-padding-top">
                <div class="custom-modal-text"><span>Are you sure you want to logout?</span>
                </div>
                <div class="main-modal-dismiss button-gap margin-top-20">
                    <ion-button class="site-full-rounded-button text-capitalize primary-button no-button" shape="round"
                        type="submit" (click)="closeLogoutModal()">No</ion-button>
                    <ion-button class="site-full-rounded-button text-capitalize primary-button yes-button" shape="round"
                        type="submit" (click)="logout()">Yes</ion-button>
                </div>
            </div>
        </div>
    </ng-template>
</ion-modal>

<ion-modal class="location-popup" #RefIdSelectionPopup [isOpen]="isRefIdSelectionPopupOpen" [backdropDismiss]="false"
    (ionModalDidPresent)="onModalDidPresent()">
    <ng-template>
        <ion-header>
            <ion-toolbar>
                <ion-title>Select Ref ID</ion-title>
                <ion-buttons slot="start">
                    <i-feather name="X" (click)="closeRefIdLocationSelectionPopup()"></i-feather>
                </ion-buttons>
            </ion-toolbar>
        </ion-header>
        <ion-content>
            <div class="ion-padding no-padding-bottom">
                <ion-item class="site-form-control" lines="none">
                    <i-feather class="map-pin-icon start-icon" name="map-pin" slot="start"></i-feather>
                    <ion-input #refIdInput label="Search by Ref ID" labelPlacement="floating" name="searchRefId"
                        [(ngModel)]="searchTerm" (ionInput)="onSearchChange()" [debounce]="500"></ion-input>
                </ion-item>
            </div>

            <div class="ref-id-text-container" *ngIf="!searchTerm || searchTerm.length === 0">
                <span class="ref-id-text">Search Ref ID using either 'shp-' or a 4-digit code (e.g., 0000).</span>
            </div>

            <div class="google-places-container">
                <ion-list *ngIf="availableShipments.length > 0" class="google-places">
                    <ion-item class="google-place" *ngFor="let shipment of availableShipments" lines="full"
                        (click)="onSelectShipment(shipment)">
                        <i-feather class="map-pin-icon" name="map-pin" slot="start"></i-feather>
                        <ion-label class="ion-text-wrap">
                            <div class="city-name">{{ shipment.refID }}</div>
                        </ion-label>
                    </ion-item>
                </ion-list>

                <div *ngIf="searchTerm && availableShipments.length === 0"
                    class="no-results-message ion-text-center ion-padding">
                    <ion-icon name="alert-circle-outline" style="font-size: 48px; color: #999;"></ion-icon>
                    <p>No Ref Id found matching "<strong>{{ searchTerm }}</strong>". Please try a different search.</p>
                </div>

            </div>
        </ion-content>
    </ng-template>
</ion-modal>

<!-- Shipment Details Modal -->
<div class="modal-overlay" *ngIf="isModalOpen" (click)="closeModal()">
    <div class="shipment-details-modal" (click)="$event.stopPropagation()">
        <div class="modal-header">
            <h3>Shipment Details</h3>
            <ion-button fill="clear" (click)="closeModal()">
                <ion-icon name="close-outline"></ion-icon>
            </ion-button>
        </div>

        <div class="modal-content" *ngIf="selectedShipment">
            <!-- Status -->
            <div class="detail-item status-item" *ngIf="selectedShipment.status">
                <div class="detail-value status-value">{{ selectedShipment.status }}</div>
            </div>

            <!-- Ref ID -->
            <div class="detail-item" *ngIf="selectedShipment.refId">
                <div class="detail-label">Ref</div>
                <div class="detail-value">{{ selectedShipment.refId }}</div>
            </div>

            <!-- Customer -->
            <!-- <div class="detail-item" *ngIf="selectedShipment.customerName">
                <div class="detail-label">Customer</div>
                <div class="detail-value">{{ selectedShipment.customerName }}</div>
            </div> -->

            <!-- From -->
            <div class="detail-item" *ngIf="selectedShipment.from">
                <div class="detail-label">From</div>
                <div class="detail-value">{{ selectedShipment.from }}</div>
            </div>

            <!-- To -->
            <div class="detail-item" *ngIf="selectedShipment.to">
                <div class="detail-label">To</div>
                <div class="detail-value">{{ selectedShipment.to }}</div>
            </div>

            <!-- ETD -->
            <div class="detail-item" *ngIf="selectedShipment.etd">
                <div class="detail-label">ETD</div>
                <div class="detail-value">{{ commonService.formatDisplayDate(selectedShipment.etd) }}</div>
            </div>

            <!-- Edit Button (only for NEW or ASSIGNED status) -->
            <div class="edit-button-container" *ngIf="isShipmentEditable(selectedShipment.status)">
                <ion-button class="site-button edit-button text-capitalize" expand="full" (click)="editShipment()">
                    Edit Shipment
                </ion-button>
            </div>
        </div>
    </div>
</div>