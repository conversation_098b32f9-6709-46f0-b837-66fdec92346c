 .home-page {
     --background: #FCFCFC;
     height: 100vh;
     overflow: hidden;
     display: flex;
     flex-direction: column;

     .dashboard-header-section {
         position: relative;

         .dashboard-header-container {
             display: flex;
             align-items: center;
             padding: 20px 15px;
             padding-top: calc(env(safe-area-inset-top) + 20px);
             background: #FFEA00;
             flex-direction: column;
             border-bottom-left-radius: 35px;
             border-bottom-right-radius: 35px;
         }

         .dashboard-icon-container {
             display: flex;
             align-items: center;
             justify-content: space-between;
             width: 94%;

             .dashboard-icon-background {
                 background-color: #fff;
                 width: 45px;
                 height: 45px;
                 border-radius: 50%;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 cursor: pointer;
                 transition: all 0.2s ease;

                 &:hover {
                     transform: scale(1.05);
                     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                 }

                 &:active {
                     transform: scale(0.95);
                 }

                 img {
                     width: 100%;
                     height: 100%;
                     object-fit: cover;
                     border-radius: 50%;
                 }

                 .bell-icon,
                 .logout-icon {
                     font-size: 20px;
                     color: #333;
                 }

                 .logout-icon {
                     color: #dc3545; // Red color for logout
                 }
             }

             .dashboard-user-info {
                 flex: 1;
                 //  margin: 0 12px;
                 display: flex;
                 flex-direction: column;
                 justify-content: center;

                 .username {
                     font-weight: 400;
                     font-size: 15px;
                     color: #000;
                 }

                 .location {
                     font-size: 12px;
                     color: #888;
                 }
             }
         }

         .tracking-container {
             display: flex;
             align-items: center;
             justify-content: center;

             .tracking-icon {
                 font-size: 24px;
                 border-radius: 65px;
                 padding: 10px;
                 background: black;
             }
         }

         // Center align search text in home page
         .home-page-tracking {
             .search-ion-input {
                 input {
                     text-align: center !important;
                 }

                 // Center the placeholder text
                 &::part(native) {
                     text-align: center !important;
                 }
             }
         }
     }

     .home-page-body-section {
         flex: 1;
         display: flex;
         flex-direction: column;
         height: calc(100vh - 220px);
         width: 100%;
         padding: 20px 15px 10px 15px !important;
         overflow-y: auto; // ✅ Enable vertical scrolling
         overflow-x: hidden;

         .logistics-text {
             font-size: 17px;
             font-weight: bold;
             margin-bottom: 20px;
         }

         .logistics-management-container {
             display: flex;
             gap: 15px;
             width: 100%;
             margin-top: 20px;
             flex: 1;
             align-items: stretch;

             .logistics-management {
                 display: flex;
                 flex-direction: column;
                 flex: 1;
                 gap: 15px;

                 &:last-child {
                     //  margin-top: 25px; // Right column offset
                 }
             }

             .total-shipment-container {
                 display: flex;
                 flex-direction: column;
                 background: white;
                 box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
                 border-radius: 25px;
                 padding: 25px 15px;
                 align-items: center;
                 justify-content: center;
                 gap: 8px;
                 min-height: 140px;
                 width: 100%;
                 box-sizing: border-box;
                 position: relative;

                 // All cards have consistent styling
                 &.upcoming-shipments,
                 &.progress-shipments,
                 &.complete-shipments {
                     padding: 20px 15px;
                     margin: 0;
                 }
             }

             .shipment-icon {
                 font-size: 50px;
                 margin-bottom: 8px;
             }

             .shipment-text {
                 font-size: 11px;
                 font-weight: 600;
                 text-align: center;
                 line-height: 1.2;
                 margin: 0 5px;
                 color: #333;
             }

             .shipment-count {
                 font-size: 24px;
                 font-weight: bold;
                 margin-top: 5px;
                 color: #000;
             }
         }

         // Responsive design for different screen sizes
         @media (max-width: 480px) {
             padding: 15px 10px 10px 10px !important;

             .logistics-management-container {
                 gap: 12px;
                 margin-top: 0px;

                 .logistics-management {
                     &:last-child {
                         //  margin-top: 20px; // Smaller offset on mobile
                     }
                 }

                 .total-shipment-container {
                     padding: 20px 12px;
                     min-height: 130px;
                 }

                 .shipment-icon {
                     font-size: 45px;
                 }

                 .shipment-text {
                     font-size: 10px;
                 }

                 .shipment-count {
                     font-size: 22px;
                 }
             }
         }

         @media (min-width: 768px) {
             .logistics-management-container {
                 gap: 20px;
                 margin-top: 25px;

                 .logistics-management {
                     &:last-child {
                         margin-top: 30px; // Larger offset on desktop
                     }
                 }

                 .total-shipment-container {
                     padding: 30px 20px;
                     min-height: 160px;
                 }

                 .shipment-icon {
                     font-size: 55px;
                 }

                 .shipment-text {
                     font-size: 12px;
                 }

                 .shipment-count {
                     font-size: 26px;
                 }
             }
         }
     }

     // iOS-specific fixes for modal input cursor positioning
     .location-popup {
         ion-modal {
             --backdrop-opacity: 0.4;
         }

         ion-content {
             --keyboard-offset: 0px;
         }

         .site-form-control {
             ion-input {
                 // Fix iOS cursor positioning in modal
                 --padding-top: 0px;
                 --padding-bottom: 0px;
                 min-height: 50px !important;

                 // Force proper input element positioning on iOS
                 input {
                     -webkit-appearance: none;
                     appearance: none;
                     line-height: normal;
                     vertical-align: baseline;
                 }
             }

             // iOS-specific input wrapper fixes
             .native-input {
                 line-height: 1.4 !important;
                 vertical-align: middle !important;
             }
         }

         // Ensure proper modal rendering on iOS
         &.ios {
             .modal-wrapper {
                 transform: translateZ(0);
                 -webkit-transform: translateZ(0);
             }
         }
     }
 }