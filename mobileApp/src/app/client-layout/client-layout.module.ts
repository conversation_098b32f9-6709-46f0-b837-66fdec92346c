import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { MaskitoDirective } from '@maskito/angular';
import { ImageCropperModule } from 'ngx-image-cropper';
import { SharedModuleModule } from 'src/shared/shared-module.module';
import { IconsModule } from 'src/shared/icon.module';
import { ClientLayoutComponent } from './client-layout.component';
import { CLIENTLAYOUTROUTING } from './client-layout.routing';
import { ClientHomeComponent } from './client-home/client-home.component';
import { ClientShippingComponent } from './client-shipping/client-shipping.component';
import { ClientQuotationComponent } from './client-quotation/client-quotation.component';
import { ClientQuotationBasicInfoComponent } from './client-quotation/add-quotation-details/client-quotation-basic-info/client-quotation-basic-info.component';
import { ClientQuotationDetailsComponent } from './client-quotation/client-quotation-details/client-quotation-details.component';
import { ClientCalendarComponent } from './client-calendar/client-calendar.component';
import { ClientAccountComponent } from './client-account/client-account.component';
import { CustomerCalculationComponent } from './client-shipping/customer-calculation/customer-calculation.component';
import { ClientShipmentDetailsComponent } from './client-shipment-details/client-shipment-details.component';
import { ClientQuotationPickupDeliveryComponent } from './client-quotation/add-quotation-details/client-quotation-pickup-delivery/client-quotation-pickup-delivery.component';
import { ClientQuotationDeliveryLocationComponent } from './client-quotation/add-quotation-details/client-quotation-delivery-location/client-quotation-delivery-location.component';
import { ClientQuotationCargoListComponent } from './client-quotation/add-quotation-details/client-quotation-cargo-list/client-quotation-cargo-list.component';
import { ClientQuotationAddCargoComponent } from './client-quotation/add-quotation-details/client-quotation-add-cargo/client-quotation-add-cargo.component';
import { ClientQuotationSpecialRequestComponent } from './client-quotation/add-quotation-details/client-quotation-special-request/client-quotation-special-request.component';
import { ClientQuotationCalculationComponent } from './client-quotation/add-quotation-details/client-quotation-calculation/client-quotation-calculation.component';
import { ClientQuotationAddDocumentsComponent } from './client-quotation/add-quotation-details/client-quotation-add-documents/client-quotation-add-documents.component';
import { ClientAccountAddressComponent } from './client-account/client-account-address/client-account-address.component';
import { CustomerChangePasswordComponent } from './client-account/customer-change-password/customer-change-password.component';

@NgModule({
  declarations: [
    ClientLayoutComponent,
    ClientHomeComponent,
    ClientShippingComponent,
    ClientQuotationComponent,
    ClientQuotationBasicInfoComponent,
    ClientQuotationDetailsComponent,
    ClientQuotationPickupDeliveryComponent,
    ClientQuotationDeliveryLocationComponent,
    ClientQuotationCargoListComponent,
    ClientQuotationAddCargoComponent,
    ClientQuotationSpecialRequestComponent,
    ClientQuotationCalculationComponent,
    ClientQuotationAddDocumentsComponent,
    ClientCalendarComponent,
    ClientAccountComponent,
    ClientAccountAddressComponent,
    CustomerCalculationComponent,
    ClientShipmentDetailsComponent,
    CustomerChangePasswordComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    MaskitoDirective,
    IconsModule,
    RouterModule.forChild(CLIENTLAYOUTROUTING),
    SharedModuleModule,
    ImageCropperModule
  ],
  providers: [],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ClientLayoutModule { }
