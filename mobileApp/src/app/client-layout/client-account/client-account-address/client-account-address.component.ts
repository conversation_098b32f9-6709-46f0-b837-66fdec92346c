import { Component, OnInit } from '@angular/core';
import { NavController, LoadingController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { AddressDetail, UserProfileDetail } from 'src/modals/user-profile-response';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { StatusBarService } from 'src/services/status-bar.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-client-account-address',
  templateUrl: './client-account-address.component.html',
  styleUrls: ['./client-account-address.component.scss'],
  standalone: false
})
export class ClientAccountAddressComponent implements OnInit {

  onClickValidation!: boolean;
  isNoAddressFoundPopupOpen: boolean = false;
  profileData: UserProfileDetail = new UserProfileDetail();
  subscription: Subscription = new Subscription();

  constructor(private readonly navController: NavController,
    public commonService: CommonService,
    private readonly localStorageService: LocalStorageService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private loadingService: LoadingService,
  ) {

  }

  ngOnInit() {
    this.onClickValidation = false;
    const profile = this.localStorageService.getObject("UPDATE_CUSTOMER_PROFILE");
    this.profileData = profile ? UserProfileDetail.fromResponse(profile) : new UserProfileDetail();
  }

  ionViewDidEnter() {
    const profile = this.localStorageService.getObject("UPDATE_CUSTOMER_PROFILE");
    this.profileData = profile ? UserProfileDetail.fromResponse(profile) : new UserProfileDetail();

    if (!this.profileData.addressDetail) {
      this.profileData.addressDetail = new AddressDetail();
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid || !this.profileData.isValidBasicRequest(form)) {
      return;
    }

    if (this.profileData.phoneNumber) {
      const phone = this.profileData.phoneNumber.trim();
      this.profileData.phoneNumber = this.commonService.formatPhoneNumberForApi(phone);
    }

    this.loadingService.show();
    this.subscription.add(
      this.dataService.updateUserProfile(this.profileData).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          this.toastService.show('Profile updated successfully!');
          this.localStorageService.remove("UPDATE_CUSTOMER_PROFILE");

          this.loadUserProfile();
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to update profile');
        }
      })
    );
  }

  loadUserProfile(): void {
    this.subscription.add(
      this.dataService.getUserProfile().subscribe({
        next: (response: RestResponse) => {

          const data = response.data;
          const storedUser = this.localStorageService.getObject('user');
          if (storedUser) {
            storedUser.firstName = data.firstName;
            storedUser.lastName = data.lastName;
            this.localStorageService.setObject('user', storedUser);
          }
          this.navController.navigateForward("/client/portal/dashboard", { animated: true });
        },
        error: (error) => {
        }
      })
    );
  }

  cancel() {
    this.navController.navigateBack("/client/portal/account", { animated: true });
  }

  changePassword() {
    this.navController.navigateForward("/client/portal/change/password", { animated: true });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
