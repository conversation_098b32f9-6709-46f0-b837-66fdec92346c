<ion-content class="client-profile-address-page">
  <app-customer-header [innerPage]="true" [headingText]="'Profile Management'" [rightAction]="false"
    [backUrl]="'/client/portal/account'" [hideBellIcon]="true"></app-customer-header>

  <div class="client-profile-address-page-body-section">
    <div class="margin-top-25">
      <span class="profile-text">Enter Profile Details</span>
    </div>

    <div class="form-container">
      <form class="custom-form" #profileForm="ngForm" novalidate (ngSubmit)="submitProfile(profileForm.form)">

        <!-- <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': companyName.invalid && onClickValidation}">
            <ion-input name="companyName" #companyName="ngModel" [(ngModel)]="profileData.customerDetail.companyName"
              required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Company Name"
              labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="companyName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div> -->

        <div class="margin-top-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':companyEmail.invalid && onClickValidation}">
            <ion-input #emailInput label="Company Email" labelPlacement="floating" required name="companyEmail"
              #companyEmail="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$"
              [(ngModel)]="profileData.customerDetail.accountsPayableEmail">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="companyEmail" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10" *ngIf="profileData?.addressDetail">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': address.invalid && onClickValidation}">
            <ion-input name="address" #address="ngModel" [(ngModel)]="profileData.addressDetail.address"
              required="required" mode="md" label="Address" labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="address" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>

        <div class="common-fields-container" *ngIf="profileData?.addressDetail">
          <div class="field-container small-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': city.invalid && onClickValidation}">
              <ion-input name="city" #city="ngModel" [(ngModel)]="profileData.addressDetail.city" required="required"
                maxlength="100" pattern="^(?!\s*$)[A-Za-zÀ-ÿ\s\-'\.]+$" mode="md" label="City"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="city" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': province.invalid && onClickValidation}">
              <ion-input name="province" #province="ngModel" [(ngModel)]="profileData.addressDetail.state"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-zÀ-ÿ\s\-'\.]+$" mode="md" label="Province"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="province" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>
        </div>

        <div class="common-fields-container" *ngIf="profileData?.addressDetail">
          <div class="field-container small-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': postalCode.invalid && onClickValidation}">
              <ion-input name="postalCode" #postalCode="ngModel" [(ngModel)]="profileData.addressDetail.pin" mode="md"
                label="Postal Code" labelPlacement="floating" maxlength="10" pattern="^[A-Za-z0-9\s\-]*$">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="postalCode" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Please enter a valid postal code.'">
            </app-validation-message>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': country.invalid && onClickValidation}">
              <ion-input name="country" #country="ngModel" [(ngModel)]="profileData.addressDetail.country"
                required="required" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Country"
                labelPlacement="floating">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="country" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </div>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn" expand="full" shape="round" type="button"
            (click)="cancel()">
            <span>Back</span>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn" expand="full" shape="round" type="submit">
            <span>Submit</span>
          </ion-button>
        </div>
      </form>
    </div>

    <div class="shippment-btn-container">
      <ion-button class="margin-top-25 margin-left-30 site-button ship-submit-btn chng-password" expand="full"
        shape="round" type="submit" (click)="changePassword()">
        <span>Change Password</span>
      </ion-button>
    </div>

  </div>
</ion-content>