import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { UserProfileDetail } from 'src/modals/user-profile-response';
import { RestResponse } from 'src/shared/auth.model';
import mask from 'src/shared/phone-number.mask';
import { NgModel } from '@angular/forms';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { DataService } from 'src/services/data.service';
import { AuthService } from 'src/shared/authservice';

@Component({
  selector: 'app-client-account',
  templateUrl: './client-account.component.html',
  styleUrls: ['./client-account.component.scss'],
  standalone: false
})
export class ClientAccountComponent implements OnInit, OnDestroy {

  onClickValidation!: boolean;
  profileData: UserProfileDetail = new UserProfileDetail();
  activeSubscriptions: Subscription = new Subscription();
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;
  displayPhoneNumber: string = '';
  isLogoutModalOpen: boolean = false;
  phoneInvalid: boolean = false;

  constructor(
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    public commonService: CommonService,
    private readonly localStorageService: LocalStorageService,
    private readonly dataService: DataService,
    private readonly authService: AuthService,
  ) {

  }

  ngOnInit() {
    this.clearFormData();
  }

  ionViewWillEnter() {
    this.clearFormData();
    this.loadUserProfile();
  }

  clearFormData() {
    this.onClickValidation = false;
    this.localStorageService.remove("UPDATE_CUSTOMER_PROFILE");
    this.profileData = new UserProfileDetail();
    this.displayPhoneNumber = '';
  }

  loadUserProfile(): void {
    this.loadingService.show();
    this.activeSubscriptions.add(
      this.dataService.getUserProfile().subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          this.profileData = data;

          if (this.profileData.phoneNumber) {
            const digits = this.profileData.phoneNumber.replace(/\D/g, '').slice(0, 10);
            this.profileData.phoneNumber = digits;
            this.displayPhoneNumber = this.commonService.formatPhoneForDisplay(digits);
          }
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to load profile');
        }
      })
    );
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    // Remove non-digits, limit to 10
    const digits = value.replace(/\D/g, '').slice(0, 10);

    // Format for display
    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.displayPhoneNumber = formatted;
    input.value = formatted;

    // Store only digits (no +1, no dashes, no spaces)
    this.profileData.phoneNumber = digits;

    // Only valid if exactly 10 digits
    this.phoneInvalid = digits.length !== 10;

    if (this.phoneInvalid) {
      userPhone.control.setErrors({ invalid: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }
    this.localStorageService.setObject("UPDATE_CUSTOMER_PROFILE", this.profileData);
    this.navController.navigateForward("/client/portal/address", { animated: true });
  }

  cancel() {
    this.navController.navigateRoot("/client/portal/dashboard", { animated: true });
  }

  changePassword() {
    this.navController.navigateForward("/client/portal/change/password", { animated: true });
  }

  closeLogoutModal() {
    this.isLogoutModalOpen = false;
  }

  openLogoutModal() {
    this.isLogoutModalOpen = true;
  }

  logoutButton() {
    this.openLogoutModal();
  }

  async logout(): Promise<void> {
    this.closeLogoutModal();

    setTimeout(async () => {
      try {
        this.loadingService.show();

        const deviceId = this.localStorageService.get('device-uuid') || '';
        const logoutPayload = {
          deviceId: deviceId
        };
        await this.dataService.logout(logoutPayload).toPromise();

        this.authService.logout();

        this.loadingService.hide();
        await this.navController.navigateRoot('/account/login', {
          animated: true,
          animationDirection: 'back'
        });
        this.toastService.show('Logged out successfully');
      } catch (error) {
        this.loadingService.hide();

        this.authService.logout();
        await this.navController.navigateRoot('/account/login', {
          animated: true,
          animationDirection: 'back'
        });
      }
    }, 200); // Delay after modal close
  }

  ngOnDestroy(): void {
    this.activeSubscriptions.unsubscribe();
  }

}
