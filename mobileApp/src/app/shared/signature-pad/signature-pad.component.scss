.signature-pad-container {
  width: 100%;
  margin: 16px 0;

  .canvas-container {
    position: relative;
    width: 100%;
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .signature-canvas {
      display: block;
      width: 100%;
      height: 200px;
      background: #ffffff;
      border-radius: 12px;
      cursor: crosshair;
      touch-action: none; // Prevent scrolling while drawing
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: #FFEA00 !important;
        box-shadow: 0 2px 8px rgba(255, 234, 0, 0.2);
      }

      &:hover {
        border-color: #ccc;
      }
    }

    .signature-placeholder {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #999;
      pointer-events: none;
      z-index: 1;

      .signature-icon {
        font-size: 32px;
        margin-bottom: 8px;
        color: #ccc;
      }

      p {
        margin: 0;
        font-size: 14px;
        font-weight: 400;
      }
    }
  }

  .signature-actions {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    margin-top: 12px;
    padding: 0 4px;

    ion-button {
      flex: 1;
      --border-radius: 8px;
      --padding-start: 12px;
      --padding-end: 12px;
      height: 36px;
      font-size: 13px;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      text-transform: uppercase;

      &:hover:not([disabled]) {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &:active:not([disabled]) {
        transform: translateY(0);
        transition: all 0.1s ease;
      }

      &[disabled] {
        opacity: 0.4;
        transform: none;
        box-shadow: none;
      }

      ion-icon {
        font-size: 16px;
        margin-right: 4px;
      }

      ion-ripple-effect {
        color: rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// Mobile optimizations
@media (max-width: 768px) {
  .signature-pad-container {
    .canvas-container {
      .signature-canvas {
        height: 180px; // Slightly smaller on mobile
      }
    }

    .signature-actions {
      gap: 8px;

      ion-button {
        font-size: 12px;
        height: 32px;
        --padding-start: 8px;
        --padding-end: 8px;
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .signature-pad-container {
    .canvas-container {
      .signature-canvas {
        background: #1e1e1e;
        border-color: #444;
      }

      .signature-placeholder {
        color: #888;

        .signature-icon {
          color: #666;
        }
      }
    }
  }
}

// High DPI displays
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {
  .signature-pad-container {
    .canvas-container {
      .signature-canvas {
        // Canvas will be handled by the component for high DPI
      }
    }
  }
}