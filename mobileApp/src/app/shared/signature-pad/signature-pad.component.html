<div class="signature-pad-container">
  <!-- Signature Canvas -->
  <div class="canvas-container">
    <canvas #signatureCanvas class="signature-canvas"
      [style.border]="isSignatureEmpty ? '2px dashed #ddd' : '2px solid #e0e0e0'" (window:resize)="onResize()">
    </canvas>

    <!-- Placeholder text when empty -->
    <div class="signature-placeholder" *ngIf="isSignatureEmpty">
      <ion-icon name="create-outline" class="signature-icon"></ion-icon>
      <p>Sign here with your finger or stylus</p>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="signature-actions">
    <ion-button fill="outline" size="small" color="medium" class="interactive-button" (click)="undo()"
      [disabled]="isSignatureEmpty">
      <ion-icon name="arrow-undo-outline" slot="start"></ion-icon>
      Undo
      <ion-ripple-effect></ion-ripple-effect>
    </ion-button>

    <ion-button fill="outline" size="small" color="danger" class="interactive-button" (click)="clear()"
      [disabled]="isSignatureEmpty">
      <ion-icon name="trash-outline" slot="start"></ion-icon>
      Clear
      <ion-ripple-effect></ion-ripple-effect>
    </ion-button>
  </div>
</div>