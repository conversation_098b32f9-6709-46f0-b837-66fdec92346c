import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, AfterViewInit, OnD<PERSON>roy } from '@angular/core';
import SignaturePad from 'signature_pad';

@Component({
  selector: 'app-signature-pad',
  templateUrl: './signature-pad.component.html',
  styleUrls: ['./signature-pad.component.scss'],
  standalone: false
})
export class SignaturePadComponent implements OnInit, AfterViewInit, OnDestroy {

  @ViewChild('signatureCanvas', { static: true }) signatureCanvas!: ElementRef<HTMLCanvasElement>;
  @Input() width: number = 400;
  @Input() height: number = 200;
  @Input() backgroundColor: string = '#ffffff';
  @Input() penColor: string = '#000000';
  @Input() penMinWidth: number = 1;
  @Input() penMaxWidth: number = 3;
  @Input() initialSignature: string = '';
  @Output() signatureChange = new EventEmitter<string>();
  @Output() signatureStart = new EventEmitter<void>();
  @Output() signatureEnd = new EventEmitter<void>();

  private signaturePad!: SignaturePad;
  public isSignatureEmpty: boolean = true;

  constructor() { }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    this.initializeSignaturePad();
    this.resizeCanvas();

    // Load initial signature if provided
    if (this.initialSignature) {
      this.fromDataURL(this.initialSignature);
    }
  }

  ngOnDestroy(): void {
    if (this.signaturePad) {
      this.signaturePad.off();
    }
  }

  private initializeSignaturePad(): void {
    const canvas = this.signatureCanvas.nativeElement;

    this.signaturePad = new SignaturePad(canvas, {
      backgroundColor: this.backgroundColor,
      penColor: this.penColor,
      minWidth: this.penMinWidth,
      maxWidth: this.penMaxWidth,
      throttle: 16, // Smooth drawing
      minDistance: 5, // Minimum distance between points
    });

    // Event listeners
    this.signaturePad.addEventListener('beginStroke', () => {
      this.signatureStart.emit();
    });

    this.signaturePad.addEventListener('endStroke', () => {
      this.isSignatureEmpty = this.signaturePad.isEmpty();
      this.signatureEnd.emit();
      this.emitSignatureChange();
    });
  }

  private resizeCanvas(): void {
    const canvas = this.signatureCanvas.nativeElement;
    const container = canvas.parentElement;

    if (container) {
      // Set canvas size based on container or provided dimensions
      const containerWidth = container.clientWidth || this.width;
      const containerHeight = this.height;

      // Set actual canvas size
      canvas.width = containerWidth;
      canvas.height = containerHeight;

      // Set CSS size to match
      canvas.style.width = containerWidth + 'px';
      canvas.style.height = containerHeight + 'px';

      // Reinitialize signature pad after resize
      if (this.signaturePad) {
        this.signaturePad.clear();
        if (this.initialSignature) {
          this.fromDataURL(this.initialSignature);
        }
      }
    }
  }

  private emitSignatureChange(): void {
    if (!this.signaturePad.isEmpty()) {
      const dataURL = this.signaturePad.toDataURL('image/png');
      this.signatureChange.emit(dataURL);
    } else {
      this.signatureChange.emit('');
    }
  }

  // Public methods
  public clear(): void {
    if (this.signaturePad) {
      this.signaturePad.clear();
      this.isSignatureEmpty = true;
      this.signatureChange.emit('');
    }
  }

  public undo(): void {
    if (this.signaturePad) {
      const data = this.signaturePad.toData();
      if (data && data.length > 0) {
        data.pop(); // Remove the last stroke
        this.signaturePad.fromData(data);
        this.isSignatureEmpty = this.signaturePad.isEmpty();
        this.emitSignatureChange();
      }
    }
  }

  public toDataURL(type: string = 'image/png', quality?: number): string {
    if (this.signaturePad) {
      return this.signaturePad.toDataURL(type, quality);
    }
    return '';
  }

  public fromDataURL(dataURL: string): void {
    if (this.signaturePad && dataURL) {
      this.signaturePad.fromDataURL(dataURL).then(() => {
        this.isSignatureEmpty = this.signaturePad.isEmpty();
      }).catch((error) => {
      });
    }
  }

  public isEmpty(): boolean {
    return this.signaturePad ? this.signaturePad.isEmpty() : true;
  }

  // Handle window resize
  onResize(): void {
    setTimeout(() => {
      this.resizeCanvas();
    }, 100);
  }
}
