<ion-content fullscreen class="onboarding-screen">
  <div class="content-overlay">
    <div class="spacer"></div> <!-- Pushes content to bottom -->
    <div class="bottom-content">
      <h2>Shipment Delivered Seamless.On Time.Zero Hassle.</h2>
      <p>Track every step with ease.</p>
      <div class="slide-to-unlock-container">
        <div class="slide-track">
          <div class="slide-text" [class.hidden]="isSliding || isCompleted">
            <span>Slide to Get Started</span>
            <div class="slide-arrows">
              <!-- <ion-icon name="chevron-forward" class="arrow-1"></ion-icon>
              <ion-icon name="chevron-forward" class="arrow-2"></ion-icon>
              <ion-icon name="chevron-forward" class="arrow-3"></ion-icon> -->
            </div>
          </div>
          <div class="success-text" [class.show]="isCompleted">
            <!-- <ion-icon name="checkmark-circle" class="success-icon"></ion-icon> -->
            <span>Welcome!</span>
          </div>
          <div class="slide-button" [class.sliding]="isSliding" [class.completed]="isCompleted"
            [style.transform]="'translateX(' + slidePosition + 'px)'" (touchstart)="onTouchStart($event)"
            (touchmove)="onTouchMove($event)" (touchend)="onTouchEnd($event)" (mousedown)="onMouseDown($event)"
            (mousemove)="onMouseMove($event)" (mouseup)="onMouseUp($event)" (mouseleave)="onMouseUp($event)">
            <div class="slide-icon">
              <ion-icon src="/assets/images/svg/product.svg" *ngIf="!isCompleted"></ion-icon>
              <ion-icon name="checkmark" *ngIf="isCompleted" class="check-icon"></ion-icon>
            </div>
          </div>
        </div>
      </div>

      <div class="register-section" (click)="goToLogin()">
        Already have an account?<strong> Sign in</strong>
      </div>

    </div>
  </div>
</ion-content>