import { Component, On<PERSON><PERSON>roy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { IonInput, NavController, Platform } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { LoadingService } from 'src/services/loading.service';
import { AuthService } from 'src/shared/authservice';
import { ToastService } from 'src/shared/toast.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { TrackMyLocationService } from 'src/services/track-my-location.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { Geolocation } from '@capacitor/geolocation';
import { SpecialRequestStates } from 'src/modals/cargoDetail';
import { BarcodeScannerService } from 'src/services/barcode-scanner.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: false,
  encapsulation: ViewEncapsulation.None
})
export class HomeComponent implements OnInit, OnDestroy {

  user: any;
  private subscriptions = new Subscription();
  isLogoutModalOpen: boolean = false;
  dashboardData: any;
  isGPSAlertOpen = false;
  isRefIdSelectionPopupOpen: boolean = false;
  searchTerm: string | null = null;
  availableShipments: any[] = [];
  selectedShipment: any;
  isModalOpen: boolean = false;
  specialRequestData: SpecialRequestStates = new SpecialRequestStates();
  fromScanTrigger: boolean = false;
  @ViewChild('refIdInput') refIdInput!: IonInput;

  constructor(
    public commonService: CommonService,
    private readonly authService: AuthService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly dataService: DataService,
    private trackMyLocationService: TrackMyLocationService,
    private readonly localStorageService: LocalStorageService,
    private platform: Platform,
    private readonly barcodeService: BarcodeScannerService
  ) {
    if (this.authService.isDriver()) {
      this.trackMyLocationService.checkAndEnableGPS().then(isGPSEnable => {
        console.log('checkAndEnableGPS--', isGPSEnable)
        if (isGPSEnable) {
          this.isGPSAlertOpen = false; // Show the modal
          setTimeout(() => {
            this.checkPermissionsAndStartService()
            // to prevent app crash
          }, 5000);
        } else {
          console.log('checkAndEnableGPS111--', isGPSEnable)
          this.isGPSAlertOpen = true; // Show the modal
        }
      }).catch((error) => {
        console.log('checkAndEnableGPS err--', error);
      });
    }
  }

  ngOnInit() {

  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();
    this.searchTerm = null;
    this.availableShipments = [];
    this.getDashboardData();
  }

  getDashboardData(): void {
    this.loadingService.show();
    this.subscriptions.add(
      this.dataService.getDriverDashboardData().subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          this.dashboardData = data;
        },
        error: (error) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  get newShipmentCount(): number {
    return this.dashboardData?.shipmentStatusCount?.find((s: { status: string; }) => s.status === 'NEW')?.count || 0;
  }

  get inAssignedShipmentCount(): number {
    return this.dashboardData?.shipmentStatusCount?.find((s: { status: string; }) => s.status === 'ASSIGNED')?.count || 0;
  }

  get InTransitShipmentCount(): number {
    return this.dashboardData?.shipmentStatusCount?.find((s: { status: string; }) => s.status === 'IN_TRANSIT')?.count || 0;
  }

  get completedShipmentCount(): number {
    return this.dashboardData?.shipmentStatusCount?.find((s: { status: string; }) => s.status === 'COMPLETED')?.count || 0;
  }

  goToShipments() {
    this.navController.navigateForward('/portal/shipping', {
      queryParams: {
        refresh: 'true',
        goTab: 'PENDING'
      }
    });
  }

  goToShipmentsOnInTransitStatus() {
    this.navController.navigateForward('/portal/shipping', {
      queryParams: {
        refresh: 'true',
        goTab: 'IN_TRANSIT_TAB'
      }
    });
  }

  goToShipmentsOnCompletedStatus() {
    this.navController.navigateForward('/portal/shipping', {
      queryParams: {
        refresh: 'true',
        goTab: 'COMPLETED'
      }
    });
  }

  async onScanBarcodeClick() {
    const scanned = await this.barcodeService.scanBarcode();
    if (scanned) {
      this.searchTerm = scanned;
      this.fromScanTrigger = true; // indicate that this is from a scan
      this.onSearchChange();
    }
  }

  onSearchChange() {
    if (!this.searchTerm) {
      return;
    }

    if (this.searchTerm.length > 2) {
      const searchPayload = {
        filtering: {
          searchText: this.searchTerm
        }
      };
      this.loadingService.show();
      this.subscriptions.add(
        this.dataService.searchDriverShipment(searchPayload).subscribe({
          next: (response: RestResponse) => {
            this.loadingService.hide();

            const data = response.data;
            if (!data || (Array.isArray(data) && data.length === 0)) {
              this.searchTerm = null;
              this.toastService.show("No data found. Try a different search term.");
              return;
            }

            this.availableShipments = data;
            if (this.fromScanTrigger && data.length > 0) {
              this.onSelectShipment(data[0]); // Pass the first shipment only
            }

            this.fromScanTrigger = false;
          },
          error: (error) => {
            this.loadingService.hide();
            this.toastService.show(error.message);
          }
        })
      );
    }
  }

  openRefIdSelectionPopup() {
    this.isRefIdSelectionPopupOpen = true;
  }

  // Handle modal presentation completion for proper iOS focus
  onModalDidPresent() {
    // iOS-specific fix for cursor positioning
    setTimeout(async () => {
      if (this.refIdInput) {
        try {
          // Force a layout recalculation before focusing
          const element = await this.refIdInput.getInputElement();
          if (element) {
            // Trigger a reflow to fix iOS cursor positioning
            element.style.display = 'none';
            element.offsetHeight; // Force reflow
            element.style.display = '';

            // Set focus with additional delay for iOS
            setTimeout(async () => {
              await this.refIdInput.setFocus();
            }, 150);
          }
        } catch (error) {
          // Fallback focus without element manipulation
          setTimeout(async () => {
            await this.refIdInput.setFocus();
          }, 200);
        }
      }
    }, 100);
  }

  closeRefIdLocationSelectionPopup() {
    this.isRefIdSelectionPopupOpen = false;
    this.searchTerm = null;
    this.availableShipments = [];
  }

  onSelectShipment(shipment: any) {
    this.selectedShipment = {
      id: shipment.id,
      status: this.formatStatus(shipment.status),
      refId: shipment.refID,
      customerName: shipment.customerUserDetail?.fullName || '',
      from: shipment.pickupAddressDetail?.city || '',
      to: shipment.deliveryAddressDetail?.city || '',
      etd: shipment.etd
    };

    this.searchTerm = this.selectedShipment.refId;
    this.isModalOpen = true;
    setTimeout(() => {
      this.closeRefIdLocationSelectionPopup();
    }, 50);
  }

  // Close shipment details modal
  closeModal(): void {
    this.isModalOpen = false;

    setTimeout(() => {
      this.searchTerm = null;
      this.selectedShipment = null;
      this.availableShipments = [];
    }, 300);
  }

  formatStatus(status: string): string {
    switch (status?.toUpperCase()) {
      case 'NEW':
        return 'New';
      case 'ASSIGNED':
        return 'Assigned';
      case 'IN_TRANSIT':
        return 'In Transit';
      case 'COMPLETED':
        return 'Completed';
      case 'DELIVERED':
        return 'Delivered';
      case 'CANCELLED':
        return 'Cancelled';
      default:
        return status || 'Unknown';
    }
  }

  editShipment(): void {
    if (this.selectedShipment) {
      this.closeModal(); // Close the modal first

      this.removeLocalStorageDetails();
      this.getShipmentById(this.selectedShipment.id);
    }
  }

  getShipmentById(id: string): void {
    this.loadingService.show();
    this.subscriptions.add(
      this.dataService.getShipmentById(id).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          this.saveShipmentDetails(data);
          this.saveSpecialRequestDetails(data);
          this.saveDocuments(data);

          // Set the shipping tab as active
          this.commonService.activeTab = 'shipping';

          this.navController.navigateForward(`/portal/basic/info`, {
            animated: true
          });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to load shipment details');
        }
      })
    );
  }

  saveShipmentDetails(data: any): void {
    this.localStorageService.setObject("SHIPPING_INFO", data);
    this.localStorageService.set("SHIPPING_MODE", "edit");
  }

  saveSpecialRequestDetails(data: any) {
    this.specialRequestData.isOversize = data.isOversize;
    this.specialRequestData.isRushRequest = data.isRushRequest;
    this.specialRequestData.isEnclosed = data.isEnclosed;
    this.specialRequestData.isFragile = data.isFragile;
    this.specialRequestData.isPerishable = data.isPerishable;
    this.specialRequestData.isDangerousGoods = data.isDangerousGoods;

    this.localStorageService.setObject("SPECIAL_REQUEST", this.specialRequestData);
  }

  saveDocuments(data: any): void {
    this.localStorageService.setObject("SHIPPING_DOCUMENTS", data.documents);
  }

  removeLocalStorageDetails(): void {
    this.localStorageService.remove("SPECIAL_REQUEST");
    this.localStorageService.remove("SHIPPING_DOCUMENTS");
    this.localStorageService.remove("CARGO_MODE");
    this.localStorageService.remove("CARGO_DETAILS");
  }

  // Check if shipment is editable (NEW or ASSIGNED status)
  isShipmentEditable(status: string): boolean {
    return status.toUpperCase() === 'NEW' || status.toUpperCase() === 'ASSIGNED';
  }

  openNotifications() {
    this.navController.navigateForward(`/portal/notifications`, {
      animated: true
    });
  }

  closeLogoutModal() {
    this.isLogoutModalOpen = false;
  }

  openLogoutModal() {
    this.isLogoutModalOpen = true;
  }

  logoutButton() {
    this.openLogoutModal();
  }

  async logout(): Promise<void> {
    this.closeLogoutModal();

    setTimeout(async () => {
      try {
        this.loadingService.show();

        // Stop location tracking service before logout
        if (this.authService.isDriver()) {
          try {
            await this.trackMyLocationService.stopService();
            console.log('Location service stopped during logout');
          } catch (error) {
            console.error('Error stopping location service during logout:', error);
          }
        }

        const deviceId = this.localStorageService.get('device-uuid') || '';
        const logoutPayload = {
          deviceId: deviceId
        };
        await this.dataService.logout(logoutPayload).toPromise();

        this.authService.logout();

        this.loadingService.hide();
        await this.navController.navigateRoot('/account/login', {
          animated: true,
          animationDirection: 'back'
        });
        this.toastService.show('Logged out successfully');
      } catch (error) {
        this.loadingService.hide();

        this.authService.logout();
        await this.navController.navigateRoot('/account/login', {
          animated: true,
          animationDirection: 'back'
        });
      }
    }, 200); // Delay after modal close
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  async checkPermissionsAndStartService() {
    await this.platform.ready();

    const permStatus = await Geolocation.checkPermissions();

    if (permStatus.location == 'granted') {
      const request = await Geolocation.requestPermissions();
      if (request.location !== 'granted') {
        console.error("Location permission denied");
        return;
      }
    }
    const token = this.localStorageService.getObject('token');
    console.log('accessToken', token.accessToken);
    const meetingId = this.localStorageService.getObject('meetingId');
    var userObject = {
      authToken: token.accessToken,
      isTracking: true,
      meetingId: meetingId,
      // type: ""
    }
    // Now call your plugin method to start the location service
    try {
      // grant loction to prevent app crash
      this.trackMyLocationService.sendObject(userObject);
      console.log("Location service started successfully");
    } catch (error) {
      console.error("Error starting location service:", error);
    }
  }

}
