<ion-content class="shipment-details-page">
  <app-customer-header [innerPage]="true" [headingText]="'Shipment Details'" [rightAction]="false"
    [backUrl]="'/portal/shipping'" [hideBellIcon]="true"></app-customer-header>

  <div class="shipment-details-body">
    <div class="section">
      <div class="section-title clickable" (click)="sections.basic = !sections.basic">
        <div class="title-left">
          <ion-icon name="information-circle-outline"></ion-icon>
          <span>Basic Information</span>
        </div>
        <ion-icon class="chev" [name]="sections.basic ? 'chevron-up' : 'chevron-down'"></ion-icon>
      </div>
      <div class="section-body" *ngIf="sections.basic">
        <div class="info-grid">
          <div class="info-card">
            <ion-icon src="assets/images/svg/barcode.svg"></ion-icon>
            <div>
              <div class="label">Ref ID</div>
              <div class="value">{{ shipment?.refID || '-' }}</div>
            </div>
          </div>
          <div class="info-card">
            <ion-icon name="person-outline"></ion-icon>
            <div>
              <div class="label">Contact Name</div>
              <div class="value">{{ shipment?.contactPersonName || '-' }}</div>
            </div>
          </div>
          <div class="info-card">
            <ion-icon name="call-outline"></ion-icon>
            <div>
              <div class="label">Contact Phone</div>
              <div class="value">{{ shipment?.contactPersonPhone || '-' }}</div>
            </div>
          </div>
          <div class="info-card">
            <ion-icon name="mail-outline"></ion-icon>
            <div>
              <div class="label">Contact Email</div>
              <div class="value">{{ shipment?.contactPersonEmail || '-' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="section-title clickable" (click)="sections.pickup = !sections.pickup">
        <div class="title-left">
          <ion-icon name="pin-outline"></ion-icon>
          <span>Pickup Address Information</span>
        </div>
        <ion-icon class="chev" [name]="sections.pickup ? 'chevron-up' : 'chevron-down'"></ion-icon>
      </div>
      <div class="section-body" *ngIf="sections.pickup">
        <div class="info-list">
          <div class="info-row">
            <div class="label">Company Name</div>
            <div class="value">{{ shipment?.pickupCompanyName || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Contact Person Name</div>
            <div class="value">{{ shipment?.pickupContactPersonName || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Contact Phone</div>
            <div class="value">{{ shipment?.pickupContactPersonPhone || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Address</div>
            <div class="value">{{ shipment?.pickupAddressDetail?.address || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">City</div>
            <div class="value">{{ shipment?.pickupAddressDetail?.city || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Province</div>
            <div class="value">{{ shipment?.pickupAddressDetail?.state || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Country</div>
            <div class="value">{{ shipment?.pickupAddressDetail?.country || '-' }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="section-title clickable" (click)="sections.delivery = !sections.delivery">
        <div class="title-left">
          <ion-icon name="navigate-outline"></ion-icon>
          <span>Delivery Address Information</span>
        </div>
        <ion-icon class="chev" [name]="sections.delivery ? 'chevron-up' : 'chevron-down'"></ion-icon>
      </div>
      <div class="section-body" *ngIf="sections.delivery">
        <div class="info-list">
          <div class="info-row">
            <div class="label">Company Name</div>
            <div class="value">{{ shipment?.deliveryCompanyName || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Contact Person Name</div>
            <div class="value">{{ shipment?.deliveryContactPersonName || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Contact Phone</div>
            <div class="value">{{ shipment?.deliveryContactPersonPhone || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Address</div>
            <div class="value">{{ shipment?.deliveryAddressDetail?.address || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">City</div>
            <div class="value">{{ shipment?.deliveryAddressDetail?.city || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Province</div>
            <div class="value">{{ shipment?.deliveryAddressDetail?.state || '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Country</div>
            <div class="value">{{ shipment?.deliveryAddressDetail?.country || '-' }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="section-title clickable" (click)="sections.item = !sections.item">
        <div class="title-left">
          <ion-icon name="cube-outline"></ion-icon>
          <span>Item Information</span>
        </div>
        <ion-icon class="chev" [name]="sections.item ? 'chevron-up' : 'chevron-down'"></ion-icon>
      </div>
      <div class="section-body" *ngIf="sections.item">
        <div class="info-row">
          <div class="label">Item Description</div>
          <div class="value">{{ itemDescription || '-' }}</div>
        </div>
        <div class="info-row">
          <div class="label">Cargo</div>
          <div class="value">{{ shipment?.totalItems || 0 }} item(s)</div>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="section-title clickable" (click)="sections.special = !sections.special">
        <div class="title-left">
          <ion-icon name="alert-circle-outline"></ion-icon>
          <span>Special Request Information</span>
        </div>
        <ion-icon class="chev" [name]="sections.special ? 'chevron-up' : 'chevron-down'"></ion-icon>
      </div>
      <div class="section-body" *ngIf="sections.special">
        <div class="chips" *ngIf="shipment">
          <ion-chip [outline]="true" color="warning" *ngIf="shipment?.isOversize">Oversize</ion-chip>
          <ion-chip [outline]="true" color="warning" *ngIf="shipment?.isEnclosed">Enclosed</ion-chip>
          <ion-chip [outline]="true" color="warning" *ngIf="shipment?.isPerishable">Perishable</ion-chip>
          <ion-chip [outline]="true" color="warning" *ngIf="shipment?.isFragile">Fragile</ion-chip>
          <ion-chip [outline]="true" color="warning" *ngIf="shipment?.isDangerousGoods">Dangerous Goods</ion-chip>
          <ion-chip [outline]="true" color="warning" *ngIf="shipment?.isRushRequest">Rush Request</ion-chip>
        </div>
        <div class="documents" *ngIf="!(shipment?.documents?.length > 0)">
          No Documents uploaded
        </div>
        <div class="image-grid margin-top-10" *ngIf="shipment?.documents?.length > 0">
          <div class="image-item" *ngFor="let doc of shipment?.documents"
            (click)="viewImage(doc.secureUrl, doc.originalName)">
            <img [src]="doc.secureUrl" [alt]="doc.originalName || 'Document'" />
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="section-title clickable" (click)="sections.calc = !sections.calc">
        <div class="title-left">
          <ion-icon name="cash-outline"></ion-icon>
          <span>Calculation Details</span>
        </div>
        <ion-icon class="chev" [name]="sections.calc ? 'chevron-up' : 'chevron-down'"></ion-icon>
      </div>
      <div class="section-body" *ngIf="sections.calc">
        <div class="info-list two-col">
          <div class="info-row">
            <div class="label">Total Items</div>
            <div class="value">{{ shipment?.totalItems || 0 }}</div>
          </div>
          <div class="info-row">
            <div class="label">Total Weight LBS</div>
            <div class="value">{{ shipment?.totalWeight || 0 }}</div>
          </div>
          <div class="info-row">
            <div class="label">Total Volume (cu. inch)</div>
            <div class="value">{{ shipment?.totalVolume || 0 }}</div>
          </div>
          <div class="info-row">
            <div class="label">Sub Total</div>
            <div class="value">{{ shipment?.subTotal != null ? (shipment?.subTotal | currency:'USD':'symbol':'1.2-2') :
              '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Fuel Charges</div>
            <div class="value">{{ shipment?.fuelChargesTotal != null ? (shipment?.fuelChargesTotal |
              currency:'USD':'symbol':'1.2-2') : '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Total GST</div>
            <div class="value">{{ shipment?.gstTotal != null ? (shipment?.gstTotal | currency:'USD':'symbol':'1.2-2') :
              '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Special Charges Total</div>
            <div class="value">{{ shipment?.specialChargesTotal != null ? (shipment?.specialChargesTotal |
              currency:'USD':'symbol':'1.2-2') : '-' }}</div>
          </div>
          <div class="info-row">
            <div class="label">Grand Total</div>
            <div class="value">{{ shipment?.grandTotal != null ? (shipment?.grandTotal |
              currency:'USD':'symbol':'1.2-2') : '-' }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</ion-content>