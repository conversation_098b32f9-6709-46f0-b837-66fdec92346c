import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ModalController, NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { AuthService } from 'src/shared/authservice';
import { RestResponse } from 'src/shared/auth.model';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-shipment-details',
  templateUrl: './shipment-details.component.html',
  styleUrls: ['./shipment-details.component.scss'],
  standalone: false
})
export class ShipmentDetailsComponent implements OnInit, OnDestroy {
  shipmentId!: string;
  shipment: any = null;
  cargoList: any[] = [];
  // Computed totals (fallback when API doesn't return them)
  totalItems: number = 0;
  totalWeight: number = 0;
  totalVolume: number = 0;
  subTotal: number | null = null;
  fuelChargesTotal: number | null = null;
  gstTotal: number | null = null;
  specialChargesTotal: number | null = null;
  grandTotal: number | null = null;
  itemDescription: string | null = null;

  // Section expand/collapse state
  sections: { basic: boolean; pickup: boolean; delivery: boolean; item: boolean; special: boolean; calc: boolean; } = {
    basic: true,
    pickup: false,
    delivery: false,
    item: false,
    special: false,
    calc: false
  };

  private sub = new Subscription();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly navCtrl: NavController,
    private readonly dataService: DataService,
    private readonly loading: LoadingService,
    private readonly toast: ToastService,
    private readonly auth: AuthService,
    private readonly modalCtrl: ModalController,
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.shipmentId = params['shipmentId'];
      if (this.shipmentId) {
        this.loadDetails();
      } else {
        this.toast.show('Shipment id not found');
        this.navCtrl.navigateRoot("/portal/shipping", { animated: true });
      }
    });
  }

  ionViewWillEnter() {
    // refresh on re-enter
    if (this.shipmentId) {
      this.loadDetails();
    }
  }

  loadDetails() {
    this.loading.show();
    const api$ = this.auth.isCustomer()
      ? this.dataService.getCustomerShipmentById(this.shipmentId)
      : this.dataService.getShipmentById(this.shipmentId);

    this.sub.add(
      api$.subscribe({
        next: (res: RestResponse) => {
          this.shipment = res.data || null;
          this.mapTotalsFromShipment();
          this.loadCargoIfNeeded();
          this.loading.hide();
        },
        error: (err) => {
          this.loading.hide();
          this.toast.show(err.message || 'Failed to load shipment details');
        }
      })
    );
  }

  private mapTotalsFromShipment() {
    if (!this.shipment) return;
    // Prefer API-provided totals if present
    this.totalItems = this.shipment.totalItems ?? 0;
    this.totalWeight = this.shipment.totalWeight ?? 0;
    this.totalVolume = this.shipment.totalVolume ?? 0;
    this.subTotal = this.shipment.subTotal ?? null;
    this.fuelChargesTotal = this.shipment.fuelChargesTotal ?? null;
    this.gstTotal = this.shipment.gstTotal ?? null;
    this.specialChargesTotal = this.shipment.specialChargesTotal ?? null;
    this.grandTotal = this.shipment.grandTotal ?? null;
  }

  private loadCargoIfNeeded() {
    // If shipment doesn't have totals but we can compute from items, fetch cargo list
    const needsTotals = (this.totalItems === 0 && !this.grandTotal);
    if (!needsTotals) return;

    const payload = { filtering: { shipmentId: this.shipmentId } } as any;
    const api$ = this.auth.isCustomer()
      ? this.dataService.getCustomerCargoList(payload)
      : this.dataService.getCargoList(payload);

    this.sub.add(api$.subscribe({
      next: (res: RestResponse) => {
        const list = res.data || [];
        this.cargoList = list;
        this.computeTotalsFromCargo(list);
      },
      error: () => { /* ignore compute fallback errors */ }
    }));
  }

  private computeTotalsFromCargo(list: any[]) {
    // Assumes cargo items contain weightInPounds and volume fields
    this.totalItems = list?.length || 0;
    this.totalWeight = list.reduce((sum, c) => sum + (c.weightInPounds || 0), 0);
    this.totalVolume = list.reduce((sum, c) => sum + (c.volume || 0), 0);
    // Item description (first non-empty)
    const firstDesc = list.find(c => !!c?.description)?.description;
    this.itemDescription = firstDesc || null;
  }

  async viewImage(imageUrl: string, fileName?: string) {
    const modal = await this.modalCtrl.create({
      component: (await import('src/shared/full-image/full-image.component')).FullImageComponent,
      componentProps: { imageUrl, fileName, showDelete: false },
      cssClass: 'full-image-modal'
    });
    await modal.present();
  }

  ngOnDestroy(): void {
    this.sub.unsubscribe();
  }
}

