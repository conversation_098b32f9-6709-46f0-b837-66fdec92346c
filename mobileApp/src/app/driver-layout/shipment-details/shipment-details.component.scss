.shipment-details-page {
  --background: #fff;

  .shipment-details-body {
    padding: 12px;
  }

  .section {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.06);
    margin-bottom: 12px;
    padding: 8px;
    border: 1px solid #f2f2f2;
  }

  .section-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 6px;
    background: #FFEA00;
    padding: 10px 12px;
    border-radius: 8px;
    font-weight: 700;
    color: #000;
    margin-bottom: 10px;
  }
  .section-title .title-left { display: inline-flex; align-items: center; gap: 6px; }
  .section-title.clickable { cursor: pointer; }
  .section-title .chev { font-size: 18px; }

  .section-body { padding: 2px 0 6px; }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr !important; /* force single column 4x1 layout */
    gap: 10px;
  }

  .info-card {
    display: flex;
    align-items: flex-start; /* allow text to wrap to multiple lines without vertical centering issues */
    gap: 8px;
    background: #fff;
    border-radius: 10px;
    border: 1px solid #eee;
    padding: 10px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  }
  .info-card ion-icon { flex: 0 0 auto; font-size: 18px; margin-top: 2px; }
  .info-card > div { flex: 1 1 auto; min-width: 0; } /* allow long content to shrink and wrap */

  .info-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    padding: 8px 10px;
    background: #fff7cc;
    border-radius: 8px;
    margin-bottom: 6px;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .info-card {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #fff;
    border-radius: 10px;
    border: 1px solid #f0f0f0;
    padding: 10px;
    box-shadow: 0 1px 6px rgba(0,0,0,0.05);
  }

  .info-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    padding: 8px 10px;
    background: #fff7cc;
    border-radius: 8px;
    margin-bottom: 6px;
  }

  .info-list.two-col .info-row { grid-template-columns: 1.3fr 0.7fr; }

  .label { font-size: 12px; color: #666; white-space: nowrap; }
  .value { font-weight: 600; color: #000; word-break: break-word; overflow-wrap: anywhere; }

  .chips { display: flex; flex-wrap: wrap; gap: 8px; }

  .documents { color: #666; font-size: 12px; padding: 8px 10px; background: #fff; border: 1px dashed #ddd; border-radius: 8px; }

  .image-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; }
  .image-item { position: relative; border-radius: 8px; overflow: hidden; border: 1px solid #eee; }
  .image-item img { width: 100%; height: 90px; object-fit: cover; display: block; }
}

