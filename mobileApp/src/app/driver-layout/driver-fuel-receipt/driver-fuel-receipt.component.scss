.fuel-receipt-page {
    --background: white;
    height: 100%;

    .fuel-receipt-body-section {
        display: inline-block;
        width: 100%;
        height: calc(100vh - 210px);
        padding: 0px 20px 10px 20px !important;
        overflow-y: auto;

        .form-container {
            min-height: 250px;
        }

        // Form row for side-by-side fields
        .form-row {
            display: flex;
            gap: 15px;
            width: 100%;

            .form-field-half {
                flex: 1;
                width: calc(50% - 7.5px);

                .site-form-control {
                    width: 100%;
                    margin-bottom: 5px;
                }

                app-validation-message,
                .error-message {
                    font-size: 12px;
                    margin-top: 2px;
                }
            }

            // Responsive design for smaller screens
            @media (max-width: 480px) {
                gap: 10px;

                .form-field-half {
                    width: calc(50% - 5px);
                }
            }

            @media (max-width: 360px) {
                gap: 8px;

                .form-field-half {
                    width: calc(50% - 4px);

                    .site-form-control {
                        ion-input,
                        ion-select {
                            font-size: 13px;
                            min-height: 45px !important;
                        }
                    }
                }
            }
        }

        .info-text {
            font-size: 17px;
            font-weight: bold;
        }

        .site-form-control {
            width: 100%;
            margin-bottom: 10px;

            ion-input,
            ion-select,
            ion-textarea {
                font-size: 14px;
                --padding-start: 0px !important;
                --padding-end: 0px;
                //   --padding-top: 3px;
                --padding-bottom: 0px;
                font-weight: 500;
                min-height: 50px !important;
                width: 100%;
            }

            ion-select::part(icon) {
                display: none;
            }

            .dropdown-arrow-icon {
                margin-top: 19px;
                font-size: 16px;
            }

            .date-icon {
                margin-top: 19px;
                font-size: 16px;
            }

            .start-icon {
                margin-right: 13px;
                color: black;
                width: 18px;
                height: 18px;
            }
        }

        .browse-file-button {
            --background: #FFEA00 !important;
            --background-activated: #FFEA00 !important;
            --color: black !important;
            min-height: 48px;
            --border-radius: 22px;
            text-transform: capitalize;
            color: black !important;
            font-weight: 600;
            font-size: 16px;
            letter-spacing: 0.5px;
            overflow: unset !important;

            // Ensure text and span are visible
            span {
                color: black !important;
                font-weight: 600;
                font-size: 16px;
                display: block;
                text-transform: capitalize;
                letter-spacing: 0.5px;
            }

            ion-label {
                color: black !important;
            }
        }

        .error-message {
            color: #ff4444;
            font-size: 14px;
            margin-top: 5px;
        }

        .image-error {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            border: 1px dashed #ccc;
            border-radius: 8px;

            ion-icon {
                font-size: 24px;
                color: #999;
                margin-bottom: 4px;
            }

            span {
                font-size: 12px;
                color: #666;
                text-align: center;
            }
        }
        .fuel-receipt-btn-container {
             display: flex;
             flex-direction: row;
             justify-content: space-between;
             align-items: center;
             gap: 16px;
             margin-top: 30px;
             padding: 0;
             width: 100%;

             .site-button {
                 flex: 1;
                 max-width: calc(50% - 8px);
                 height: 54px;
                 border-radius: 16px;
                 font-weight: 600;
                 font-size: 16px;
                 text-transform: none;
                 box-shadow: none !important;
                 --box-shadow: none !important;
                 transition: all 0.3s ease;
                 position: relative;
                 overflow: hidden;
                 user-select: none;

                 &:hover {
                     // Remove complex transforms
                     box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
                 }

                 &:active {
                     transform: translateY(-1px);
                 }

                 &.ship-cancel-btn {
                     // Simple cancel button matching app design
                     background: #ffffff;
                     --background-activated: #ffffff !important;
                     color: #6c757d;
                     border: 1px solid #dee2e6;
                     text-transform: uppercase;
                     font-weight: 600;
                     transition: all 0.3s ease;

                     &:hover {
                         background: #f8f9fa;
                         color: #495057;
                         border-color: #adb5bd;
                     }

                     &:active {
                         background: #e9ecef;
                         color: #495057;
                         border-color: #adb5bd;
                     }

                     ion-ripple-effect {
                         color: rgba(108, 117, 125, 0.2);
                     }

                     // Animated border effect
                     &::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: -100%;
                         width: 100%;
                         height: 100%;
                         background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
                         transition: left 0.5s ease;
                         z-index: 1;
                     }

                     &:hover::before {
                         left: 100%;
                     }

                     // Subtle shake animation on focus
                     &:focus {
                         animation: subtle-shake 0.5s ease-in-out;
                     }
                 }

                 &.ship-submit-btn {
                     // Enhanced interactive Next button with white text
                     position: relative;
                     overflow: hidden;
                     text-transform: uppercase;
                     background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                     color: #ffffff;
                     box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
                     transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

                     &:hover {
                         transform: translateY(-3px) scale(1.02);
                         box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                         border-color: #1a252f;
                         background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
                         color: #ffffff;
                     }

                     &:active {
                         transform: translateY(-1px) scale(0.98);
                         box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
                     }

                     // Enhanced ripple effect for Next button
                     ion-ripple-effect {
                         color: rgba(255, 255, 255, 0.3);
                         z-index: 2;
                     }

                     // Animated border effect
                     &::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: -100%;
                         width: 100%;
                         height: 100%;
                         background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                         transition: left 0.5s ease;
                         z-index: 1;
                     }

                     &:hover::before {
                         left: 100%;
                     }

                     // Subtle pulse animation on focus
                     &:focus {
                         animation: next-pulse 0.6s ease-in-out;
                     }

                     // Enhanced text styling
                     span {
                         font-weight: 700 !important;
                         letter-spacing: 0.8px;
                         text-transform: uppercase;
                         position: relative;
                         z-index: 3;
                         transition: all 0.3s ease;
                         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                     }

                     &:hover span {
                         letter-spacing: 1px;
                         transform: scale(1.05);
                     }
                 }

                 // Ensure ripple effect is visible
                 ion-ripple-effect {
                     z-index: 1;
                     pointer-events: none;
                 }
             }

             // Responsive design - keep buttons in row
             @media (max-width: 480px) {
                 gap: 12px;
                 margin-top: 20px;

                 .site-button {
                     max-width: calc(50% - 6px);
                     height: 48px;
                     font-size: 14px;
                 }
             }

             @media (max-width: 360px) {
                 gap: 8px;

                 .site-button {
                     max-width: calc(50% - 4px);
                     height: 44px;
                     font-size: 13px;
                 }
             }
         }

    }

}