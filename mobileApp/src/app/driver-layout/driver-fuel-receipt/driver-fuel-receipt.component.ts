import { ChangeDetectorRef, Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Nav<PERSON><PERSON>roller, ModalController } from '@ionic/angular';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { ToastService } from 'src/shared/toast.service';
import { LoadingService } from 'src/services/loading.service';
import { FullImageComponent } from 'src/shared/full-image/full-image.component';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';

@Component({
  selector: 'app-driver-fuel-receipt',
  templateUrl: './driver-fuel-receipt.component.html',
  styleUrls: ['./driver-fuel-receipt.component.scss'],
  standalone: false
})
export class DriverFuelReceiptComponent implements OnInit, OnD<PERSON>roy {

  onClickValidation!: boolean;
  selectedFuelType: 'PETROL' | 'DIESEL' = 'PETROL';
  fuelTypes: Array<{ display: string, value: string }> = [
    { value: 'PETROL', display: 'Petrol' },
    { value: 'DIESEL', display: 'Diesel' }
  ];
  vehicles: Array<any> = new Array<any>();
  fuelData: any;
  fuelIdForEdit: any;
  fuelEditMode: any;
  showValidationErrors = false;
  requestImage: string | null = null;
  hasFuelReceiptInfo = true;

  // Custom validation errors for digit limits
  kmsReadingDigitError = false;
  fuelInLiterDigitError = false;
  fuelCostDigitError = false;
  uploadedImages: {
    id: string | null;
    url: string;
    secureUrl?: string;
    fileName?: string;
    mimeType?: string;
    size?: number;
    originalName?: string;
    path?: string;
    selected?: boolean;
  }[] = [];

  constructor(private navController: NavController,
    private modalCtrl: ModalController,
    private toastService: ToastService,
    private loadingService: LoadingService,
    private changeDetectorRef: ChangeDetectorRef,
    private dataService: DataService,
    private readonly localStorageService: LocalStorageService,
  ) {

  }

  ngOnInit() {
    this.fuelData = {};
  }

  ionViewWillEnter() {
    this.fuelIdForEdit = this.localStorageService.get("FUEL_EDIT_ID");
    this.fuelEditMode = this.localStorageService.get("FUEL_EDIT_MODE");

    this.hasFuelReceiptInfo = false;

    this.restoreStatusBar();
    this.loadVehicles(); // ✅ Load vehicles every time we enter the screen
    this.checkForEditMode();

    // Force UI refresh
    setTimeout(() => {
      this.hasFuelReceiptInfo = true;
    }, 0);
  }

  ionViewDidEnter() {
    setTimeout(() => {
      this.restoreStatusBar();
    }, 100);

    setTimeout(() => {
      this.restoreStatusBar();
    }, 500);

    setTimeout(() => {
      this.restoreStatusBar();
    }, 1000);
  }

  ionViewWillLeave() {
    this.localStorageService.remove("FUEL_EDIT_MODE");
    this.localStorageService.remove("FUEL_EDIT_ID");
    this.resetForm();
  }

  async loadVehicles() {
    this.loadingService.show();
    this.dataService.getVehicleSelection().subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const data = response.data;
        this.vehicles = data;
        console.log('✅ Vehicles loaded:', this.vehicles.length, 'vehicles'); // Debug log
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
        console.error('❌ Error loading vehicles:', error); // Debug log
      }
    });
  }

  // Manual refresh method for vehicles
  refreshVehicles() {
    console.log('🔄 Manually refreshing vehicles...');
    this.loadVehicles();
  }

  checkForEditMode() {
    if (this.fuelEditMode === "edit") {
      this.loadFuelReceiptForEdit();
    }
  }

  async loadFuelReceiptForEdit() {
    this.loadingService.show();
    this.dataService.getFuelReceiptById(this.fuelIdForEdit).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const receiptData = response.data;
        this.populateFormWithData(receiptData);
      },
      error: (error: any) => {
        this.loadingService.hide();
        this.toastService.show(error.message);
      }
    })
  }

  populateFormWithData(data: any) {
    this.fuelData.id = data.id || '';
    this.fuelData.selectedVehicleId = data.vehicle || '';
    this.selectedFuelType = data.fuelType || 'PETROL';
    this.fuelData.kmsReading = data.meterReading?.toString() || '';
    this.fuelData.fuelInLiter = data.fuelInLiters?.toString() || '';
    this.fuelData.fuelCost = data.fuelCost?.toString() || '';

    // Handle existing images
    if (data.fuelReceiptImages && data.fuelReceiptImages.length > 0) {
      this.uploadedImages = data.fuelReceiptImages.map((img: any) => ({
        id: img.id,
        filename: img.filename,
        mimeType: img.mimeType,
        size: img.size,
        path: img.path,
        originalName: img.originalName,
        secureUrl: img.secureUrl,
        url: img.secureUrl,
      }));
    }

    // Trigger change detection to update the UI
    this.changeDetectorRef.detectChanges();
  }

  navigateToFuelReceiptsList() {
    this.navController.navigateForward('/portal/receipt/list', { animated: true });
  }

  preventInvalidInput(event: any, inputType: 'integer' | 'decimal') {
    const key = event.key;

    // Allow control keys (backspace, delete, tab, escape, enter, etc.)
    if (event.ctrlKey || event.metaKey ||
      ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'Home', 'End', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(key)) {
      return;
    }

    // For integer inputs, only allow digits
    if (inputType === 'integer') {
      if (!/^\d$/.test(key)) {
        event.preventDefault();
        return;
      }
    }

    // For decimal inputs, allow digits and one decimal point
    if (inputType === 'decimal') {
      const currentValue = event.target.value || '';

      // Allow digits
      if (/^\d$/.test(key)) {
        return;
      }

      // Allow decimal point only if there isn't one already
      if (key === '.' && !currentValue.includes('.')) {
        return;
      }

      // Block all other characters
      event.preventDefault();
    }
  }

  validateNumericInput(event: any, fieldName: string) {
    const value = event.detail.value;
    let cleanValue = value;
    let isValid = true;

    // Reset digit error flags
    this.resetDigitErrors(fieldName);

    // Different validation for different fields
    switch (fieldName) {
      case 'kmsReading':
        // Only allow integers (no decimals)
        const integerPattern = /^\d*$/;
        if (!integerPattern.test(value)) {
          cleanValue = value.replace(/[^\d]/g, '');
          isValid = false;
        }
        // Check 16-digit limit for integers
        if (cleanValue.length > 16) {
          this.kmsReadingDigitError = true;
          cleanValue = cleanValue.substring(0, 16);
          isValid = false;
        }
        break;
      case 'fuelInLiter':
      case 'fuelCost':
        // Allow decimals but only one decimal point
        const decimalPattern = /^\d*\.?\d*$/;
        if (!decimalPattern.test(value)) {
          // Remove all non-numeric characters except one decimal point
          cleanValue = value.replace(/[^\d.]/g, '');
          // Ensure only one decimal point
          const parts = cleanValue.split('.');
          if (parts.length > 2) {
            cleanValue = parts[0] + '.' + parts.slice(1).join('');
          }
          isValid = false;
        }
        // Check 16-digit limit (including decimal point)
        const totalDigits = cleanValue.replace('.', '').length;
        if (totalDigits > 16) {
          if (fieldName === 'fuelInLiter') {
            this.fuelInLiterDigitError = true;
          } else {
            this.fuelCostDigitError = true;
          }
          // Truncate to 16 digits while preserving decimal structure
          const parts = cleanValue.split('.');
          if (parts.length === 2) {
            const integerPart = parts[0];
            const decimalPart = parts[1];
            const availableDecimalDigits = 16 - integerPart.length;
            if (availableDecimalDigits > 0) {
              cleanValue = integerPart + '.' + decimalPart.substring(0, availableDecimalDigits);
            } else {
              cleanValue = integerPart.substring(0, 16);
            }
          } else {
            cleanValue = cleanValue.substring(0, 16);
          }
          isValid = false;
        }
        break;
    }

    // If input is invalid, clean it and update the model
    if (!isValid) {
      // Update the input field immediately
      event.target.value = cleanValue;

      // Update the model
      switch (fieldName) {
        case 'kmsReading':
          this.fuelData.kmsReading = cleanValue;
          break;
        case 'fuelInLiter':
          this.fuelData.fuelInLiter = cleanValue;
          break;
        case 'fuelCost':
          this.fuelData.fuelCost = cleanValue;
          break;
      }

      // Trigger change detection
      this.changeDetectorRef.detectChanges();
    }
  }

  private resetDigitErrors(fieldName: string) {
    switch (fieldName) {
      case 'kmsReading':
        this.kmsReadingDigitError = false;
        break;
      case 'fuelInLiter':
        this.fuelInLiterDigitError = false;
        break;
      case 'fuelCost':
        this.fuelCostDigitError = false;
        break;
    }
  }

  goBack() {
    this.navController.navigateRoot("/portal/dashboard", { animated: true });
  }

  async upload() {
    try {
      const response = await Camera.getPhoto({
        quality: 50,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Prompt, // Prompt user to select from camera or files
      });
      if (response.base64String) {
        await this.processCropper(response.base64String);
      }
    } catch (error) {
      //  this.toastService.show("Something went wrong while uploading profile picture.");
    } finally {
      // Restore status bar after camera operation
      await this.restoreStatusBar();
    }
  }

  async processCropper(base64Image: string) {
    const modal = await this.modalCtrl.create({
      component: FileCropperComponent,
      componentProps: {
        file: { base64String: base64Image }
      },
      cssClass: "cropper-modal"
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role !== 'confirm') {
      this.toastService.show("Sorry, profile picture uploading has been cancelled.");
      return;
    }
    if (data && data.croppedFile) {
      await this.uploadImage(data.croppedFile);
    } else {
      this.toastService.show("No file selected after cropping.");
    }
  }

  private async restoreStatusBar(): Promise<void> {
    try {
      // Force a second restoration after a delay
      setTimeout(async () => {
        try {
          // await this.statusBarService.setColorScheme('driverPortal');
        } catch (error) {
          console.error('Error in delayed status bar restore:', error);
        }
      }, 500);

    } catch (error) {
      console.error('Error restoring status bar:', error);
    }
  }

  async uploadImage(blob: Blob): Promise<void> {
    if (!blob) {
      this.toastService.show('No file selected. Please choose an image to upload.');
      return;
    }

    const file = new File([blob], 'cropped-image.png', { type: 'image/png' }); // Convert Blob to File

    const formData = new FormData();
    formData.append('file', file, file.name);

    this.loadingService.show(); // Show loading indicator

    this.dataService.uploadFile(formData).subscribe({
      next: (response: any) => {
        this.loadingService.hide();
        this.handleUploadResponse(response);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred while uploading the file');
      }
    });
  }

  private handleUploadResponse(response: any): void {
    const attachments = response?.data;
    if (Array.isArray(attachments) && attachments.length > 0) {
      attachments.forEach((attachment: any) => {
        // Use secureUrl for display but preserve clean path for API
        const displayUrl = attachment.secureUrl || attachment.path || attachment.fileName;
        this.uploadedImages.push({
          id: null,
          url: displayUrl, // For display purposes
          secureUrl: attachment.secureUrl || '',
          fileName: attachment.filename || '',
          mimeType: attachment.mimeType || 'image/png',
          size: attachment.size || 0,
          originalName: attachment.originalName || '',
          path: attachment.path || '' // Store clean path separately
        });
      });
      this.changeDetectorRef.detectChanges();
      this.toastService.show('File uploaded successfully.');
    } else {
      this.toastService.show('Failed to upload file.');
    }
  }

  toggleSelected(selectedImage: { url: string, selected?: boolean }) {
    this.uploadedImages.forEach(image => {
      if (image === selectedImage) {
        image.selected = !image.selected; // Toggle current image
      } else {
        image.selected = false; // Deselect others
      }
    });
  }

  onDeleteClick(event: Event, image: { url: string }) {
    event.stopPropagation(); // Prevent parent (click) from firing
    this.removeImage(image);
  }

  removeImage(imageOrIndex: { url: string } | number) {
    if (typeof imageOrIndex === 'number') {
      // Remove by index
      this.uploadedImages.splice(imageOrIndex, 1);
    } else {
      // Remove by image object
      this.uploadedImages = this.uploadedImages.filter(img => img.url !== imageOrIndex.url);
    }
  }

  async viewImage(imageUrl: string, showDelete: boolean = false) {
    const modal = await this.modalCtrl.create({
      component: FullImageComponent,
      componentProps: { imageUrl, showDelete },
      cssClass: 'full-image-modal'
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role === 'remove' && data) {
      this.removeImage({ url: data }); // use existing method
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    // Check for digit limit errors
    if (this.kmsReadingDigitError || this.fuelInLiterDigitError || this.fuelCostDigitError) {
      this.toastService.show('Please fix the digit limit errors before submitting.');
      return;
    }

    if (!form.valid) {
      return;
    }

    // Check for uploaded images
    if (this.uploadedImages.length === 0) {
      this.toastService.show('Please upload at least one image before submitting.');
      return;
    }

    const fuelReceiptImages = this.generateFuelReceiptImages();

    const payload = {
      id: this.fuelData.id || null,
      vehicle: this.fuelData.selectedVehicleId,
      meterReading: parseInt(this.fuelData.kmsReading),
      fuelInLiters: parseInt(this.fuelData.fuelInLiter),
      fuelCost: parseFloat(this.fuelData.fuelCost),
      fuelType: this.selectedFuelType,
      fuelReceiptImages: fuelReceiptImages
    };

    const apiCall = (this.fuelEditMode === "edit"
      ? this.dataService.editFuelReceipt(payload)
      : this.dataService.saveFuelReceipt(payload)
    )
    apiCall.subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        this.toastService.show(this.fuelEditMode === "edit" ? 'Fuel receipt updated successfully!' : 'Fuel receipt saved successfully!');
        this.resetForm();
        this.navController.navigateForward('/portal/receipt/list');
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  resetForm() {
    this.onClickValidation = false;
    this.fuelData = {
      id: '',
      selectedVehicleId: '',
      selectedFuelType: 'PETROL',
      kmsReading: '',
      fuelInLiter: '',
      fuelCost: '',
    };

    // Clear images
    this.uploadedImages = [];
    this.requestImage = null;

    // Reset digit error flags
    this.kmsReadingDigitError = false;
    this.fuelInLiterDigitError = false;
    this.fuelCostDigitError = false;
  }

  private generateFuelReceiptImages() {
    return this.uploadedImages.map((image) => ({
      id: image.id ?? null,
      filename: image.fileName || '',
      mimeType: image.mimeType || 'image/png',
      size: image.size || 0,
      path: image.path || image.url, // Use clean path if available, fallback to url
      originalName: image.originalName || '',
      secureUrl: image.secureUrl || ''
    }));
  }

  ngOnDestroy() {
  }

}
