<ion-content class="fuel-receipt-page">
  <app-customer-header [innerPage]="true" [headingText]="'Fuel Receipt'" [backUrl]="'/portal/dashboard'"
    [rightAction]="true" [rightActionIcon]="'list-outline'" [hideBellIcon]="true"
    (rightActionCallback)="navigateToFuelReceiptsList()"></app-customer-header>

  <div class="fuel-receipt-body-section">
    <div class="margin-top-25">
      <span class="info-text">Fuel Receipt</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #basicForm="ngForm" novalidate>

        <!-- Vehicle Selection and Fuel Type in one row -->
        <div class="form-row margin-top-10 margin-bottom-10">
          <!-- Vehicle Selection -->
          <div class="form-field-half">
            <ng-container *ngIf="hasFuelReceiptInfo">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': vehicleSelect?.invalid && onClickValidation}">
                <ion-select #vehicleSelect="ngModel" [(ngModel)]="fuelData.selectedVehicleId" interface="action-sheet"
                  required name="vehicle" label="Select Vehicle" labelPlacement="floating">
                  <ion-select-option *ngFor="let vehicle of vehicles" [value]="vehicle.id">
                    {{ vehicle.name }}
                  </ion-select-option>
                </ion-select>
                <ion-icon slot="end" class="dropdown-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
              </ion-item>
              <app-validation-message [field]="vehicleSelect" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </ng-container>
          </div>

          <!-- Fuel Type Selection -->
          <div class="form-field-half">
            <ng-container *ngIf="hasFuelReceiptInfo">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': fuelTypeSelect?.invalid && onClickValidation}">
                <ion-select #fuelTypeSelect="ngModel" [(ngModel)]="selectedFuelType" interface="action-sheet" required
                  name="fuelType" label="Fuel Type" labelPlacement="floating">
                  <ion-select-option *ngFor="let type of fuelTypes" [value]="type.value">
                    {{ type.display }}
                  </ion-select-option>
                </ion-select>
                <ion-icon slot="end" class="dropdown-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
              </ion-item>
              <app-validation-message [field]="fuelTypeSelect" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </ng-container>
          </div>
        </div>

        <!-- Kms Reading -->
        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasFuelReceiptInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': kmsReadingField?.invalid && onClickValidation}">
              <ion-input label="Kms Reading" labelPlacement="floating" name="kmsReading"
                [(ngModel)]="fuelData.kmsReading" #kmsReadingField="ngModel" type="number" pattern="^[0-9]+$"
                inputmode="numeric" required (ionInput)="validateNumericInput($event, 'kmsReading')"
                (keydown)="preventInvalidInput($event, 'integer')">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="kmsReadingField" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Please add valid Kms Reading (numbers only).'">
            </app-validation-message>
            <div class="error-message" *ngIf="kmsReadingDigitError">
              Maximum 16 digits allowed for Kms Reading.
            </div>
          </ng-container>
        </div>

        <!-- Fuel in Liters and Fuel Cost in one row -->
        <div class="form-row margin-top-10 margin-bottom-10">
          <!-- Fuel in Liters -->
          <div class="form-field-half">
            <ng-container *ngIf="hasFuelReceiptInfo">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': fuelInLiterField?.invalid && onClickValidation}">
                <ion-input label="Fuel in Liters" labelPlacement="floating" name="fuelInLiter"
                  [(ngModel)]="fuelData.fuelInLiter" #fuelInLiterField="ngModel" type="number"
                  pattern="^[0-9]+(\.[0-9]+)?$" inputmode="decimal" required
                  (ionInput)="validateNumericInput($event, 'fuelInLiter')"
                  (keydown)="preventInvalidInput($event, 'decimal')">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="fuelInLiterField" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Please add valid Fuel in Liters (numbers only).'">
              </app-validation-message>
              <div class="error-message" *ngIf="fuelInLiterDigitError">
                Maximum 16 digits allowed for Fuel in Liters.
              </div>
            </ng-container>
          </div>

          <!-- Fuel Cost -->
          <div class="form-field-half">
            <ng-container *ngIf="hasFuelReceiptInfo">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': fuelCostField?.invalid && onClickValidation}">
                <ion-input label="Fuel Cost" labelPlacement="floating" name="fuelCost" [(ngModel)]="fuelData.fuelCost"
                  #fuelCostField="ngModel" type="number" pattern="^[0-9]+(\.[0-9]{1,2})?$" inputmode="decimal" required
                  (ionInput)="validateNumericInput($event, 'fuelCost')"
                  (keydown)="preventInvalidInput($event, 'decimal')">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="fuelCostField" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Please add valid fuel cost (numbers and decimal only).'">
              </app-validation-message>
              <div class="error-message" *ngIf="fuelCostDigitError">
                Maximum 16 digits allowed for Fuel Cost.
              </div>
            </ng-container>
          </div>
        </div>

        <!-- Image Upload Section -->
        <div class="document-upload-container margin-top-20"
          [ngClass]="{'missing-image': onClickValidation && uploadedImages.length === 0}">
          <img src="assets/images/svg/attach.svg" alt="Upload Icon" class="upload-icon">
          <div class="upload-text">
            <span class="choose-file-text">Upload a picture of your fuel receipt</span>
            <span class="upload-multiple-file-text">upload multiple or single file upload (max. 2mb)</span>
            <ion-button class="site-button browse-file-button margin-top-20" expand="full" shape="round"
              (click)="upload()">
              <span>Browse file</span>
            </ion-button>
          </div>
        </div>

        <!-- Image Gallery Below -->
        <div class="image-grid margin-top-30" *ngIf="uploadedImages.length > 0">
          <div class="image-item" *ngFor="let image of uploadedImages" (click)="toggleSelected(image)"
            [class.selected]="image.selected">
            <img [src]="image.url" alt="Uploaded" />
            <div class="icon-container" *ngIf="image.selected">
              <ion-icon class="icon" src="assets/images/svg/view-icon.svg"
                (click)="viewImage(image.url, true)"></ion-icon>
              <!-- <ion-icon class="icon" src="assets/images/svg/delete-icon.svg"
                (click)="onDeleteClick($event, image)"></ion-icon> -->
            </div>
          </div>
        </div>

        <!-- Image Upload Validation Message -->
        <!-- <div class="error-message margin-top-10" *ngIf="onClickValidation && uploadedImages.length === 0">
          Please upload at least one fuel receipt image.
        </div> -->

        <div class="fuel-receipt-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn" expand="full" shape="round" (click)="goBack()">
            <span>Cancel</span>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn" expand="full" shape="round" type="submit"
            (click)="submitProfile(basicForm.form)">
            <span>Submit</span>
          </ion-button>
        </div>

      </form>

    </div>

  </div>
</ion-content>