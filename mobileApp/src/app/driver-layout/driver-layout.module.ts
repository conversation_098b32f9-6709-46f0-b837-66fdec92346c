import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { MaskitoDirective } from '@maskito/angular';
import { HttpAuthInterceptor } from 'src/shared/http.interceptor';
import { IconsModule } from 'src/shared/icon.module';
import { DriverLayoutComponent } from './driver-layout.component';
import { DRIVERLAYOUTROUTING } from './driver-layout.routing';
import { HomeComponent } from './home/<USER>';
import { DriverShippingComponent } from './driver-shipping/driver-shipping.component';
import { DriverFuelReceiptComponent } from './driver-fuel-receipt/driver-fuel-receipt.component';
import { FuelReceiptsListComponent } from './fuel-receipts-list/fuel-receipts-list.component';
import { DriverCalendarComponent } from './driver-calendar/driver-calendar.component';
import { DriverAccountComponent } from './driver-account/driver-account.component';
import { BasicInfoComponent } from './driver-shipping/add-shipping-details/basic-info/basic-info.component';
import { SharedModuleModule } from "../../shared/shared-module.module";
import { OtherInfoComponent } from './driver-shipping/add-shipping-details/other-info/other-info.component';
import { PickupDeliveryComponent } from './driver-shipping/add-shipping-details/pickup-delivery/pickup-delivery.component';
import { DeliveryLocationComponent } from './driver-shipping/add-shipping-details/delivery-location/delivery-location.component';
import { ShipmentCargoListingComponent } from './driver-shipping/add-shipping-details/shipment-cargo-listing/shipment-cargo-listing.component';
import { AddCargoDetailsComponent } from './driver-shipping/add-shipping-details/add-cargo-details/add-cargo-details.component';
import { SpecialRequestComponent } from './driver-shipping/add-shipping-details/special-request/special-request.component';
import { AddDocumentsComponent } from './driver-shipping/add-shipping-details/add-documents/add-documents.component';
import { StartShipmentComponent } from './start-shipment/start-shipment.component';
import { PodManagementComponent } from './pod-management/pod-management.component';
import { SignaturePadComponent } from '../shared/signature-pad/signature-pad.component';
import { ImageCropperModule } from 'ngx-image-cropper';
import { ViewDocumentsComponent } from './view-documents/view-documents.component';
import { ShipmentDetailsComponent } from './shipment-details/shipment-details.component';
import { NotificationListComponent } from './notification-list/notification-list.component';
import { ChangePasswordComponent } from './driver-account/change-password/change-password.component';

@NgModule({
  declarations: [
    DriverLayoutComponent,
    HomeComponent,
    DriverShippingComponent,
    DriverFuelReceiptComponent,
    FuelReceiptsListComponent,
    DriverCalendarComponent,
    DriverAccountComponent,
    BasicInfoComponent,
    OtherInfoComponent,
    PickupDeliveryComponent,
    DeliveryLocationComponent,
    ShipmentCargoListingComponent,
    AddCargoDetailsComponent,
    SpecialRequestComponent,
    AddDocumentsComponent,
    StartShipmentComponent,
    PodManagementComponent,
    SignaturePadComponent,
    ViewDocumentsComponent,
    ShipmentDetailsComponent,
    ChangePasswordComponent,
    NotificationListComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    MaskitoDirective,
    IconsModule,
    RouterModule.forChild(DRIVERLAYOUTROUTING),
    SharedModuleModule,
    ImageCropperModule
  ],
  providers: [
    provideHttpClient(withInterceptorsFromDi()),
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpAuthInterceptor,
      multi: true
    }
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DriverLayoutModule { }
