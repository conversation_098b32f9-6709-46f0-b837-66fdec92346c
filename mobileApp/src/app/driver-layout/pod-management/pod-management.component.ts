import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Nav<PERSON>ontroller, ActionSheetController, AlertController, ModalController } from '@ionic/angular';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Subscription } from 'rxjs';
import { ToastService } from 'src/shared/toast.service';
import { LoadingService } from 'src/services/loading.service';
import { FullImageComponent } from 'src/shared/full-image/full-image.component';
import { FileCropperComponent } from 'src/shared/file-cropper/file-cropper.component';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { BarcodeScannerService } from 'src/services/barcode-scanner.service';
import { StatusBarService } from 'src/services/status-bar.service';
import { SignaturePadComponent } from '../../shared/signature-pad/signature-pad.component';

@Component({
  selector: 'app-pod-management',
  templateUrl: './pod-management.component.html',
  styleUrls: ['./pod-management.component.scss'],
  standalone: false
})
export class PodManagementComponent implements OnInit, OnDestroy {

  @ViewChild('signaturePad') signaturePadComponent!: SignaturePadComponent;

  shipmentId: string = '';
  refId: string = '';
  onClickValidation!: boolean;
  private subscription: Subscription = new Subscription();
  hasPodInfo = true;

  formData: any;
  showValidationErrors = false;
  requestImage: string | null = null;
  uploadedImages: {
    url: string;
    secureUrl?: string;
    fileName?: string;
    mimeType?: string;
    size?: number;
    originalName?: string;
    path?: string;
    selected?: boolean;
  }[] = [];

  constructor(
    private route: ActivatedRoute,
    private navController: NavController,
    private modalCtrl: ModalController,
    private toastService: ToastService,
    private loadingService: LoadingService,
    private changeDetectorRef: ChangeDetectorRef,
    private readonly dataService: DataService,
    private readonly barcodeService: BarcodeScannerService,
    private statusBarService: StatusBarService

  ) { }

  ngOnInit() {
    this.clearFormData();
    this.clearUploadedImages();
    this.clearSignature();
  }

  ionViewWillEnter() {
    this.hasPodInfo = true; // Enable POD info to show delivery notes field
    this.clearAllPodData();
    this.restoreStatusBar();

    // Clear signature pad after a short delay to ensure it's initialized
    setTimeout(() => {
      this.clearSignature();
    }, 100);

    this.subscription.add(
      this.route.queryParams.subscribe(params => {
        this.shipmentId = params['shipmentId'] || '';
        this.refId = params['refId'] || '';
        const barcodeDetail = params['barcodeDetail'] ? JSON.parse(params['barcodeDetail']) : null;

        this.formData.shipmentNumber = this.refId;
        this.formData.barcodeNo = barcodeDetail?.barcodeNo;
      })
    );

    // Force UI refresh
    setTimeout(() => {
      this.hasPodInfo = true;
    }, 0);
  }

  async onScanBarcodeClick() {
    const scanned = await this.barcodeService.scanBarcode();
    if (scanned) {
      this.formData.barcodeNo = scanned;
    }
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  private clearFormData() {
    this.showValidationErrors = false;
    this.onClickValidation = false;

    this.formData = {
      shipmentNumber: this.refId || '',
      barcodeNo: '',
      podNotes: '',
      signature: '',
      podImages: []
    };
  }

  private clearUploadedImages() {
    this.uploadedImages = [];
    this.requestImage = null;
  }

  private clearSignature() {
    this.formData.signature = '';
    // Clear the signature pad component if it exists
    if (this.signaturePadComponent) {
      this.signaturePadComponent.clear();
    }
  }

  private clearAllPodData() {
    this.clearFormData();
    this.clearUploadedImages();
    this.clearSignature();
    this.showValidationErrors = false;
    this.onClickValidation = false;
    this.requestImage = null;

    // Force change detection to ensure UI is updated
    this.changeDetectorRef.detectChanges();
  }

  goBack() {
    this.navController.navigateRoot("/portal/shipping", { animated: true });
  }

  onSignatureChange(signatureDataURL: string) {
    this.formData.signature = signatureDataURL;
  }

  async upload() {
    try {
      const response = await Camera.getPhoto({
        quality: 50,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Prompt, // Prompt user to select from camera or files
      });
      if (response.base64String) {
        await this.processCropper(response.base64String);
      }
    } catch (error) {
      //  this.toastService.show("Something went wrong while uploading profile picture.");
    } finally {
      // Restore status bar after camera operation
      await this.restoreStatusBar();
    }
  }

  async processCropper(base64Image: string) {
    const modal = await this.modalCtrl.create({
      component: FileCropperComponent,
      componentProps: {
        file: { base64String: base64Image }
      },
      cssClass: "cropper-modal"
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role !== 'confirm') {
      this.toastService.show("Sorry, profile picture uploading has been cancelled.");
      return;
    }
    if (data && data.croppedFile) {
      await this.uploadImage(data.croppedFile);
    } else {
      this.toastService.show("No file selected after cropping.");
    }
  }

  async uploadImage(blob: Blob): Promise<void> {
    if (!blob) {
      this.toastService.show('No file selected. Please choose an image to upload.');
      return;
    }

    const file = new File([blob], 'cropped-image.png', { type: 'image/png' }); // Convert Blob to File

    const formData = new FormData();
    formData.append('file', file, file.name);

    this.loadingService.show(); // Show loading indicator

    this.subscription.add(
      this.dataService.uploadFile(formData).subscribe({
        next: (response: any) => {
          this.loadingService.hide();
          this.handleUploadResponse(response);
        },
        error: (error) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'An error occurred while uploading the file');
        }
      })
    );
  }

  private handleUploadResponse(response: any): void {
    const attachments = response?.data;

    if (Array.isArray(attachments) && attachments.length > 0) {
      attachments.forEach((attachment: any) => {
        // Use secureUrl for display but preserve clean path for API
        const displayUrl = attachment.secureUrl || attachment.path || attachment.fileName;
        this.uploadedImages.push({
          url: displayUrl, // For display purposes
          secureUrl: attachment.secureUrl || '',
          fileName: attachment.filename || '',
          mimeType: attachment.mimeType || 'image/png',
          size: attachment.size || 0,
          originalName: attachment.originalName || '',
          path: attachment.path || '' // Store clean path separately
        });
      });
      this.changeDetectorRef.detectChanges();
      this.toastService.show('File uploaded successfully.');
    } else {
      this.toastService.show('Failed to upload file.');
    }
  }

  toggleSelected(selectedImage: { url: string, selected?: boolean }) {
    this.uploadedImages.forEach(image => {
      if (image === selectedImage) {
        image.selected = !image.selected; // Toggle current image
      } else {
        image.selected = false; // Deselect others
      }
    });
  }

  onDeleteClick(event: Event, image: { url: string }) {
    event.stopPropagation(); // Prevent parent (click) from firing
    this.removeImage(image);
  }

  removeImage(image: { url: string }) {
    this.uploadedImages = this.uploadedImages.filter(img => img.url !== image.url);
  }

  async viewImage(imageUrl: string, showDelete: boolean = false) {
    const modal = await this.modalCtrl.create({
      component: FullImageComponent,
      componentProps: { imageUrl, showDelete },
      cssClass: 'full-image-modal'
    });
    await modal.present();
    const { data, role } = await modal.onWillDismiss();
    if (role === 'remove' && data) {
      this.removeImage({ url: data }); // use existing method
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;
    if (!form.valid) {
      return
    };

    if (!this.formData.signature || this.formData.signature.trim() === '') {
      this.toastService.show('Please provide a signature.');
      return;
    }

    if (this.uploadedImages.length === 0) {
      this.toastService.show('Please upload at least one POD image before submitting.');
      return;
    }

    // Prepare the API payload
    const payload = {
      id: this.shipmentId,
      barcodeNo: this.formData.barcodeNo.trim(),
      refId: this.refId,
      podNotes: this.formData.podNotes.trim(),
      podSignatures: this.formData.signature,
      podImages: this.generatePodImages()
    };

    // Debug logging to verify payload
    console.log('POD Submission Payload:', payload);
    console.log('Delivery Notes:', this.formData.notes);

    this.loadingService.show();
    this.subscription.add(
      this.dataService.submitPOD(payload).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          if (response.status === 200) {
            this.toastService.show('POD submitted successfully!');
            this.navController.navigateForward('/portal/shipping', {
              queryParams: {
                refresh: 'true',
                goTab: 'COMPLETED'
              }
            });
          } else {
            this.toastService.show(response.message || 'Failed to submit POD');
          }
        },
        error: (error) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'An error occurred while submitting POD');
        }
      })
    );
  }

  private generatePodImages() {
    return this.uploadedImages.map((image) => ({
      filename: image.fileName || '',
      mimeType: image.mimeType || 'image/png',
      size: image.size || 0,
      path: image.path || image.url, // Use clean path if available, fallback to url
      originalName: image.originalName || '',
      secureUrl: image.secureUrl || ''
    }));
  }

  /**
   * Restore status bar configuration after camera operations
   */
  private async restoreStatusBar(): Promise<void> {
    try {
      // Restore status bar for POD page (driver portal)
      // await this.statusBarService.setColorScheme('driverPortal');
    } catch (error) {
      console.error('Error restoring status bar:', error);
    }
  }

}
