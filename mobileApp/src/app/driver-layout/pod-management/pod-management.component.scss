.pod-management-page {
  --background: white;

  .pod-management-body-section {
    display: inline-block;
    width: 100%;
    height: calc(100vh - 140px);
    padding: 0px 20px 10px 20px !important;
    overflow-y: auto;

    .form-container {
      min-height: 250px;
    }

    .info-text {
      font-size: 17px;
      font-weight: bold;
    }

    .browse-file-button {
      --background: #FFEA00 !important;
      --background-activated: #FFEA00 !important;
      --color: black !important;
      min-height: 48px;
      --border-radius: 22px;
      text-transform: capitalize;
      color: black !important;
      font-weight: 600;
      font-size: 16px;
      letter-spacing: 0.5px;
      overflow: unset !important;

      // Ensure text and span are visible
      span {
        color: black !important;
        font-weight: 600;
        font-size: 16px;
        display: block;
        text-transform: capitalize;
        letter-spacing: 0.5px;
      }

      ion-label {
        color: black !important;
      }
    }

    .signature-section {
      .signature-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
        padding-left: 4px;
      }
    }

    // Interactive button styles
    .interactive-button {
      position: relative;
      //  overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      transform: translateZ(0);

      &:hover:not([disabled]) {
        transform: translateY(-2px);
        //  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:active:not([disabled]) {
        transform: translateY(0);
        transition: all 0.1s ease;
      }

      &[disabled] {
        opacity: 0.6;
        transform: none;
        box-shadow: none;
      }

      ion-ripple-effect {
        color: rgba(255, 255, 255, 0.3);
      }

      // Simple cancel button matching app design
      &.ship-cancel-btn {
        background: #ffffff !important;
        --background-activated: #ffffff !important;
        color: #6c757d !important;
        border: 1px solid #dee2e6;
        text-transform: uppercase;
        font-weight: 600;

        &:hover:not([disabled]) {
          background: #f8f9fa !important;
          color: #495057 !important;
          border-color: #adb5bd;
          transform: translateY(-2px);
        }

        &:active:not([disabled]) {
          background: #e9ecef !important;
          color: #495057 !important;
          border-color: #adb5bd;
          transform: translateY(0);
        }

        ion-ripple-effect {
          color: rgba(108, 117, 125, 0.2);
        }
      }
    }

    // Bold cancel button text
    .cancel-text {
      font-weight: 700 !important;
      letter-spacing: 0.5px;
    }

    // Enhanced browse button
    .browse-file-button {
      &:hover:not([disabled]) {
        --background: #FFD700;
        --background-activated: #FFEA00 !important;
        transform: translateY(-1px);
      }
    }
  }
}