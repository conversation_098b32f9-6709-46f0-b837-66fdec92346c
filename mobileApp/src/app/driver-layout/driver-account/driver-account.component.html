<ion-content class="profile-page">
  <app-customer-header [innerPage]="true" [headingText]="'Profile Management'" [rightAction]="false"
    [logoutAction]="true" (logoutActionCallback)="logoutButton()" [hideBellIcon]="true"
    [backUrl]="'/portal/dashboard'"></app-customer-header>

  <div class="profile-page-body-section">
    <div class="margin-top-25">
      <span class="profile-text">Enter Profile Details</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #profileForm="ngForm" novalidate (ngSubmit)="submitProfile(profileForm.form)">

        <div class="margin-top-20 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{ 'is-invalid': firstName.invalid && onClickValidation }">
            <ion-input label="Name" labelPlacement="floating" name="firstName" required
              [(ngModel)]="profileData.firstName" #firstName="ngModel" maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$"
              mode="md">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="firstName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>

        <!-- <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': lastName.invalid && onClickValidation}">
            <ion-input name="lastName" #lastName="ngModel" [(ngModel)]="profileData.lastName" required="required"
              maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Last Name" labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="lastName" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div> -->

        <!-- Phone Number -->
        <!-- <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPhone.invalid && onClickValidation}">
            <img width="30" height="30" [attr.alt]="countryIsoCode" [src]="countryIsoCode" slot="start"
              [style.border-radius.%]="50" *ngIf="countryIsoCode!==''" />
            <input class="custom-mobile-input" inputmode="tel" [attr.pattern]="pattern" [maskito]="mask"
              required="required" name="userPhone" #userPhone="ngModel" [(ngModel)]="profileData.phoneNumber"
              placeholder="Phone Number"
              (ngModelChange)="profileData.phoneNumber = $event.trim(); onPhoneChange(userPhone)" />
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
        </div> -->

        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control phone-input-container" lines="none"
            [ngClass]="{ 'is-invalid': (userPhone.invalid || phoneInvalid) && onClickValidation }">
            <ion-icon src="/assets/images/svg/canada-flag-icon.svg" slot="start" class="start-icon"></ion-icon>
            <div class="phone-input-wrapper">
              <span class="phone-prefix">+1</span>
              <ion-input inputmode="tel" required name="userPhone" #userPhone="ngModel" [(ngModel)]="displayPhoneNumber"
                (ionInput)="formatPhoneNumber($event, userPhone)" placeholder="Phone Number">
              </ion-input>
            </div>
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
          <!-- Show custom message if phone number is invalid length -->
          <div class="error-message" *ngIf="!userPhone.invalid && phoneInvalid && onClickValidation">
            Please provide a valid contact number.
          </div>
        </div>

        <!-- Email (Disabled) -->
        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control disabled-field" lines="none">
            <ion-input label="Email" labelPlacement="floating" name="email" type="email" [(ngModel)]="profileData.email"
              #email="ngModel" readonly="true" disabled="true">
            </ion-input>
          </ion-item>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn" expand="full" shape="round" type="button"
            (click)="cancel()">
            <span>Cancel</span>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn" expand="full" shape="round" type="submit">
            <span>Submit</span>
          </ion-button>
        </div>
      </form>
    </div>

    <div class="shippment-btn-container">
      <ion-button class="margin-top-25 margin-left-30 site-button ship-submit-btn chng-password" expand="full"
        shape="round" type="submit" (click)="changePassword()">
        <span>Change Password</span>
      </ion-button>
    </div>

  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #logoutModal [isOpen]="isLogoutModalOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container heading-setting">
      <div class="site-custom-popup-header no-padding-bottom padding-top-10">
        <i-feather name="X" (click)="closeLogoutModal()"></i-feather>
        <h1>Confirmation!</h1>
      </div>
      <div class="site-custom-popup-body popup-normal-heading ion-padding no-padding-top">
        <div class="custom-modal-text"><span>Are you sure you want to logout?</span>
        </div>
        <div class="main-modal-dismiss button-gap margin-top-20">
          <ion-button class="site-full-rounded-button text-capitalize primary-button no-button" shape="round"
            type="submit" (click)="closeLogoutModal()">No</ion-button>
          <ion-button class="site-full-rounded-button text-capitalize primary-button yes-button" shape="round"
            type="submit" (click)="logout()">Yes</ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>