import { Component, OnInit, OnDestroy } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { UserProfileDetail } from 'src/modals/user-profile-response';
import { RestResponse } from 'src/shared/auth.model';
import mask from 'src/shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { NgModel } from '@angular/forms';
import { DataService } from 'src/services/data.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { AuthService } from 'src/shared/authservice';
import { TrackMyLocationService } from 'src/services/track-my-location.service';

@Component({
  selector: 'app-driver-account',
  templateUrl: './driver-account.component.html',
  styleUrls: ['./driver-account.component.scss'],
  standalone: false
})
export class DriverAccountComponent implements OnInit, OnDestroy {

  onClickValidation!: boolean;
  profileData: UserProfileDetail = new UserProfileDetail();
  activeSubscriptions: Subscription = new Subscription();
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;
  displayPhoneNumber: string = '';
  isLogoutModalOpen: boolean = false;
  phoneInvalid: boolean = false;

  constructor(
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    public commonService: CommonService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly authService: AuthService,
    private trackMyLocationService: TrackMyLocationService,
  ) {

  }

  ngOnInit() {
    this.clearFormData();
  }

  ionViewWillEnter() {
    this.clearFormData();
    this.loadUserProfile();
  }

  private clearFormData() {
    this.onClickValidation = false;
    this.profileData = new UserProfileDetail();
    this.displayPhoneNumber = '';
  }

  loadUserProfile(): void {
    this.loadingService.show();
    this.activeSubscriptions.add(
      this.dataService.getDriverUserProfile().subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          this.profileData = data;

          if (this.profileData.phoneNumber) {
            const digits = this.profileData.phoneNumber.replace(/\D/g, '').slice(0, 10);
            this.profileData.phoneNumber = digits;
            this.displayPhoneNumber = this.commonService.formatPhoneForDisplay(digits);
          }
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to load profile');
        }
      })
    );
  }

  protected get countryIsoCode(): string {
    const phone = this.profileData?.phoneNumber;
    if (!phone || phone.trim() === '') {
      return '';
    }

    const code = maskitoGetCountryFromNumber(phone, metadata) ?? '';
    //  return code ? `/assets/images/icons/flags/${code.toLowerCase()}.png` : '';
    return code ? `/assets/images/icons/flags/ca.png` : '';
  }

  protected get pattern(): string {
    return '^\\+1\\d{10}$'; // +1 followed by exactly 10 digits
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    // Remove non-digits, limit to 10
    const digits = value.replace(/\D/g, '').slice(0, 10);

    // Format for display
    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.displayPhoneNumber = formatted;
    input.value = formatted;

    // Store only digits (no +1, no dashes, no spaces)
    this.profileData.phoneNumber = digits;

    // Only valid if exactly 10 digits
    this.phoneInvalid = digits.length !== 10;

    if (this.phoneInvalid) {
      userPhone.control.setErrors({ invalid: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }

    if (this.profileData.phoneNumber) {
      const phone = this.profileData.phoneNumber.trim();
      this.profileData.phoneNumber = this.commonService.formatPhoneNumberForApi(phone);
    }

    this.loadingService.show();
    this.activeSubscriptions.add(
      this.dataService.updateDriverUserProfile(this.profileData).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          this.toastService.show('Profile updated successfully!');

          this.loadDataAfterSubmitProfile();
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'Failed to update profile');
        }
      })
    );
  }

  loadDataAfterSubmitProfile(): void {
    this.activeSubscriptions.add(
      this.dataService.getDriverUserProfile().subscribe({
        next: (response: RestResponse) => {

          const data = response.data;
          const storedUser = this.localStorageService.getObject('user');
          if (storedUser) {
            storedUser.firstName = data.firstName;
            storedUser.lastName = data.lastName;
            this.localStorageService.setObject('user', storedUser);
          }
          this.navController.navigateForward("/portal/dashboard", { animated: true });
        },
        error: (error) => {
        }
      })
    );
  }

  cancel() {
    this.navController.navigateRoot("/portal/dashboard", { animated: true });
  }

  changePassword() {
    this.navController.navigateForward("/portal/change/password", { animated: true });
  }

  closeLogoutModal() {
    this.isLogoutModalOpen = false;
  }

  openLogoutModal() {
    this.isLogoutModalOpen = true;
  }

  logoutButton() {
    this.openLogoutModal();
  }

  async logout(): Promise<void> {
    this.closeLogoutModal();

    setTimeout(async () => {
      try {
        this.loadingService.show();

        // Stop location tracking service before logout
        if (this.authService.isDriver()) {
          try {
            await this.trackMyLocationService.stopService();
            console.log('Location service stopped during logout');
          } catch (error) {
            console.error('Error stopping location service during logout:', error);
          }
        }

        const deviceId = this.localStorageService.get('device-uuid') || '';
        const logoutPayload = {
          deviceId: deviceId
        };
        await this.dataService.logout(logoutPayload).toPromise();

        this.authService.logout();

        this.loadingService.hide();
        await this.navController.navigateRoot('/account/login', {
          animated: true,
          animationDirection: 'back'
        });
        this.toastService.show('Logged out successfully');
      } catch (error) {
        this.loadingService.hide();

        this.authService.logout();
        await this.navController.navigateRoot('/account/login', {
          animated: true,
          animationDirection: 'back'
        });
      }
    }, 200); // Delay after modal close
  }

  ngOnDestroy(): void {
    this.activeSubscriptions.unsubscribe();
  }

}
