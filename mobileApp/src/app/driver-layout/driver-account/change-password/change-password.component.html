<ion-content class="change-password-page">
  <div class="safe-area-yellow-section" *ngIf="authService.isDriver() && user?.isPasswordChanged === false"></div>

  <div class="change-password-header-section" *ngIf="authService.isDriver() && user?.isPasswordChanged === false">
    <ion-buttons slot="start" class="back-button">
      <ion-button (click)="goToLoginPage()" fill="clear">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <img src="assets/images/icons/common-header.png" />
    <img class="bees-logo-image" src="assets/images/icons/bees-logo.png" />
  </div>

  <app-customer-header *ngIf="authService.isDriver() && user?.isPasswordChanged" [innerPage]="true"
    [headingText]="'Change Password'" [rightAction]="false" [hideBellIcon]="true"
    [backUrl]="'/portal/account'"></app-customer-header>

  <div class="change-password-page-container">

    <div class="change-password-container margin-bottom-10">
      <ion-icon class="change-password-icon" src="assets/images/svg/forgot-pass-icon.svg" slot="start"></ion-icon>
    </div>

    <div class="change-password-container">
      <span class="page-heading">Change<strong> Password</strong></span>
    </div>

    <!-- Container for the Change Password form -->
    <div class="form-container">
      <form #changePasswordForm="ngForm" novalidate="novalidate" class="custom-form" autocomplete="off"
        (ngSubmit)="changePassword(changePasswordForm.form)">

        <div class="margin-top-20">
          <ng-container *ngIf="hasChangePasswordInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid':!password.valid && onClickValidation}">
              <ion-input label="Current Password*" labelPlacement="floating" mode="md" [type]="passwordFieldType"
                name="password" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
                [ngClass]="{'is-invalid':password.invalid && onClickValidation}" #password="ngModel"
                [(ngModel)]="data.oldPassword" required="required"></ion-input>
              <i-feather name="eye" (click)="eyePassword()"
                *ngIf="data.oldPassword && data.oldPassword.length>0 && passwordFieldType==='password'"></i-feather>
              <i-feather name="eye-off" (click)="eyePassword()"
                *ngIf="data.oldPassword && data.oldPassword.length>0 && passwordFieldType!=='password'"></i-feather>
            </ion-item>
            <app-validation-message [field]="password" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Password must contain a capital letter, number and special character & be greater than 8 characters'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10">
          <ng-container *ngIf="hasChangePasswordInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid':!newPasswords.valid && onClickValidation}">
              <ion-input label="New Password*" labelPlacement="floating" mode="md" [type]="passwordFieldType"
                name="newPasswords" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
                [ngClass]="{'is-invalid':newPasswords.invalid && onClickValidation}" #newPasswords="ngModel"
                [(ngModel)]="data.password" required="required"></ion-input>
              <i-feather name="eye" (click)="eyePassword()"
                *ngIf="data.password && data.password.length>0 && passwordFieldType==='password'"></i-feather>
              <i-feather name="eye-off" (click)="eyePassword()"
                *ngIf="data.password && data.password.length>0 && passwordFieldType!=='password'"></i-feather>
            </ion-item>
            <app-validation-message [field]="newPasswords" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Password must contain a capital letter, number and special character & be greater than 8 characters'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10">
          <ng-container *ngIf="hasChangePasswordInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid':!confirmNewPassword.valid && onClickValidation}">
              <ion-input label="Confirm New Password*" labelPlacement="floating" mode="md" [type]="passwordFieldType"
                name="confirmNewPassword" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
                [ngClass]="{'is-invalid':confirmNewPassword.invalid && onClickValidation}" #confirmNewPassword="ngModel"
                [(ngModel)]="data.confirmPassword" required="required"></ion-input>
              <i-feather name="eye" (click)="eyePassword()"
                *ngIf="data.confirmPassword && data.confirmPassword.length>0 && passwordFieldType==='password'"></i-feather>
              <i-feather name="eye-off" (click)="eyePassword()"
                *ngIf="data.confirmPassword && data.confirmPassword.length>0 && passwordFieldType!=='password'"></i-feather>
            </ion-item>
            <app-validation-message [field]="confirmNewPassword" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Password must contain a capital letter, number and special character & be greater than 8 characters'">
            </app-validation-message>
          </ng-container>
        </div>

        <!-- Submit button for the Change Password form -->
        <ion-button class="site-button change-password-button margin-top-20" expand="full" shape="round" type="submit">
          <span>Update Password</span>
        </ion-button>
      </form>
    </div>

  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #changePasswordModal
  [isOpen]="commonService.isChangePasswordModalOpen" [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container heading-setting">
      <div class="site-custom-popup-header no-padding-bottom padding-top-10">
        <i-feather name="X" (click)="changePasswordConfirmation()"></i-feather>
        <h1>Password Updated!</h1>
      </div>
      <div class="site-custom-popup-body popup-normal-heading ion-padding no-padding-top">
        <div class="custom-modal-text">
          <span>Your password has been changed! We’ve logged you out to keep your account safe. Please log in again with
            your new password.</span>
        </div>
        <div class="main-modal-dismiss button-gap margin-top-20">
          <ion-button class="site-full-rounded-button text-capitalize primary-button yes-button" shape="round"
            type="submit" (click)="changePasswordConfirmation()">OK</ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>