import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Style } from '@capacitor/status-bar';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { StatusBarService } from 'src/services/status-bar.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss'],
  standalone: false
})
export class ChangePasswordComponent implements OnInit, OnDestroy {

  user: any;
  onClickValidation: boolean = false;
  passwordFieldType: string = 'password';
  data: any = {};
  subscription: Subscription = new Subscription(); // Subscription to manage observable lifecycle
  hasChangePasswordInfo = true;

  constructor(
    private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private statusBarService: StatusBarService,
    private readonly cdr: ChangeDetectorRef,
    public readonly authService: AuthService,
    public readonly commonService: CommonService
  ) {

  }

  ngOnInit() {
    this.resetForm();
  }

  ionViewWillEnter() {
    this.statusBar();
    this.resetForm();
    this.hasChangePasswordInfo = false;
    this.user = this.authService.getUser();

    // Force UI refresh
    setTimeout(() => {
      this.hasChangePasswordInfo = true;
    }, 0);
  }

  statusBar() {
    this.statusBarService.setColorScheme('authentication');

    // Or set custom colors
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#FFEA00', // Orange
      style: Style.Light // Light text on dark background
    });
  }

  resetForm() {
    this.data = {} as any;
    this.onClickValidation = false;

    // Trigger change detection
    this.cdr.detectChanges();
  }

  validatePasswords(): boolean {
    if (this.data.password !== this.data.confirmPassword) {
      this.toastService.show('New Password and Confirm Password do not match');
      return false;
    }
    return true;
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  goToLoginPage() {
    this.resetForm();
    this.authService.logout();
    setTimeout(() => {
      this.navController.navigateRoot('/account/login', { animated: true });
    }, 200);
  }

  async changePassword(form: any): Promise<void> {
    this.onClickValidation = true;
    if (!form.valid || !this.validatePasswords()) {
      return;
    }
    this.loadingService.show();
    this.dataService.changePassword(this.data).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        if (this.authService.isDriver() && this.user?.isPasswordChanged === false) {
          this.changePasswordConfirmation();
          return;
        }
        this.openChangePasswordModal();
      },
      error: (error: any) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  closeChangePasswordModal() {
    this.commonService.isChangePasswordModalOpen = false;
  }

  openChangePasswordModal() {
    this.commonService.isChangePasswordModalOpen = true;
  }

  changePasswordConfirmation() {
    this.closeChangePasswordModal();

    this.authService.logout();
    setTimeout(() => {
      this.navController.navigateRoot('/account/login', { animated: true });
    }, 200);
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
