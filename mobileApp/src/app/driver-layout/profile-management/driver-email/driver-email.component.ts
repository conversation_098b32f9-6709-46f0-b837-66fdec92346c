import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { ProfileDetail } from 'src/modals/profileDetail';
import { DataService } from 'src/services/data.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import mask from 'src/shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { NgModel } from '@angular/forms';

@Component({
  selector: 'app-driver-email',
  templateUrl: './driver-email.component.html',
  styleUrls: ['./driver-email.component.scss'],
  standalone: false
})
export class DriverEmailComponent implements OnInit {

  onClickValidation!: boolean;
  profile: ProfileDetail = new ProfileDetail();
  passwordFieldType: string = 'password';
  confirmPassword: string | null = null;
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;
  displayPhoneNumber: string = '';
  phoneInvalid: boolean = false;

  constructor(private readonly navController: NavController,
    private readonly localStorageService: LocalStorageService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
  ) {

  }
  ionViewWillEnter() {
    // Use predefined color scheme
    // this.statusBarService.setColorScheme('authentication');
    // // Or set custom colors
    // this.statusBarService.setCustomStatusBar({
    //   backgroundColor: '#FFEA00', // Orange
    //   style: Style.Light // Light text on dark background
    // });
  }
  ngOnInit() {
    this.clearFormData();
  }

  ionViewDidEnter() {
    this.clearFormData();
  }

  private clearFormData() {
    this.onClickValidation = false;
    // Clear the stored onboarding data
    this.localStorageService.remove("CUSTOMER_ONBOARDING");
    this.profile = new ProfileDetail();
    this.displayPhoneNumber = '';
    this.confirmPassword = null;
  }

  goToLogin() {
    this.localStorageService.remove("CUSTOMER_ONBOARDING");
    this.navController.navigateRoot("/account/login", { animated: true });
  }

  validatePasswords(): boolean {
    if (this.profile.password !== this.confirmPassword) {
      this.toastService.show('Password and Confirm Password do not match');
      return false;
    }
    return true;
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  protected get countryIsoCode(): string {
    const phone = this.profile?.phoneNumber;
    if (!phone || phone.trim() === '') {
      return '';
    }

    const code = maskitoGetCountryFromNumber(phone, metadata) ?? '';
    //  return code ? `/assets/images/icons/flags/${code.toLowerCase()}.png` : '';
    return code ? `/assets/images/icons/flags/ca.png` : '';
  }

  protected get pattern(): string {
    return '^\\+1\\d{10}$'; // +1 followed by exactly 10 digits
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    // Remove non-digits, limit to 10
    const digits = value.replace(/\D/g, '').slice(0, 10);

    // Format for display
    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.displayPhoneNumber = formatted;
    input.value = formatted;

    // Store only digits (no +1, no dashes, no spaces)
    this.profile.phoneNumber = digits;

    // Only valid if exactly 10 digits
    this.phoneInvalid = digits.length !== 10;

    if (this.phoneInvalid) {
      userPhone.control.setErrors({ invalid: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.validatePasswords()) {
      return;
    }
    this.localStorageService.setObject("CUSTOMER_ONBOARDING", this.profile);
    this.navController.navigateForward("/account/register/address", { animated: true });
  }

}
