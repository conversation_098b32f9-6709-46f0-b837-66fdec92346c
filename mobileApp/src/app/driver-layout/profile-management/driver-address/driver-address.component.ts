import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { AddressDetail, ProfileDetail } from 'src/modals/profileDetail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { RestResponse } from 'src/shared/auth.model';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';
import { Geolocation } from '@capacitor/geolocation';
import { LoadingController } from '@ionic/angular';
import { LoadingService } from 'src/services/loading.service';
import { StatusBarService } from 'src/services/status-bar.service';
import { Style } from '@capacitor/status-bar';
import { GooglePlacesService } from 'src/services/google-places-service';


@Component({
  selector: 'app-driver-address',
  templateUrl: './driver-address.component.html',
  styleUrls: ['./driver-address.component.scss'],
  standalone: false
})
export class DriverAddressComponent implements OnInit, OnDestroy {

  onClickValidation!: boolean;
  isNoAddressFoundPopupOpen: boolean = false;
  profile: ProfileDetail = new ProfileDetail();
  subscription: Subscription = new Subscription();
  currentAddress: any;
  isLocationSelectionPopupOpen: boolean = false;
  searchLocation: string | null = null;
  availablePlaces: any[] = [];

  constructor(private readonly navController: NavController,
    public commonService: CommonService,
    private readonly localStorageService: LocalStorageService,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly loadingController: LoadingController,
    private loadingService: LoadingService,
    private statusBarService: StatusBarService,
    private googlePlaces: GooglePlacesService,
  ) {

  }

  ngOnInit() {
    this.onClickValidation = false;
    this.searchLocation = null;

    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();
  }

  ionViewWillEnter() {
    this.statusBarService.setColorScheme('authentication');
    // Or set custom colors
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#FFEA00',
      style: Style.Light // Light text on dark background
    });
  }

  ionViewDidEnter() {
    this.onClickValidation = false;
    this.searchLocation = null;

    const profile = this.localStorageService.getObject("CUSTOMER_ONBOARDING");
    this.profile = profile ? ProfileDetail.fromResponse(profile) : new ProfileDetail();

    if (!this.profile.addressDetail) {
      this.profile.addressDetail = new AddressDetail();
    }
    //  this.fetchAddressFromCurrentLocation();
  }

  openNoAddressFoundPopup() {
    this.isNoAddressFoundPopupOpen = true;
  }

  closeNoAddressFoundPopup() {
    this.isNoAddressFoundPopupOpen = false;
  }

  openLocationSelectionPopup() {
    this.isLocationSelectionPopupOpen = true;
  }

  closeLocationSelectionPopup() {
    this.isLocationSelectionPopupOpen = false;
    this.searchLocation = null;
    this.availablePlaces = [];
  }

  async fetchPlaces(): Promise<void> {
    if (!this.searchLocation) {
      this.availablePlaces = [];
      return;
    }
    try {
      this.availablePlaces = await this.googlePlaces.fetchAutocomplete(this.searchLocation.trim());
    } catch (error: any) {
      this.availablePlaces = [];
    }
  }

  async onSelectLocation(place: any): Promise<void> {
    try {
      const result = await this.googlePlaces.fetchPlaceDetails(place.place_id);
      const components = result.address_components;

      const getComponent = (type: string) =>
        components.find((c: any) => c.types.includes(type))?.long_name || '';

      const lat = result.geometry?.location?.lat?.();
      const lng = result.geometry?.location?.lng?.();

      this.profile.addressDetail = {
        address: result.formatted_address,
        city: getComponent('locality') || getComponent('sublocality'),
        state: getComponent('administrative_area_level_1'),
        pin: getComponent('postal_code'),
        country: getComponent('country'),
        latitude: lat?.toString() || '',
        longitude: lng?.toString() || '',
      };
      this.searchLocation = this.profile.addressDetail.address;
      this.availablePlaces = [];
      this.toastService.show('Address fetched successfully!');
      this.closeLocationSelectionPopup();
    } catch (error: any) {
      this.toastService.show(error.message || 'Error fetching place details');
    }
  }

  goToLogin() {
    this.localStorageService.remove("CUSTOMER_ONBOARDING");
    this.navController.navigateRoot("/account/login", { animated: true });
  }

  retry() {
    this.fetchAddressFromCurrentLocation();
    this.closeNoAddressFoundPopup();
  }

  async fetchAddressFromCurrentLocation() {
    let loading = await this.loadingController.create({
      message: 'Fetching address...',
      spinner: 'circles',
      cssClass: 'custom-loading-popup',
      backdropDismiss: true
    });

    await loading.present();

    const dismissLoading = async () => {
      try {
        await loading?.dismiss();
      } catch (e) {
        // Loading already dismissed
      }
    };

    try {
      const permissions = await Geolocation.checkPermissions();

      const locationPermissionGranted = permissions.location === 'granted';

      if (!locationPermissionGranted) {
        await dismissLoading();

        this.toastService.show('Please allow location access when prompted');
        await new Promise(resolve => setTimeout(resolve, 1000));

        const requestResult = await Geolocation.requestPermissions();

        if (requestResult.location !== 'granted') {
          this.toastService.show(
            requestResult.location === 'denied'
              ? 'Location permission denied. Please enable it in Settings > Privacy > Location Services.'
              : 'Location permission is required to fetch address'
          );
          return;
        }

        // Re-create loading
        loading = await this.loadingController.create({
          message: 'Fetching address...',
          spinner: 'circles',
          cssClass: 'custom-loading-popup',
          backdropDismiss: false
        });
        await loading.present();
      }

      // Fetch current location
      const position = await Geolocation.getCurrentPosition({
        enableHighAccuracy: true,
        timeout: 15000
      });

      const lat = position.coords.latitude;
      const lng = position.coords.longitude;

      // Get address from lat/lng using reverse geocoding
      const response = await this.getAddressFromCoords(lat, lng);

      if (response?.results?.length > 0) {
        const addressComponents = response.results[0].address_components;
        const getComponent = (type: string) =>
          addressComponents.find((c: { types: string[] }) => c.types.includes(type))?.long_name || '';

        const fullAddress = response.results[0].formatted_address;
        this.profile.addressDetail.address = fullAddress;
        this.profile.addressDetail.city = getComponent('locality');
        this.profile.addressDetail.state = getComponent('administrative_area_level_1');
        this.profile.addressDetail.pin = getComponent('postal_code');
        this.profile.addressDetail.country = getComponent('country');

        this.profile.addressDetail.latitude = lat.toString();
        this.profile.addressDetail.longitude = lng.toString();

        this.toastService.show('Address fetched successfully!');
      } else {
        this.toastService.show('Address not found for current location');
      }

    } catch (error: any) {

      const message = error?.message?.toLowerCase() || '';

      if (message.includes('permission')) {
        this.toastService.show('Location permission is required to fetch address');
      } else if (message.includes('timeout')) {
        this.toastService.show('Location request timed out. Please try again.');
        this.openNoAddressFoundPopup();
      } else if (message.includes('unavailable') || message.includes('position_unavailable')) {
        this.toastService.show('Location is currently unavailable. Please try again.');
        this.openNoAddressFoundPopup();
      } else {
        this.openNoAddressFoundPopup();
      }
    } finally {
      await dismissLoading();
    }
  }

  async getAddressFromCoords(lat: number, lng: number): Promise<any> {
    const apiKey = 'AIzaSyDjwD-3pwe36dk5kv_dkEzsYGirWZPWCiY';
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`;

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      if (data.status === 'OK') {
        return data;
      } else {
        throw new Error(`Geocoding failed: ${data.status}`);
      }
    } catch (e) {
      throw e;
    }
  }

  get isCityOnlySpaces(): boolean {
    const value = this.profile?.addressDetail?.city;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isProvinceOnlySpaces(): boolean {
    const value = this.profile?.addressDetail?.state;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isPostalOnlySpaces(): boolean {
    const value = this.profile?.addressDetail?.pin;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isCountryOnlySpaces(): boolean {
    const value = this.profile?.addressDetail?.country;
    return typeof value === 'string' && value.trim().length === 0;
  }

  async continue(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid) {
      return;
    }

    if (this.profile.phoneNumber) {
      const phone = this.profile.phoneNumber.trim();
      this.profile.phoneNumber = this.commonService.formatPhoneNumberForApi(phone);
    }
    this.profile.addressDetail.city = this.profile.addressDetail.city?.trim() || null;
    this.profile.addressDetail.state = this.profile.addressDetail.state?.trim() || null;
    this.profile.addressDetail.pin = this.profile.addressDetail.pin?.trim() || null;
    this.profile.addressDetail.country = this.profile.addressDetail.country?.trim() || null;

    this.loadingService.show();
    this.subscription = this.dataService.customerOnboarding(this.profile)
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          this.toastService.show(response.message || "Account Registered Successfully");
          this.localStorageService.remove("CUSTOMER_ONBOARDING");
          this.navController.navigateRoot("/account/login", { animated: true });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
  }

  ngOnDestroy(): void {
    // Unsubscribe from any subscriptions to prevent memory leaks
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
