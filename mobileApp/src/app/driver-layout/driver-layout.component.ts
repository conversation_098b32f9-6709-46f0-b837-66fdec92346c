import { ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { NavController, Platform } from '@ionic/angular';
import { filter, Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { EventService } from 'src/shared/event.service';
import { FcmService } from 'src/shared/fcm.service';

@Component({
  selector: 'app-driver-layout',
  templateUrl: './driver-layout.component.html',
  styleUrls: ['./driver-layout.component.scss'],
  standalone: false
})
export class DriverLayoutComponent implements OnInit, OnDestroy {

  showTabs = true;
  private routeSub!: Subscription;

  constructor(private readonly navController: NavController,
    private readonly router: Router,
    private readonly platform: Platform,
    public readonly commonService: CommonService,
    private readonly eventService: EventService,
    private readonly fcmService: FcmService,
    private readonly changeDetectorRef: ChangeDetectorRef
  ) {

  }

  ngOnInit() {
    this.platform.ready().then(() => {
      if (this.platform.is('cordova')) {
        this.fcmService.initializeFirebaseToken();
      }
    });

    this.routeSub = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.checkRoute();
      });

    this.eventService.event.subscribe((data) => {
      if (!data) {
        return;
      }
      if (data.key === "fcm:notification") {
        this.processNotifications(JSON.parse(JSON.stringify(data.value)))
      }
      data = undefined;
    });
  }

  ionViewWillEnter() {
  }

  processNotifications(data: any) {
    let queryParams: any = {
      targetId: data.targetId,
      targetType: data.targetType,
      message: data.message,
      status: data.status
    };

    if (data.targetType === "SHIPMENT") {
      if (data.status === "NEW" || data.status === "ASSIGNED") {
        queryParams.refresh = 'true';
        queryParams.goTab = 'PENDING';
      } else if (data.status === "IN_TRANSIT") {
        queryParams.refresh = 'true';
        queryParams.goTab = 'IN_TRANSIT_TAB';
      } else if (data.status === "DELIVERED" || data.status === "COMPLETED") {
        queryParams.refresh = 'true';
        queryParams.goTab = 'COMPLETED';
      }
      this.navController.navigateForward('/portal/shipping', {
        queryParams,
        animated: true
      });
    }

    setTimeout(() => this.changeDetectorRef.detectChanges(), 200);
  }

  private checkRoute() {
    const currentRoute = this.router.url;

    const hideTabRoutes = [
      '/portal/dashboard',
      '/portal/shipping',
      '/portal/fuel/receipt',
      '/portal/receipt/list',
      '/portal/calendar',
      '/portal/account',
      '/portal/basic/info',
      '/portal/other/info',
      '/portal/pickup/delivery',
      '/portal/delivery/location',
      '/portal/cargo/listing',
      '/portal/add/cargo/details',
      '/portal/special/request',
      '/portal/start-shipment',
      '/portal/shipment/details'
    ];

    this.showTabs = hideTabRoutes.some(route => currentRoute.startsWith(route));

    // Set active tab manually
    if (currentRoute.startsWith('/portal/basic/info') || currentRoute.startsWith('/portal/other/info') ||
      currentRoute.startsWith('/portal/pickup/delivery') || currentRoute.startsWith('/portal/delivery/location')
      || currentRoute.startsWith('/portal/cargo/listing') || currentRoute.startsWith('/portal/add/cargo/details')
      || currentRoute.startsWith('/portal/special/request') || currentRoute.startsWith('/portal/start-shipment') ||
      currentRoute.startsWith('/portal/shipment/details')) {
      this.commonService.activeTab = 'shipping';
    } else if (currentRoute.startsWith('/portal/shipping')) {
      this.commonService.activeTab = 'shipping';
    } else if (currentRoute.startsWith('/portal/fuel/receipt') || currentRoute.startsWith('/portal/receipt/list')) {
      this.commonService.activeTab = 'fuel/receipt';
    } else if (currentRoute.startsWith('/portal/calendar')) {
      this.commonService.activeTab = 'calendar';
    } else if (currentRoute.startsWith('/portal/account')) {
      this.commonService.activeTab = 'account';
    } else {
      this.commonService.activeTab = '';
    }
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }

}
