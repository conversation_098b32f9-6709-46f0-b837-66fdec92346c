.fuel-receipts-list-page {
    --background: white;
    height: 100%;

    .fuel-receipts-body-section {
        display: inline-block;
        width: 100%;
        height: calc(100vh - 210px);
        padding: 0px 20px 10px 20px !important;
        overflow-y: auto;

        .info-text {
            font-size: 17px;
            font-weight: bold;
        }

        .loading-container {
            text-align: center;
            padding: 50px 20px;
            
            ion-spinner {
                margin-bottom: 20px;
            }
            
            p {
                color: #666;
                font-size: 16px;
            }
        }

        .empty-state {
            text-align: center;
            padding: 50px 20px;
            
            .empty-icon {
                font-size: 64px;
                color: #ccc;
                margin-bottom: 20px;
            }
            
            h3 {
                color: #333;
                margin-bottom: 10px;
            }
            
            p {
                color: #666;
                margin-bottom: 30px;
            }
            
            ion-button {
                --background: #FFEA00;
                --color: black;
                font-weight: 600;
            }
        }

        .receipts-list {
            margin-top: 20px;
            
            .receipt-card {
                margin-bottom: 15px;
                border-radius: 12px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                
                ion-card-content {
                    padding: 16px;
                }
                
                .receipt-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 15px;
                    
                    .receipt-info {
                        flex: 1;
                        
                        h3 {
                            margin: 0 0 5px 0;
                            font-size: 16px;
                            font-weight: 600;
                            color: #333;
                        }
                        
                        .fuel-type {
                            margin: 0;
                            font-size: 12px;
                            color: #666;
                            background: #f0f0f0;
                            padding: 2px 8px;
                            border-radius: 10px;
                            display: inline-block;
                        }
                    }
                    
                    .receipt-actions {
                        display: flex;
                        gap: 5px;
                        
                        ion-button {
                            --padding-start: 8px;
                            --padding-end: 8px;
                            
                            ion-icon {
                                font-size: 18px;
                            }
                        }
                    }
                }
                
                .receipt-details {
                    .detail-row {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 8px;
                        
                        .label {
                            font-size: 14px;
                            color: #666;
                            font-weight: 500;
                        }
                        
                        .value {
                            font-size: 14px;
                            color: #333;
                            font-weight: 600;
                        }
                    }
                }
                
                .receipt-images {
                    margin-top: 15px;
                    padding-top: 15px;
                    border-top: 1px solid #eee;
                    
                    .images-label {
                        font-size: 12px;
                        color: #007bff;
                        font-weight: 500;
                    }
                }
            }
        }
    }
}
