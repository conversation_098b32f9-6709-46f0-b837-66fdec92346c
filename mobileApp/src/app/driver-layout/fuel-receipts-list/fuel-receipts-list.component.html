<ion-content class="fuel-receipts-list-page">
  <app-customer-header [innerPage]="true" [headingText]="'Fuel Receipts'" [backUrl]="'/portal/dashboard'"
    [rightAction]="false" [showAddNew]="true" [addNewText]="'Add New'" [hideBellIcon]="true"
    (addNewCallback)="navigateToAddNew()"></app-customer-header>

  <div class="fuel-receipts-body-section">
    <!-- <div class="margin-top-25" *ngIf="fuelReceipts.length > 0">
      <span class="info-text">Saved Fuel Receipts</span>
    </div> -->

    <!-- Empty State -->
    <div class="empty-state no-height" *ngIf="fuelReceipts.length <= 0">
      <div class="empty-content">
        <ion-icon name="cube-outline" class="empty-icon"></ion-icon>
        <h3>No Fuel Receipts Found</h3>
        <p>You haven't added any fuel receipts yet.</p>
        <ion-button expand="block" shape="round" (click)="navigateToAddNew()">
          Add Your First Receipt
        </ion-button>
      </div>
    </div>

    <!-- Fuel Receipts List -->
    <div *ngIf="fuelReceipts.length > 0" class="receipts-list">
      <ion-card *ngFor="let receipt of fuelReceipts" class="receipt-card">
        <ion-card-content>
          <div class="receipt-header">
            <div class="receipt-info">
              <h3>{{ receipt.vehicleDetail.name || 'Unknown Vehicle' }}</h3>
              <p class="fuel-type">{{ receipt.fuelType }}</p>
            </div>
            <div class="receipt-actions">
              <!-- <ion-button fill="clear" size="small" (click)="editReceipt(receipt)">
                <ion-icon name="create-outline"></ion-icon>
              </ion-button> -->
              <!-- <ion-button fill="clear" size="small" color="danger" (click)="deleteReceipt(receipt)">
                <ion-icon name="trash-outline"></ion-icon>
              </ion-button> -->
            </div>
          </div>

          <div class="receipt-details">
            <div class="detail-row">
              <span class="label">Meter Reading:</span>
              <span class="value">{{ receipt.meterReading }} km</span>
            </div>
            <div class="detail-row">
              <span class="label">Fuel:</span>
              <span class="value">{{ receipt.fuelInLiters }} L</span>
            </div>
            <div class="detail-row">
              <span class="label">Fuel Cost:</span>
              <span class="value">${{ receipt.fuelCost }}</span>
            </div>
            <div class="detail-row" *ngIf="receipt.createdOn">
              <span class="label">Date:</span>
              <span class="value">{{ commonService.formatDisplayDate(receipt.createdOn) }}</span>
            </div>
          </div>

          <!-- <div class="receipt-images" *ngIf="receipt.totalCount && receipt.totalCount > 0">
            <span class="images-label">Attachments: {{ receipt.totalCount }}</span>
          </div> -->
        </ion-card-content>
      </ion-card>
    </div>
  </div>
</ion-content>