import { Component, OnInit } from '@angular/core';
import { NavController, AlertController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { RestResponse } from 'src/shared/auth.model';
import { CommonService } from 'src/services/common.service';

@Component({
  selector: 'app-fuel-receipts-list',
  templateUrl: './fuel-receipts-list.component.html',
  styleUrls: ['./fuel-receipts-list.component.scss'],
  standalone: false
})
export class FuelReceiptsListComponent implements OnInit {

  fuelReceipts: Array<any> = new Array<any>();
  vehicles: Array<any> = new Array<any>();
  isLoading: boolean = false;

  constructor(
    private navController: NavController,
    private dataService: DataService,
    private loadingService: LoadingService,
    private toastService: ToastService,
    private alertController: AlertController,
    private readonly localStorageService: LocalStorageService,
    public readonly commonService: CommonService
  ) {

  }

  ngOnInit() {

  }

  ionViewWillEnter() {
    this.loadFuelReceipts();
    this.loadVehicles();
  }

  async loadFuelReceipts() {
    this.loadingService.show();
    this.dataService.getFuelReceipts().subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const data = response.data;
        this.fuelReceipts = data;
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  async loadVehicles() {
    this.loadingService.show();
    this.dataService.getVehicleSelection().subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const data = response.data;
        this.vehicles = data;
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  navigateToAddNew() {
    this.navController.navigateForward('/portal/fuel/receipt', { animated: true });
  }

  editReceipt(receipt: any) {
    this.localStorageService.set("FUEL_EDIT_MODE", "edit");
    this.localStorageService.set("FUEL_EDIT_ID", receipt.id);
    this.navController.navigateForward('/portal/fuel/receipt', {
      animated: true
    });
  }

  async deleteReceipt(receipt: any) {
    const alert = await this.alertController.create({
      header: 'Confirm Delete',
      message: `Are you sure you want to delete this fuel receipt?`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          role: 'destructive',
          handler: () => {
            this.performDelete(receipt.id);
          }
        }
      ]
    });
    await alert.present();
  }

  async performDelete(receiptId: string) {
    this.loadingService.show();
    this.dataService.deleteFuelReceipt(receiptId).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        this.fuelReceipts = this.fuelReceipts.filter(r => r.id !== receiptId);
        this.toastService.show('Fuel receipt deleted successfully!');
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

}
