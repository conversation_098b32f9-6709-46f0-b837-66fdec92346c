.start-shipment-page {
  --background: white;
  height: 100%;

  .start-shipment-body-section {
    display: inline-block;
    width: 100%;
    height: calc(100vh - 210px);
    padding: 0px 20px 10px 20px !important;
    overflow-y: auto;

    .form-container {
      min-height: 250px;
    }

    .info-text {
      font-size: 17px;
      font-weight: bold;
    }

    .browse-file-button {
      --background: #FFEA00 !important;
      --background-activated: #FFEA00 !important;
      --color: black !important;
      min-height: 48px;
      --border-radius: 22px;
      text-transform: capitalize;
      color: black !important;
      font-weight: 600;
      font-size: 16px;
      letter-spacing: 0.5px;
      overflow: unset !important;

      // Ensure text and span are visible
      span {
        color: black !important;
        font-weight: 600;
        font-size: 16px;
        display: block;
        text-transform: capitalize;
        letter-spacing: 0.5px;
      }

      ion-label {
        color: black !important;
      }
    }

    // Interactive button styles
    .interactive-button {
      position: relative;
      //  overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      transform: translateZ(0);

      &:hover:not([disabled]) {
        transform: translateY(-2px);
        //  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:active:not([disabled]) {
        transform: translateY(0);
        transition: all 0.1s ease;
      }

      &[disabled] {
        opacity: 0.6;
        transform: none;
        box-shadow: none;
      }

      ion-ripple-effect {
        color: rgba(255, 255, 255, 0.3);
      }
    }

    // Simple cancel button matching app design
    .ship-cancel-btn {
      background: #ffffff;
      --background-activated: #ffffff !important;
      color: #6c757d;
      border: 1px solid #dee2e6;
      text-transform: uppercase;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        background: #f8f9fa;
        color: #495057;
        border-color: #adb5bd;
      }

      &:active {
        background: #e9ecef;
        color: #495057;
        border-color: #adb5bd;
      }

      ion-ripple-effect {
        color: rgba(108, 117, 125, 0.2);
      }

    }

    // Enhanced button container styling
    .shippment-btn-container {
      display: flex;
      gap: 10px;
      margin-top: 20px;
      //  padding: 0 10px;
      position: relative;

      // Subtle background animation
      &::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, transparent, rgba(255, 234, 0, 0.1), transparent);
        border-radius: 20px;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -1;
      }

      &:hover::before {
        opacity: 1;
      }

      .site-button {
        flex: 1;
        height: 54px;
        border-radius: 16px;
        font-weight: 600;
        font-size: 16px;
        text-transform: none;
        position: relative;
        overflow: hidden;
        user-select: none;
        box-shadow: none !important;
        --box-shadow: none !important;
      }
    }

    // Enhanced browse button
    .browse-file-button {
      &:hover:not([disabled]) {
        --background: #FFD700;
        transform: translateY(-1px);
      }
    }
  }

}