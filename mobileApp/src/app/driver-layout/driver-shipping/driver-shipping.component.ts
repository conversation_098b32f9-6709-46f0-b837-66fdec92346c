import { Component, OnInit, OnDestroy } from '@angular/core';
import { NavController } from '@ionic/angular';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { RestResponse } from 'src/shared/auth.model';
import { ShipmentData, DriverShipmentItem } from 'src/modals/shipping-info';
import { CommonService } from 'src/services/common.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { SpecialRequestStates } from 'src/modals/cargoDetail';
import { ShipmentFilter, CityOption, CustomerOption, SHIPMENT_TYPES, ShipmentTypeOption } from 'src/modals/shipment-filter';
import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-driver-shipping',
  templateUrl: './driver-shipping.component.html',
  styleUrls: ['./driver-shipping.component.scss'],
  standalone: false
})
export class DriverShippingComponent implements OnInit, OnDestroy {

  filter!: ShipmentFilter;
  shippingList: DriverShipmentItem[] = [];
  allShipments: DriverShipmentItem[] = [];
  searchTerm: string | null = null;
  private subscription: Subscription = new Subscription();

  // Filter data
  showFilterModal: boolean = false;
  cities: CityOption[] = [];
  customers: CustomerOption[] = [];
  shipmentTypes: ShipmentTypeOption[] = SHIPMENT_TYPES;

  // Loading states
  loadingCities = false;
  loadingCustomers = false;

  // Custom dropdown states
  showPickupCityDropdown = false;
  pickupCitySearchTerm = '';
  filteredPickupCities: CityOption[] = [];

  showDeliveryCityDropdown = false;
  deliveryCitySearchTerm = '';
  filteredDeliveryCities: CityOption[] = [];

  showCustomerDropdown = false;
  customerSearchTerm = '';
  filteredCustomers: CustomerOption[] = [];

  showShipmentTypeDropdown = false;

  // Filter selections
  selectedPickupCity: CityOption | null = null;
  selectedDeliveryCity: CityOption | null = null;
  selectedCustomer: CustomerOption | null = null;
  selectedShipmentType: ShipmentTypeOption | null = null;
  specialRequestData: SpecialRequestStates = new SpecialRequestStates();
  expandedPickup: { [id: string]: boolean } = {};
  expandedDelivery: { [id: string]: boolean } = {};

  shippingNotificationId!: string;

  constructor(
    private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute,
    public readonly commonService: CommonService,
    private readonly localStorageService: LocalStorageService,
    private readonly cdr: ChangeDetectorRef,
  ) {

  }

  ngOnInit() {
    this.initializeFilter();
    this.loadFilterData();
  }

  ionViewWillEnter() {
    this.searchTerm = null;

    this.route.queryParams.pipe(take(1)).subscribe((params: any) => {
      const refresh = params['refresh'];
      const goTab = params['goTab'];
      this.shippingNotificationId = params['targetId'];

      if (refresh === 'true') {
        this.filter.tabType = goTab || 'PENDING';
        setTimeout(() => {
          this.loadShipments();
          this.cdr.detectChanges(); // ensure tabs clickable
        }, 100);
        // Remove this: this.navController.navigateForward('/portal/shipping', { replaceUrl: true });
      } else {
        this.filter.tabType = 'PENDING';
        this.loadShipments();
        this.cdr.detectChanges();
      }
    });
  }

  ionViewWillLeave() {
    this.clearFilters();
  }

  private initializeFilter() {
    this.filter = {
      tabType: 'PENDING',
      offset: 1
    };
  }

  openDetails(item: DriverShipmentItem) {
    this.navController.navigateForward('/portal/shipment/details', {
      queryParams: { shipmentId: item.id }
    });
  }

  onChangeStatusTab(status: 'PENDING' | 'IN_TRANSIT_TAB' | 'COMPLETED') {
    this.filter.tabType = status;
    this.searchTerm = null;
    this.expandedPickup = {};
    this.expandedDelivery = {};
    this.filterShipmentsByStatus();
    this.cdr.detectChanges();
  }

  add() {
    this.getRefId();
  }

  getRefId() {
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getRefId().subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data;
          this.removeLocalStorageDetails();
          this.navController.navigateForward("/portal/basic/info", {
            queryParams: {
              refIdData: data?.refID,
              reset: true
            }, animated: true
          });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  onEditClick(item: any) {
    this.removeLocalStorageDetails();
    this.getShipmentById(item.id);
  }

  getShipmentById(id: string) {
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getShipmentById(id).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          this.saveShipmentDetails(data);
          this.saveSpecialRequestDetails(data);
          this.saveDocuments(data);
          this.saveCustomerDetails(data);

          this.navController.navigateForward(`/portal/basic/info`, {
            animated: true
          });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  saveShipmentDetails(data: any) {
    this.localStorageService.setObject("SHIPPING_INFO", data);
    this.localStorageService.set("SHIPPING_MODE", "edit");
  }

  saveSpecialRequestDetails(data: any) {
    this.specialRequestData.isOversize = data.isOversize;
    this.specialRequestData.isRushRequest = data.isRushRequest;
    this.specialRequestData.isEnclosed = data.isEnclosed;
    this.specialRequestData.isFragile = data.isFragile;
    this.specialRequestData.isPerishable = data.isPerishable;
    this.specialRequestData.isDangerousGoods = data.isDangerousGoods;

    this.localStorageService.setObject("SPECIAL_REQUEST", this.specialRequestData);
  }

  saveDocuments(data: any) {
    this.localStorageService.setObject("SHIPPING_DOCUMENTS", data.documents);
  }

  saveCustomerDetails(data: any) {
    this.localStorageService.setObject("CUSTOMER_DETAILS", data.customerUserDetail);
  }

  removeLocalStorageDetails() {
    this.localStorageService.remove("SPECIAL_REQUEST");
    this.localStorageService.remove("SHIPPING_DOCUMENTS");
    this.localStorageService.remove("CARGO_MODE");
    this.localStorageService.remove("CARGO_DETAILS");
    this.localStorageService.remove("OTHER_SHIPMENT_DETAILS");
    this.localStorageService.remove("CUSTOMER_DETAILS");
    this.localStorageService.remove("SELECTED_CUSTOMER");
  }

  private loadShipments() {
    const apiPayload: any = {};

    // Create filtering object if any filters are applied
    const filtering: any = {};
    let hasFilters = false;

    if (this.filter.pickupCity) {
      filtering.pickupCity = this.filter.pickupCity;
      hasFilters = true;
    }
    if (this.filter.deliveryCity) {
      filtering.deliveryCity = this.filter.deliveryCity;
      hasFilters = true;
    }
    if (this.filter.customerId) {
      filtering.customerId = this.filter.customerId;
      hasFilters = true;
    }
    if (this.filter.shipmentType) {
      filtering.shipmentType = this.filter.shipmentType;
      hasFilters = true;
    }

    // Add filtering object to payload if filters exist
    if (hasFilters) {
      apiPayload.filtering = filtering;
    }

    this.loadingService.show();
    this.subscription.add(
      this.dataService.getShipments(apiPayload).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          if (Array.isArray(data)) {
            this.allShipments = this.transformApiDataToUIModel(data);
            this.filterShipmentsByStatus();
          } else {
            this.toastService.show('Failed to load shipments');
          }
        },
        error: (error) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'An error occurred');
          this.shippingList = [];
        }
      })
    );
  }

  private transformApiDataToUIModel(apiData: ShipmentData[]): DriverShipmentItem[] {
    return apiData.map(item => {
      const transformedItem = {
        id: item.id,
        refId: item.refID,
        paymentType: this.formatPaymentType(item.paymentType),
        from: item.pickupAddressDetail?.address || 'N/A',
        to: item.deliveryAddressDetail?.address || 'N/A',
        etd: this.commonService.formatDisplayDate(item.etd) || 'N/A',
        status: this.formatStatus(item.status),
        pickupContactPersonName: item.pickupContactPersonName || 'N/A',
        pickupContactPersonPhone: item.pickupContactPersonPhone || '',
        deliveryContactPersonName: item.deliveryContactPersonName || 'N/A',
        deliveryContactPersonPhone: item.deliveryContactPersonPhone || '',
        originalStatus: item.status, // Store original API status
        customerName: item.customerUserDetail?.fullName || 'N/A',
        grandTotal: item.grandTotal || 0,
        shipmentType: item.shipmentType,
        step: item.step,
        isCargoAdded: item.isCargoAdded || false,
        barcodeDetail: item.barcodeDetail ? {
          id: item.barcodeDetail.id,
          barcodeNo: item.barcodeDetail.barcodeNo
        } : undefined
      };

      return transformedItem;
    });
  }

  private filterShipmentsByStatus() {
    if (!this.allShipments || this.allShipments.length === 0) {
      this.shippingList = [];
      return;
    }

    if (this.filter.tabType === 'PENDING') {
      this.shippingList = this.allShipments.filter(shipment => {
        const status = shipment.originalStatus?.toLowerCase() || shipment.status.toLowerCase();
        const isIncluded = status === 'new' || status === 'assigned' ||
          status === 'pending';

        if (status === 'in_transit') {
        }
        return isIncluded;
      });
    } else if (this.filter.tabType === 'IN_TRANSIT_TAB') {
      this.shippingList = this.allShipments.filter(shipment => {
        const status = shipment.originalStatus?.toLowerCase() || shipment.status.toLowerCase();
        return status === 'in_transit';
      });
    } else if (this.filter.tabType === 'COMPLETED') {
      this.shippingList = this.allShipments.filter(shipment => {
        const status = shipment.originalStatus?.toLowerCase() || shipment.status.toLowerCase();
        return status === 'completed' ||
          status === 'delivered';
      });
    } else {
      this.shippingList = [...this.allShipments];
    }
  }

  togglePickupAddress(id: string) {
    this.expandedPickup[id] = !this.expandedPickup[id];
    //  this.expandedDelivery[id] = false;
  }

  toggleDeliveryAddress(id: any) {
    this.expandedDelivery[id] = !this.expandedDelivery[id];
    //  this.expandedPickup[id] = false;
  }

  private formatPaymentType(paymentType: string): string {
    switch (paymentType?.toUpperCase()) {
      case 'PREPAID':
        return 'Prepaid';
      case 'COD':
        return 'COD';
      default:
        return paymentType || 'N/A';
    }
  }

  private formatStatus(status: string): string {
    switch (status?.toUpperCase()) {
      case 'NEW':
        return 'New';
      case 'ASSIGNED':
        return 'Assigned';
      case 'IN_TRANSIT':
        return 'In Transit';
      case 'COMPLETED':
        return 'Completed';
      case 'PENDING':
        return 'Pending';
      default:
        return status || 'N/A';
    }
  }

  // Dynamic button functionality
  getActionText(item: DriverShipmentItem): string {
    const originalStatus = this.getOriginalStatus(item);
    switch (originalStatus?.toUpperCase()) {
      case 'NEW':
        return item.isCargoAdded === false ? 'View Cargo' : 'Start';
      case 'ASSIGNED':
        return item.isCargoAdded === false ? 'View Cargo' : 'Start';
      case 'IN_TRANSIT':
        return 'Add POD';
      default:
        return 'DOCS';
    }
  }

  getActionIcon(item: DriverShipmentItem): string {
    const originalStatus = this.getOriginalStatus(item);
    switch (originalStatus?.toUpperCase()) {
      case 'NEW':
        return item.isCargoAdded === false ? 'cube' : 'play-circle';
      case 'ASSIGNED':
        return item.isCargoAdded === false ? 'cube' : 'play-circle';
      case 'IN_TRANSIT':
        return 'document-attach';
      default:
        return 'eye';
    }
  }

  private getOriginalStatus(item: DriverShipmentItem): string {
    return item.originalStatus || item.status;
  }

  getTimelineClass(item: DriverShipmentItem): string {
    const originalStatus = this.getOriginalStatus(item);
    switch (originalStatus?.toUpperCase()) {
      case 'NEW':
      case 'PENDING':
        return 'status-new';
      case 'ASSIGNED':
        return 'status-assigned';
      case 'IN_TRANSIT':
        return 'status-in-transit';
      case 'COMPLETED':
      case 'DELIVERED':
        return 'status-completed';
      default:
        return 'status-new';
    }
  }

  getIconForStatus(item: DriverShipmentItem): string {
    const originalStatus = this.getOriginalStatus(item);
    switch (originalStatus?.toUpperCase()) {
      case 'NEW':
      case 'PENDING':
        return '/assets/images/svg/oval-location-red.svg';
      case 'ASSIGNED':
        return '/assets/images/svg/oval-location-blue.svg';
      case 'IN_TRANSIT':
        return '/assets/images/svg/oval-location-yellow.svg';
      case 'COMPLETED':
      case 'DELIVERED':
        return '/assets/images/svg/oval-location-purple.svg';
      default:
        return '/assets/images/svg/oval-location.svg';
    }
  }

  getTimelineHeight(item: DriverShipmentItem): string {
    const originalStatus = this.getOriginalStatus(item);
    switch (originalStatus?.toUpperCase()) {
      case 'NEW':
      case 'PENDING':
        return '40px';
      case 'ASSIGNED':
        return '85px';
      case 'IN_TRANSIT':
        return '135px';
      case 'COMPLETED':
      case 'DELIVERED':
        return '120px';
      default:
        return '60px';
    }
  }

  isStatusReached(item: DriverShipmentItem, checkStatus: string): boolean {
    const currentStatus = this.getOriginalStatus(item)?.toUpperCase();
    const statusOrder = ['NEW', 'ASSIGNED', 'IN_TRANSIT', 'COMPLETED'];

    // Handle PENDING as NEW for comparison
    const normalizedCurrentStatus = currentStatus === 'PENDING' ? 'NEW' : currentStatus;
    const normalizedCheckStatus = checkStatus === 'NEW' ? 'NEW' : checkStatus;

    const currentIndex = statusOrder.indexOf(normalizedCurrentStatus);
    const checkIndex = statusOrder.indexOf(normalizedCheckStatus);

    return currentIndex >= checkIndex;
  }

  isCurrentStatus(item: DriverShipmentItem, checkStatus: string): boolean {
    const currentStatus = this.getOriginalStatus(item)?.toUpperCase();

    // Handle PENDING as NEW for comparison
    const normalizedCurrentStatus = currentStatus === 'PENDING' ? 'NEW' : currentStatus;
    const normalizedCheckStatus = checkStatus === 'NEW' ? 'NEW' : checkStatus;

    return normalizedCurrentStatus === normalizedCheckStatus;
  }

  getStatusColor(item: DriverShipmentItem): string {
    const status = this.getOriginalStatus(item)?.toUpperCase();

    switch (status) {
      case 'NEW':
      case 'PENDING':
        return '#ec0b0b'; // red
      case 'ASSIGNED':
        return '#007bff'; // blue
      case 'IN_TRANSIT':
        return '#ffc107'; // yellow
      case 'DELIVERED':
        return '#28a745'; // green
      case 'COMPLETED':
        return '#6f42c1'; // purple
      default:
        return '#6c757d'; // default grey
    }
  }

  getTimelineMargin(item: DriverShipmentItem): string {
    const originalStatus = this.getOriginalStatus(item);
    switch (originalStatus?.toUpperCase()) {
      case 'NEW':
      case 'PENDING':
        return '-10px';
      case 'ASSIGNED':
        return '-10px';
      case 'IN_TRANSIT':
        return '120px';
      default:
        return '10px';
    }
  }

  getStepIcon(stepStatus: string, item: DriverShipmentItem): string {
    // Only show colored SVG if this is the current/active status
    if (this.isCurrentStatus(item, stepStatus)) {
      switch (stepStatus.toUpperCase()) {
        case 'NEW':
          return '/assets/images/svg/oval-location-red.svg';
        case 'ASSIGNED':
          return '/assets/images/svg/oval-location-blue.svg';
        case 'IN_TRANSIT':
          return '/assets/images/svg/oval-location-yellow.svg';
        case 'COMPLETED':
          return '/assets/images/svg/oval-location-purple.svg';
        default:
          return '/assets/images/svg/oval-location.svg'; // grey
      }
    } else {
      // Return grey SVG for all non-current statuses (both completed and future)
      return '/assets/images/svg/oval-location.svg';
    }
  }

  handleShipmentAction(item: DriverShipmentItem): void {
    const originalStatus = this.getOriginalStatus(item);
    switch (originalStatus?.toUpperCase()) {
      case 'ASSIGNED':
        if (item.isCargoAdded === false) {
          this.addCargo(item);
        } else {
          this.startShipment(item);
        }
        break;
      case 'NEW':
        if (item.isCargoAdded === false) {
          this.addCargo(item);
        } else {
          this.startShipment(item);
        }
        break;
      case 'IN_TRANSIT':
        this.addPOD(item);
        break;
      default:
        this.viewShipment(item);
        break;
    }
  }

  private addCargo(item: DriverShipmentItem): void {
    this.goToViewCargoItem(item.id);
  }

  private startShipment(item: DriverShipmentItem): void {
    this.navController.navigateForward('/portal/start-shipment', {
      queryParams: { shipmentId: item.id, refId: item.refId }
    });
  }

  private addPOD(item: DriverShipmentItem): void {
    this.navController.navigateForward('/portal/pod-management', {
      queryParams: {
        shipmentId: item.id,
        refId: item.refId,
        barcodeDetail: JSON.stringify(item.barcodeDetail || {})
      }
    });
  }

  private viewShipment(item: DriverShipmentItem): void {
    this.navController.navigateForward('/portal/view/documents', {
      queryParams: { shipmentId: item.id }
    });
  }

  goToViewCargoItem(id: string) {
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getShipmentById(id).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();

          const data = response.data;
          this.saveShipmentDetails(data);
          this.saveSpecialRequestDetails(data);
          this.saveDocuments(data);

          this.navController.navigateForward('/portal/cargo/listing', {
            queryParams: { shipmentData: data.id }
          });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  refreshShipments() {
    this.loadShipments();
  }

  // Search functionality
  onSearchChange(event: any) {
    this.searchTerm = event.detail.value || '';
    this.applySearch();
  }

  private applySearch() {
    if (!this.searchTerm) {
      this.filterShipmentsByStatus();
      return;
    }

    // First filter by current tab status, then apply search
    let statusFilteredShipments: any[] = [];

    if (this.filter.tabType === 'PENDING') {
      statusFilteredShipments = this.allShipments.filter(shipment => {
        const status = shipment.originalStatus?.toLowerCase() || shipment.status.toLowerCase();
        return status === 'new' || status === 'assigned' ||
          status === 'pending';
      });
    } else if (this.filter.tabType === 'IN_TRANSIT_TAB') {
      statusFilteredShipments = this.allShipments.filter(shipment => {
        const status = shipment.originalStatus?.toLowerCase() || shipment.status.toLowerCase();
        return status === 'in_transit';
      });
    } else if (this.filter.tabType === 'COMPLETED') {
      statusFilteredShipments = this.allShipments.filter(shipment => {
        const status = shipment.originalStatus?.toLowerCase() || shipment.status.toLowerCase();
        return status === 'completed' || status === 'delivered';
      });
    } else {
      statusFilteredShipments = [...this.allShipments];
    }

    // Then apply search filter on the status-filtered results
    const searchLower = this.searchTerm.toLowerCase();
    this.shippingList = statusFilteredShipments.filter(shipment =>
      shipment.refId.toLowerCase().includes(searchLower) ||
      shipment.status.toLowerCase().includes(searchLower)
    );
  }

  // Filter Methods
  private loadFilterData() {
    this.loadCities();
    this.loadCustomers();
  }

  // Check if all data is loaded
  get isFilterDataLoaded(): boolean {
    return !this.loadingCities && !this.loadingCustomers && this.cities.length > 0 && this.customers.length > 0;
  }

  // Test method to check API endpoints
  testApiEndpoints() {
    // Test cities API
    this.dataService.getCitySelection().subscribe({
      next: (response) => console.log('Cities test response:', response),
      error: (error) => console.error('Cities test error:', error)
    });

    // Test customers API
    this.dataService.getAllCustomers().subscribe({
      next: (response) => console.log('Customers test response:', response),
      error: (error) => console.error('Customers test error:', error)
    });
  }

  private loadCities() {
    this.loadingCities = true;
    this.subscription.add(
      this.dataService.getCitySelection().subscribe({
        next: (response: RestResponse) => {

          this.cities = [];

          if (response && response.data) {
            let cityData = response.data;

            // Handle different response structures
            if (Array.isArray(cityData)) {
              this.cities = cityData.map((city: any) => ({
                id: city.id || city.cityId || city.value || city.Id || city.ID || String(Math.random()),
                name: city.name || city.cityName || city.label || city.text || city.Name || city.city || city.City,
                province: city.province || city.provinceName || city.state || city.Province || city.State
              }));
            } else if (cityData.cities && Array.isArray(cityData.cities)) {
              this.cities = cityData.cities.map((city: any) => ({
                id: city.id || city.cityId || city.value || city.Id || city.ID || String(Math.random()),
                name: city.name || city.cityName || city.label || city.text || city.Name || city.city || city.City,
                province: city.province || city.provinceName || city.state || city.Province || city.State
              }));
            } else {
              // Try to find any array in the response
              Object.keys(cityData).forEach(key => {
                if (Array.isArray(cityData[key])) {
                  this.cities = cityData[key].map((city: any) => ({
                    id: city.id || city.cityId || city.value || city.Id || city.ID || String(Math.random()),
                    name: city.name || city.cityName || city.label || city.text || city.Name || city.city || city.City,
                    province: city.province || city.provinceName || city.state || city.Province || city.State
                  }));
                }
              });
            }

            // Filter out invalid entries
            this.cities = this.cities.filter(city => city.name && city.name.trim() !== '');

            // Initialize filtered cities for all dropdowns
            this.filteredPickupCities = [...this.cities];
            this.filteredDeliveryCities = [...this.cities];
          }

          this.loadingCities = false;
          this.cdr.detectChanges();

          setTimeout(() => {
            this.cdr.detectChanges();
          }, 100);
        },
        error: (error) => {
          console.error('Error loading cities:', error);
          this.toastService.show('Failed to load cities');
          this.loadingCities = false;
          this.cdr.detectChanges();
        }
      })
    );
  }

  private loadCustomers() {
    this.loadingCustomers = true;
    this.subscription.add(
      this.dataService.getAllCustomers().subscribe({
        next: (response: RestResponse) => {

          this.customers = [];

          if (response && response.data) {
            let customerData = response.data;

            // Handle different response structures
            if (Array.isArray(customerData)) {
              this.customers = customerData.map((customer: any) => ({
                id: customer.id || customer.customerId || customer.value || customer.Id || customer.ID || String(Math.random()),
                name: customer.name || customer.customerName || customer.fullName || customer.label || customer.text || customer.Name || customer.customer,
                companyName: customer.companyName || customer.company || customer.businessName || customer.CompanyName || customer.Company
              }));
            } else if (customerData.customers && Array.isArray(customerData.customers)) {
              this.customers = customerData.customers.map((customer: any) => ({
                id: customer.id || customer.customerId || customer.value || customer.Id || customer.ID || String(Math.random()),
                name: customer.name || customer.customerName || customer.fullName || customer.label || customer.text || customer.Name || customer.customer,
                companyName: customer.companyName || customer.company || customer.businessName || customer.CompanyName || customer.Company
              }));
            } else {
              // Try to find any array in the response
              Object.keys(customerData).forEach(key => {
                if (Array.isArray(customerData[key])) {
                  this.customers = customerData[key].map((customer: any) => ({
                    id: customer.id || customer.customerId || customer.value || customer.Id || customer.ID || String(Math.random()),
                    name: customer.name || customer.customerName || customer.fullName || customer.label || customer.text || customer.Name || customer.customer,
                    companyName: customer.companyName || customer.company || customer.businessName || customer.CompanyName || customer.Company
                  }));
                }
              });
            }

            // Filter out invalid entries
            this.customers = this.customers.filter(customer => customer.name && customer.name.trim() !== '');

            // Initialize filtered customers
            this.filteredCustomers = [...this.customers];
          }

          this.loadingCustomers = false;
          this.cdr.detectChanges();

          setTimeout(() => {
            this.cdr.detectChanges();
          }, 100);
        },
        error: (error) => {
          console.error('Error loading customers:', error);
          this.toastService.show('Failed to load customers');
          this.loadingCustomers = false;
          this.cdr.detectChanges();
        }
      })
    );
  }

  openFilterModal() {
    this.showFilterModal = true;
    this.loadFilterData();
  }

  closeFilterModal() {
    this.showFilterModal = false;
  }

  applyFilters() {
    this.filter.pickupCity = this.selectedPickupCity?.name || undefined;
    this.filter.deliveryCity = this.selectedDeliveryCity?.name || undefined;
    this.filter.customerId = this.selectedCustomer?.id || undefined;
    this.filter.shipmentType = this.selectedShipmentType?.value || undefined;

    // Reload shipments with filters
    this.loadShipments();
    this.closeFilterModal();
  }

  // Check if any filters are currently active
  hasActiveFilters(): boolean {
    return !!(this.filter.pickupCity ||
      this.filter.deliveryCity ||
      this.filter.customerId ||
      this.filter.shipmentType);
  }

  clearFilters() {
    this.selectedPickupCity = null;
    this.selectedDeliveryCity = null;
    this.selectedCustomer = null;
    this.selectedShipmentType = null;

    // Clear filter object
    this.filter.pickupCity = undefined;
    this.filter.deliveryCity = undefined;
    this.filter.customerId = undefined;
    this.filter.shipmentType = undefined;

    this.closeFilterModal();
  }

  // Clear filters and reload - for the clear button in tabs
  clearFiltersAndReload() {
    this.selectedPickupCity = null;
    this.selectedDeliveryCity = null;
    this.selectedCustomer = null;
    this.selectedShipmentType = null;

    // Clear filter object
    this.filter.pickupCity = undefined;
    this.filter.deliveryCity = undefined;
    this.filter.customerId = undefined;
    this.filter.shipmentType = undefined;

    // Reload shipments without filters
    this.closeFilterModal();
    this.loadShipments();
  }

  // Handle header right action - filter or clear based on current state
  onHeaderRightAction() {
    // this commented line is for if close icon shown after apply filter then clear filters. 
    // if (this.hasActiveFilters()) {
    //   this.clearFiltersAndReload();
    // } else {
    //   this.openFilterModal();
    // }
    this.openFilterModal();
  }

  // Compare function for ion-select
  compareOptions(o1: any, o2: any): boolean {
    return o1 && o2 ? o1.id === o2.id : o1 === o2;
  }

  // TrackBy functions for better performance
  trackByCity(_index: number, city: CityOption): string {
    return city.id;
  }

  trackByCustomer(_index: number, customer: CustomerOption): string {
    return customer.id;
  }

  trackByShipmentType(_index: number, type: ShipmentTypeOption): string {
    return type.value;
  }

  // Custom dropdown methods
  private closeAllDropdowns() {
    this.showPickupCityDropdown = false;
    this.showDeliveryCityDropdown = false;
    this.showCustomerDropdown = false;
    this.showShipmentTypeDropdown = false;
  }

  togglePickupCityDropdown() {
    const wasOpen = this.showPickupCityDropdown;
    this.closeAllDropdowns();

    if (!wasOpen) {
      this.showPickupCityDropdown = true;
      this.filteredPickupCities = [...this.cities];
      this.pickupCitySearchTerm = '';
    }
  }

  filterPickupCities(event: any) {
    const searchTerm = event.target.value.toLowerCase();
    this.pickupCitySearchTerm = searchTerm;

    if (searchTerm.trim() === '') {
      this.filteredPickupCities = [...this.cities];
    } else {
      this.filteredPickupCities = this.cities.filter(city =>
        city.name.toLowerCase().includes(searchTerm) ||
        (city.province && city.province.toLowerCase().includes(searchTerm))
      );
    }
  }

  selectPickupCity(city: CityOption | null) {
    this.selectedPickupCity = city;
    this.showPickupCityDropdown = false;
    this.pickupCitySearchTerm = '';
  }

  // Delivery City dropdown methods
  toggleDeliveryCityDropdown() {
    const wasOpen = this.showDeliveryCityDropdown;
    this.closeAllDropdowns();

    if (!wasOpen) {
      this.showDeliveryCityDropdown = true;
      this.filteredDeliveryCities = [...this.cities];
      this.deliveryCitySearchTerm = '';
    }
  }

  filterDeliveryCities(event: any) {
    const searchTerm = event.target.value.toLowerCase();
    this.deliveryCitySearchTerm = searchTerm;

    if (searchTerm.trim() === '') {
      this.filteredDeliveryCities = [...this.cities];
    } else {
      this.filteredDeliveryCities = this.cities.filter(city =>
        city.name.toLowerCase().includes(searchTerm) ||
        (city.province && city.province.toLowerCase().includes(searchTerm))
      );
    }
  }

  selectDeliveryCity(city: CityOption | null) {
    this.selectedDeliveryCity = city;
    this.showDeliveryCityDropdown = false;
    this.deliveryCitySearchTerm = '';
  }

  // Customer dropdown methods
  toggleCustomerDropdown() {
    const wasOpen = this.showCustomerDropdown;
    this.closeAllDropdowns();

    if (!wasOpen) {
      this.showCustomerDropdown = true;
      this.filteredCustomers = [...this.customers];
      this.customerSearchTerm = '';
    }
  }

  filterCustomers(event: any) {
    const searchTerm = event.target.value.toLowerCase();
    this.customerSearchTerm = searchTerm;

    if (searchTerm.trim() === '') {
      this.filteredCustomers = [...this.customers];
    } else {
      this.filteredCustomers = this.customers.filter(customer =>
        customer.name.toLowerCase().includes(searchTerm) ||
        (customer.companyName && customer.companyName.toLowerCase().includes(searchTerm))
      );
    }
  }

  selectCustomer(customer: CustomerOption | null) {
    this.selectedCustomer = customer;
    this.showCustomerDropdown = false;
    this.customerSearchTerm = '';
  }

  // Shipment Type dropdown methods
  toggleShipmentTypeDropdown() {
    const wasOpen = this.showShipmentTypeDropdown;
    this.closeAllDropdowns();

    if (!wasOpen) {
      this.showShipmentTypeDropdown = true;
    }
  }

  selectShipmentType(type: ShipmentTypeOption | null) {
    this.selectedShipmentType = type;
    this.showShipmentTypeDropdown = false;
  }

  // Close dropdowns when clicking outside
  onModalContentClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.custom-select') && !target.closest('.custom-dropdown')) {
      this.closeAllDropdowns();
    }
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

}
