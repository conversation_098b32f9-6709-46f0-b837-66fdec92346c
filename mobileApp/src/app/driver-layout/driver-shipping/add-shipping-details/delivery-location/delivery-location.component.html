<ion-content class="delivery-location-page">
  <app-customer-header [innerPage]="true" [headingText]="'Pickup & Delivery'" [rightAction]="false"
    [backUrl]="backUrl" [hideBellIcon]="true"></app-customer-header>

  <div class="delivery-location-body-section">

    <div class="shipment-detail-container margin-top-20">
      <div class="shipment-detail">
        <span class="shipment-label">Ref Id</span>
        <span class="shipment-value">{{ shippingData.refID }}</span>
      </div>
      <div class="shipment-detail">
        <span class="shipment-label">Customer</span>
        <span class="shipment-value">
          {{
          authService.isDriver()
          ? (selectedCustomer?.fullName || customerDetail?.fullName)
          : (user?.firstName + " " + user?.lastName)
          }}
        </span>
      </div>
      <div class="shipment-detail" *ngIf="authService.isDriver() && shippingData.contactPersonPhone">
        <span class="shipment-label">Customer Phone</span>
        <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(shippingData.contactPersonPhone)
          }}</span>
      </div>
      <div class="shipment-detail" *ngIf="authService.isCustomer() && user?.phoneNumber">
        <span class="shipment-label">Customer Phone</span>
        <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(user?.phoneNumber)
          }}</span>
      </div>
    </div>

    <div class="margin-top-20">
      <span class="info-text">Shipment Delivery Location</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #deliveryForm="ngForm" novalidate>

        <div class="margin-top-20 margin-bottom-10">
          <ng-container *ngIf="hasDeliveryData">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'is-invalid': companyName.invalid && onClickValidation }">
              <ion-input label="Delivery Company Name" labelPlacement="floating" name="companyName" required
                [(ngModel)]="shippingData.deliveryCompanyName" #companyName="ngModel" maxlength="100"
                pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="companyName" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasDeliveryData">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'is-invalid': contactName.invalid && onClickValidation }">
              <ion-input label="Delivery Contact Name" labelPlacement="floating" name="contactName" required
                [(ngModel)]="shippingData.deliveryContactPersonName" #contactName="ngModel" maxlength="100"
                pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md">
              </ion-input>
            </ion-item>
            <app-validation-message [field]="contactName" [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ion-item class="site-form-control phone-input-container" lines="none"
            [ngClass]="{ 'is-invalid': (userPhone.invalid || phoneInvalid) && onClickValidation }">
            <ion-icon src="/assets/images/svg/canada-flag-icon.svg" slot="start" class="start-icon"></ion-icon>
            <div class="phone-input-wrapper">
              <span class="phone-prefix">+1</span>
              <ion-input inputmode="tel" required name="userPhone" #userPhone="ngModel"
                [(ngModel)]="displayDeliveryPhone" (ionInput)="formatPhoneNumber($event, userPhone)"
                placeholder="Phone Number">
              </ion-input>
            </div>
          </ion-item>
          <app-validation-message [field]="userPhone" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid contact number.'">
          </app-validation-message>
          <!-- Show custom message if phone number is invalid length -->
          <div class="error-message" *ngIf="!userPhone.invalid && phoneInvalid && onClickValidation">
            Please provide a valid contact number.
          </div>
        </div>

        <div class="margin-top-10" (click)="openLocationSelectionPopup()">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': address.invalid && onClickValidation}">
            <ion-input name="address" #address="ngModel" [(ngModel)]="shippingData.deliveryAddressDetail.address"
              required="required" mode="md" label="Address" labelPlacement="floating">
            </ion-input>
            <ion-button slot="end" fill="clear" class="fetch-address-btn">
              <ion-icon name="location-outline"></ion-icon>
            </ion-button>
          </ion-item>
          <app-validation-message [field]="address" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ng-container *ngIf="hasDeliveryData">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': (city.invalid || isCityOnlySpaces) && onClickValidation}">
                <ion-input name="city" #city="ngModel" [(ngModel)]="shippingData.deliveryAddressDetail.city"
                  required="required" maxlength="100" mode="md" label="City" labelPlacement="floating">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="city" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
              <div class="error-message margin-top-5" *ngIf="!city.invalid && isCityOnlySpaces && onClickValidation">
                City cannot be empty or just spaces.
              </div>
            </ng-container>
          </div>

          <div class="field-container large-field">
            <ng-container *ngIf="hasDeliveryData">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': (province.invalid || isProvinceOnlySpaces) && onClickValidation}">
                <ion-input name="province" #province="ngModel" [(ngModel)]="shippingData.deliveryAddressDetail.state"
                  required="required" maxlength="100" mode="md" label="Province" labelPlacement="floating">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="province" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
              <div class="error-message margin-top-5"
                *ngIf="!province.invalid && isProvinceOnlySpaces && onClickValidation">
                Province cannot be empty or just spaces.
              </div>
            </ng-container>
          </div>
        </div>

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ng-container *ngIf="hasDeliveryData">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': (postalCode.invalid || isPostalOnlySpaces) && onClickValidation}">
                <ion-input name="postalCode" #postalCode="ngModel" [(ngModel)]="shippingData.deliveryAddressDetail.pin"
                  required="required" mode="md" label="Postal Code" labelPlacement="floating" maxlength="10">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="postalCode" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Please enter a valid postal code.'">
              </app-validation-message>
              <div class="error-message margin-top-5"
                *ngIf="!postalCode.invalid && isPostalOnlySpaces && onClickValidation">
                Postal Code cannot be empty or just spaces.
              </div>
            </ng-container>
          </div>

          <div class="field-container large-field">
            <ng-container *ngIf="hasDeliveryData">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': (country.invalid || isCountryOnlySpaces) && onClickValidation}">
                <ion-input name="country" #country="ngModel" [(ngModel)]="shippingData.deliveryAddressDetail.country"
                  required="required" maxlength="100" mode="md" label="Country" labelPlacement="floating">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="country" [onClickValidation]="onClickValidation"
                [customPatternMessage]="'Only alphabetic characters are allowed.'">
              </app-validation-message>
              <div class="error-message margin-top-5"
                *ngIf="!country.invalid && isCountryOnlySpaces && onClickValidation">
                Country cannot be empty or just spaces.
              </div>
            </ng-container>
          </div>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn" expand="full" shape="round" type="submit"
            (click)="cancel()">
            <span>Back</span>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn" expand="full" shape="round" type="submit"
            (click)="submitProfile(deliveryForm.form)">
            <span>Submit</span>
          </ion-button>
        </div>

      </form>
    </div>

  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #noAddressFoundPopup [isOpen]="isNoAddressFoundPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container">
      <div class="site-custom-popup-header no-header-text">
        <i-feather name="X" (click)="closeNoAddressFoundPopup()"></i-feather>
      </div>
      <div class="site-custom-popup-body ion-padding no-padding-top">
        <div class="popup-large-heading">Oops! Unable to fetch the address. Tap to retry.</div>
        <div class="popup-normal-heading margin-top-10 secondary-text">Note:- Please make sure that your location
          permission is
          granted.</div>
        <ion-button class="site-full-rounded-button primary-button margin-top-25" expand="full" shape="round"
          (click)="retry()">
          Retry
        </ion-button>
      </div>
    </div>
  </ng-template>
</ion-modal>

<ion-modal class="location-popup" #locationSelectionPopup [isOpen]="isLocationSelectionPopupOpen"
  [backdropDismiss]="false">
  <ng-template>
    <ion-header>
      <ion-toolbar>
        <ion-title>Select location</ion-title>
        <ion-buttons slot="start">
          <i-feather name="X" (click)="closeLocationSelectionPopup()"></i-feather>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>
    <ion-content>
      <div class="ion-padding no-padding-bottom">
        <ion-item class="site-form-control" lines="none">
          <i-feather class="map-pin-icon start-icon" name="map-pin" slot="start"></i-feather>
          <ion-input label="Search Location" labelPlacement="floating" name="searchLocation"
            [(ngModel)]="searchLocation" (ionInput)="fetchPlaces()" [debounce]="500"></ion-input>
        </ion-item>
      </div>

      <div class="google-places-container">
        <ion-list *ngIf="availablePlaces.length > 0" class="google-places">
          <ion-item class="google-place" *ngFor="let place of availablePlaces" lines="full"
            (click)="onSelectLocation(place)">
            <i-feather class="map-pin-icon" name="map-pin" slot="start"></i-feather>
            <ion-label class="ion-text-wrap">
              <div class="city-name">{{ place.description }}</div>
            </ion-label>
          </ion-item>
        </ion-list>

        <div *ngIf="searchLocation && availablePlaces.length === 0"
          class="no-results-message ion-text-center ion-padding">
          <ion-icon name="alert-circle-outline" style="font-size: 48px; color: #999;"></ion-icon>
          <p>No locations found matching "<strong>{{ searchLocation }}</strong>". Please try a different search.</p>
        </div>

      </div>
    </ion-content>
  </ng-template>
</ion-modal>