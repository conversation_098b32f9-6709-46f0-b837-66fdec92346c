import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { SpecialRequestStates } from 'src/modals/cargoDetail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-special-request',
  templateUrl: './special-request.component.html',
  styleUrls: ['./special-request.component.scss'],
  standalone: false
})
export class SpecialRequestComponent implements OnInit {

  onClickValidation!: boolean;
  specialRequestData: SpecialRequestStates = new SpecialRequestStates();
  shipmentData: any;
  backUrl!: string;
  shippingEditMode: any;
  activeSubscriptions: Subscription = new Subscription();
  shippingDetails: any;
  user: any;

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    public commonService: CommonService,
    public readonly authService: AuthService
  ) {

  }

  ngOnInit(): void {
    const shippingData = this.localStorageService.getObject("SPECIAL_REQUEST");
    this.specialRequestData = shippingData ? SpecialRequestStates.fromResponse(shippingData) : new SpecialRequestStates();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;
    this.user = this.authService.getUser();

    this.shippingDetails = this.localStorageService.getObject("OTHER_SHIPMENT_DETAILS");

    const shippingData = this.localStorageService.getObject("SPECIAL_REQUEST");
    this.specialRequestData = shippingData ? SpecialRequestStates.fromResponse(shippingData) : new SpecialRequestStates();

    this.route.queryParams.subscribe(params => {
      this.shipmentData = params['shipmentData'] || null;
    });

    this.shippingEditMode = this.localStorageService.get("SHIPPING_MODE");
    this.specialRequestData.id = this.shipmentData;

    if (this.authService.isDriver()) {
      this.backUrl = `/portal/cargo/listing?shipmentData=${this.shipmentData}`;
    }
    else {
      this.backUrl = `/client/portal/cargo/listing?shipmentData=${this.shipmentData}`;
    }
  }

  onViewChange(section: 'isOversize' | 'isRushRequest' | 'isEnclosed' | 'isFragile' | 'isPerishable' | 'isDangerousGoods', newValue: 'YES' | 'NO'): void {
    this.specialRequestData[section] = newValue === 'YES';
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }
    this.localStorageService.setObject("SPECIAL_REQUEST", this.specialRequestData);
    if (this.authService.isDriver()) {
      this.navController.navigateForward('/portal/add/documents', {
        queryParams: {
          shipmentData: this.shipmentData
        }, animated: true
      });
      return;
    }
    if (this.authService.isCustomer() && this.shippingEditMode === "edit") {
      this.navController.navigateForward('/client/portal/calculation', {
        queryParams: {
          shipmentData: this.shipmentData
        }, animated: true
      });
      return;
    }
    if (this.authService.isCustomer()) {
      this.navController.navigateForward('/client/portal/add/documents', {
        queryParams: {
          shipmentData: this.shipmentData
        }, animated: true
      });
      return;
    }
  }

  cancel() {
    if (this.authService.isDriver()) {
      this.navController.navigateBack('/portal/cargo/listing', {
        queryParams: {
          shipmentData: this.shipmentData
        }, animated: true
      });
      return;
    }
    this.navController.navigateBack('/client/portal/cargo/listing', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

  goToShippingList() {
    if (this.authService.isDriver()) {
      this.navController.navigateRoot('/portal/shipping', { animated: true });
      return;
    }
    this.navController.navigateRoot('/client/portal/shipping', { animated: true });
  }

}
