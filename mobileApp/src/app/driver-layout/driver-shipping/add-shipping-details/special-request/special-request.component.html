<ion-content class="special-request-page">
  <app-customer-header [innerPage]="true" [headingText]="'Special Request'" [rightAction]="false" [backUrl]="backUrl"
    [homeAction]="true" [hideBellIcon]="true" (homeActionCallback)="goToShippingList()"></app-customer-header>

  <div class="special-request-body-section">

    <div class="shipment-detail-container margin-top-20">
      <div class="shipment-detail">
        <span class="shipment-label">Ref Id</span>
        <span class="shipment-value">{{ shippingDetails?.refID }}</span>
      </div>
      <div class="shipment-detail">
        <span class="shipment-label">Customer</span>
        <span class="shipment-value">
          {{
          authService.isDriver()
          ? (shippingDetails?.customerUserDetail?.fullName || 'N/A')
          : (user?.firstName + " " + user?.lastName || 'N/A')
          }}
        </span>
      </div>
      <div class="shipment-detail" *ngIf="authService.isDriver() && shippingDetails?.contactPersonPhone">
        <span class="shipment-label">Customer Phone</span>
        <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(shippingDetails?.contactPersonPhone)
          }}</span>
      </div>
      <div class="shipment-detail" *ngIf="authService.isCustomer() && user?.phoneNumber">
        <span class="shipment-label">Customer Phone</span>
        <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(user?.phoneNumber)
          }}</span>
      </div>
    </div>

    <div class="form-container">
      <form class="custom-form" #requestForm="ngForm" novalidate>

        <!-- Oversize -->
        <div class="margin-top-20">
          <div class="request-container">
            <span class="request-text">Oversize</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('isOversize', 'YES')"
                  [ngClass]="{'selected': specialRequestData.isOversize}">
                  <ion-checkbox [checked]="specialRequestData.isOversize" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('isOversize', 'NO')"
                  [ngClass]="{'selected': specialRequestData.isOversize === false}">
                  <ion-checkbox [checked]="specialRequestData.isOversize === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Rush Request -->
          <div class="request-container margin-top-12">
            <span class="request-text">Rush Request Immediately</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('isRushRequest', 'YES')"
                  [ngClass]="{'selected': specialRequestData.isRushRequest}">
                  <ion-checkbox [checked]="specialRequestData.isRushRequest" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('isRushRequest', 'NO')"
                  [ngClass]="{'selected': specialRequestData.isRushRequest === false}">
                  <ion-checkbox [checked]="specialRequestData.isRushRequest === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Enclosed -->
          <div class="request-container margin-top-12">
            <span class="request-text">Enclosed</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('isEnclosed', 'YES')"
                  [ngClass]="{'selected': specialRequestData.isEnclosed}">
                  <ion-checkbox [checked]="specialRequestData.isEnclosed" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('isEnclosed', 'NO')"
                  [ngClass]="{'selected': specialRequestData.isEnclosed === false}">
                  <ion-checkbox [checked]="specialRequestData.isEnclosed === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Fragile -->
          <div class="request-container margin-top-12">
            <span class="request-text">Fragile</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('isFragile', 'YES')"
                  [ngClass]="{'selected': specialRequestData.isFragile}">
                  <ion-checkbox [checked]="specialRequestData.isFragile" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('isFragile', 'NO')"
                  [ngClass]="{'selected': specialRequestData.isFragile === false}">
                  <ion-checkbox [checked]="specialRequestData.isFragile === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Perishable -->
          <div class="request-container margin-top-12">
            <span class="request-text">Perishable</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('isPerishable', 'YES')"
                  [ngClass]="{'selected': specialRequestData.isPerishable}">
                  <ion-checkbox [checked]="specialRequestData.isPerishable" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('isPerishable', 'NO')"
                  [ngClass]="{'selected': specialRequestData.isPerishable === false}">
                  <ion-checkbox [checked]="specialRequestData.isPerishable === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Dangerous Goods -->
          <div class="request-container margin-top-12">
            <span class="request-text">Dangerous Goods</span>
            <div class="request-section-container margin-top-10">
              <div class="request-type-section-container ion-text-left">
                <div class="request-type-section-item self-padding" (click)="onViewChange('isDangerousGoods', 'YES')"
                  [ngClass]="{'selected': specialRequestData.isDangerousGoods}">
                  <ion-checkbox [checked]="specialRequestData.isDangerousGoods" mode="ios" shape="round"></ion-checkbox>
                  <span>Yes</span>
                </div>
                <div class="request-type-section-item" (click)="onViewChange('isDangerousGoods', 'NO')"
                  [ngClass]="{'selected': specialRequestData.isDangerousGoods === false}">
                  <ion-checkbox [checked]="specialRequestData.isDangerousGoods === false" mode="ios"
                    shape="round"></ion-checkbox>
                  <span>No</span>
                </div>
              </div>
            </div>
          </div>

        </div>

        <!-- Buttons -->
        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn" expand="full" shape="round" type="submit"
            (click)="cancel()">
            <span>Back</span>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn" expand="full" shape="round" type="submit"
            (click)="submitProfile(requestForm.form)">
            <span>Next</span>
          </ion-button>
        </div>

      </form>
    </div>
  </div>
</ion-content>