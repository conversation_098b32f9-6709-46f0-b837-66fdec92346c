 .other-info-page {
     --background: white;
     height: 100%;

     .other-info-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 210px);
         padding: 0px 20px 10px 20px !important;
         overflow-y: auto;

         .shipment-detail-container {
             display: flex;
             flex-direction: column;
             justify-content: space-between;
             width: 100%;
             gap: 5px;

             .shipment-detail {
                 display: flex;
                 justify-content: space-between;
             }

             .shipment-label {
                 font-size: 13px;
             }

             .shipment-value {
                 font-size: 14px;
                 font-weight: 600;
             }
         }

         .form-container {
             min-height: 250px;
         }

         .info-text {
             font-size: 17px;
             font-weight: bold;
         }



         // Ensure all form fields have consistent sizing
         .site-form-control {
             width: 100%;
             margin-bottom: 10px;

             ion-input,
             ion-select,
             ion-textarea {
                 font-size: 14px;
                 --padding-start: 0px !important;
                 --padding-end: 0px;
                 //   --padding-top: 3px;
                 --padding-bottom: 0px;
                 font-weight: 500;
                 min-height: 50px !important;
                 width: 100%;
             }

             ion-select::part(icon) {
                 display: none;
             }

             .dropdown-arrow-icon {
                 margin-top: 19px;
                 font-size: 16px;
             }

             .date-icon {
                 margin-top: 19px;
                 font-size: 16px;
             }

             .start-icon {
                 margin-right: 13px;
                 color: black;
                 width: 18px;
                 height: 18px;
             }
         }

         .site-form-control.disabled {
             opacity: 0.6;
             pointer-events: none;

             ion-input,
             ion-select,
             ion-textarea {
                 //   --background: #f2f2f2; // Light gray background
                 --color: #999999; // Gray text
             }

             ion-icon,
             .date-icon,
             .dropdown-arrow-icon {
                 opacity: 0.5; // Fade out icons for disabled state
             }
         }

         .shipment-status-container {
             display: flex;
             justify-content: space-between;
             margin-top: 15px;
             align-items: center;

             .shipment-text {
                 font-size: 13px;
                 font-weight: 500;
                 white-space: normal;
                 word-break: break-word;
                 overflow-wrap: break-word;
                 max-width: 125px;
                 color: #555555;
             }

             .shipment-type-section-container {
                 display: flex;
                 justify-content: center;

                 .shipment-type-section-item {
                     display: flex;
                     align-items: center;
                     max-width: 80px;
                     vertical-align: middle;
                     border: 1px solid #2c3c64 !important;
                     margin-right: 10px;
                     background: white;
                     border-radius: 10px;
                     padding: 7px 12px;

                     &.selected {
                         background: #FFEA00 !important;
                         border: unset !important;
                     }

                     &.disabled {
                         pointer-events: none;
                         opacity: 0.5;
                     }

                     ion-checkbox,
                     span {
                         display: inline-block;
                         vertical-align: middle;
                         padding: 4px;
                     }

                     span {
                         padding-left: 4px;
                         font-size: 13px;
                         font-weight: 500;
                         margin-top: 2px;
                     }
                 }
             }
         }

         // Enhanced interactive buttons with ripple effects - 50% width each in row
         .shippment-btn-container {
             display: flex;
             flex-direction: row;
             justify-content: space-between;
             align-items: center;
             gap: 16px;
             margin-top: 30px;
             padding: 0;
             width: 100%;

             .site-button {
                 flex: 1;
                 max-width: calc(50% - 8px);
                 height: 54px;
                 border-radius: 16px;
                 font-weight: 600;
                 font-size: 16px;
                 text-transform: none;
                 box-shadow: none !important;
                 --box-shadow: none !important;
                 transition: all 0.3s ease;
                 position: relative;
                 overflow: hidden;
                 user-select: none;

                 &:hover {
                     // Remove complex transforms
                 }

                 &:active {
                     // Remove complex transforms
                 }

                 &.ship-cancel-btn {
                     // Simple cancel button matching app design
                     background: #ffffff;
                     --background-activated: #ffffff !important;
                     color: #6c757d;
                     border: 1px solid #dee2e6;
                     text-transform: uppercase;
                     font-weight: 600;
                     transition: all 0.3s ease;

                     &:hover {
                         background: #f8f9fa;
                         color: #495057;
                         border-color: #adb5bd;
                     }

                     &:active {
                         background: #e9ecef;
                         color: #495057;
                         border-color: #adb5bd;
                     }

                     ion-ripple-effect {
                         color: rgba(108, 117, 125, 0.2);
                     }

                 }

                 &.ship-submit-btn {
                     // Enhanced interactive Next button with white text
                     position: relative;
                     overflow: hidden;
                     text-transform: uppercase;
                     background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                     color: #ffffff;

                     transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

                     &:hover {
                         transform: translateY(-3px) scale(1.02);

                         border-color: #1a252f;
                         background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
                         color: #ffffff;
                     }

                     &:active {
                         transform: translateY(-1px) scale(0.98);

                     }

                     // Enhanced ripple effect for Next button
                     ion-ripple-effect {
                         color: rgba(255, 255, 255, 0.3);
                         z-index: 2;
                     }

                     // Animated border effect
                     &::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: -100%;
                         width: 100%;
                         height: 100%;
                         background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                         transition: left 0.5s ease;
                         z-index: 1;
                     }

                     &:hover::before {
                         left: 100%;
                     }

                     // Subtle pulse animation on focus
                     &:focus {
                         animation: next-pulse 0.6s ease-in-out;
                     }

                     // Enhanced text styling
                     span {
                         font-weight: 700 !important;
                         letter-spacing: 0.8px;
                         text-transform: uppercase;
                         position: relative;
                         z-index: 3;
                         transition: all 0.3s ease;
                         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                     }

                     &:hover span {
                         letter-spacing: 1px;
                         transform: scale(1.05);
                     }
                 }

                 // Ensure ripple effect is visible
                 ion-ripple-effect {
                     z-index: 1;
                     pointer-events: none;
                 }
             }

             // Responsive design - keep buttons in row
             @media (max-width: 480px) {
                 gap: 12px;
                 margin-top: 20px;

                 .site-button {
                     max-width: calc(50% - 6px);
                     height: 48px;
                     font-size: 14px;
                 }
             }

             @media (max-width: 360px) {
                 gap: 8px;

                 .site-button {
                     max-width: calc(50% - 4px);
                     height: 44px;
                     font-size: 13px;
                 }
             }
         }



     }
 }