<ion-content class="other-info-page">
  <app-customer-header [innerPage]="true" [headingText]="'Shipment Basic Info'" [rightAction]="false"
    [backUrl]="backUrl" [hideBellIcon]="true"></app-customer-header>

  <div class="other-info-body-section">

    <div class="shipment-detail-container margin-top-20">
      <div class="shipment-detail">
        <span class="shipment-label">Ref Id</span>
        <span class="shipment-value">{{ shippingData.refID }}</span>
      </div>
      <div class="shipment-detail">
        <span class="shipment-label">Customer</span>
        <span class="shipment-value">
          {{
          authService.isDriver()
          ? (selectedCustomer?.fullName || customerDetail?.fullName)
          : (user?.firstName + " " + user?.lastName)
          }}
        </span>
      </div>
      <div class="shipment-detail" *ngIf="authService.isDriver() && shippingData.contactPersonPhone">
        <span class="shipment-label">Customer Phone</span>
        <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(shippingData.contactPersonPhone)
          }}</span>
      </div>
      <div class="shipment-detail" *ngIf="authService.isCustomer() && user?.phoneNumber">
        <span class="shipment-label">Customer Phone</span>
        <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(user?.phoneNumber)
          }}</span>
      </div>
    </div>

    <div class="margin-top-20">
      <span class="info-text">Enter Details</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #otherInfoForm="ngForm" novalidate>

        <!-- <div class="common-fields-container margin-top-15">
          <div class="field-container small-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': startdate.invalid && onClickValidation}">
              <ion-input readonly="readonly" (click)="openDatePicker('startDate')" label="Start Date"
                labelPlacement="floating" required="required" name="startdate" #startdate="ngModel"
                [(ngModel)]="shippingData.startDate" [value]="shippingData.startDate | date:'dd-MM-yyyy'">
              </ion-input>
              <ion-icon slot="end" class="date-icon" (click)="openDatePicker('startDate')"
                [src]="'/assets/images/svg/calendar.svg'"></ion-icon>
            </ion-item>
            <app-validation-message [field]="startdate" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>

          <div class="field-container large-field">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': enddate.invalid && onClickValidation}">
              <ion-input readonly="readonly" (click)="openDatePicker('endDate')" label="End Date"
                labelPlacement="floating" required="required" name="enddate" #enddate="ngModel"
                [(ngModel)]="shippingData.endDate" [value]="shippingData.endDate | date:'dd-MM-yyyy'">
              </ion-input>
              <ion-icon slot="end" class="date-icon" (click)="openDatePicker('endDate')"
                [src]="'/assets/images/svg/calendar.svg'"></ion-icon>
            </ion-item>
            <app-validation-message [field]="enddate" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </div>
        </div> -->

        <div class="margin-top-10" *ngIf="authService.isDriver()">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': drivername.invalid && onClickValidation}">
            <ion-input name="drivername" #drivername="ngModel" [(ngModel)]="driverName" required="required"
              maxlength="100" pattern="^(?!\s*$)[A-Za-z\s]+$" mode="md" label="Driver Name" labelPlacement="floating">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="drivername" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Only alphabetic characters are allowed.'">
          </app-validation-message>
        </div>

        <div class="margin-top-10 margin-bottom-10" *ngIf="authService.isDriver()">
          <ion-item class="site-form-control" lines="none">
            <ion-select [(ngModel)]="shippingData.status" interface="action-sheet" name="shipStatus" [disabled]="true"
              label="Shipment Status" labelPlacement="floating">
              <ion-select-option *ngFor="let type of shipmentStatus" [value]="type.value">
                {{ type.display }}
              </ion-select-option>
            </ion-select>
            <ion-icon slot="end" class="dropdown-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
          </ion-item>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasOtherInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': shipTypes?.invalid && onClickValidation}">
              <ion-select #shipTypes="ngModel" [(ngModel)]="shippingData.shipmentType" interface="action-sheet"
                required="required" name="shipTypes" label="Shipment Type" labelPlacement="floating">
                <ion-select-option *ngFor="let type of shipmentTypes" [value]="type.value">
                  {{ type.display }}
                </ion-select-option>
              </ion-select>
              <ion-icon slot="end" class="dropdown-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
            </ion-item>
            <app-validation-message [field]="shipTypes" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </ng-container>
        </div>

        <!-- <div class="margin-top-10 margin-bottom-10" *ngIf="authService.isDriver()">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid': types.invalid && onClickValidation}">
            <ion-select #types="ngModel" [(ngModel)]="shippingData.paymentType" interface="action-sheet"
              required="required" name="types" (ionChange)="onPaymentTypeChange()" label="Payment Type"
              labelPlacement="floating">
              <ion-select-option *ngFor="let type of paymentTypes" [value]="type.value">
                {{ type.display }}
              </ion-select-option>
            </ion-select>
            <ion-icon slot="end" class="dropdown-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
          </ion-item>
          <app-validation-message [field]="types" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div> -->

        <div class="margin-top-10 margin-bottom-10" *ngIf="authService.isDriver()">
          <ng-container *ngIf="hasOtherInfo">
            <ion-item class="site-form-control" lines="none" [ngClass]="{ 'disabled': authService.isDriver() }">
              <ion-select #statutypes="ngModel" [(ngModel)]="shippingData.paymentStatus" interface="action-sheet"
                name="statutypes" label="Payment Status" labelPlacement="floating">
                <ion-select-option *ngFor="let status of paymentStatus" [value]="status.value">
                  {{ status.display }}
                </ion-select-option>
              </ion-select>
              <ion-icon slot="end" class="dropdown-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
            </ion-item>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10" *ngIf="authService.isDriver()">
          <ng-container *ngIf="hasOtherInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': onClickValidation && !shippingData.etd}">
              <ion-input required readonly="readonly" (click)="openDatePicker()" label="ETD" labelPlacement="floating"
                name="etd" [value]="etdDisplay">
              </ion-input>
              <ion-icon slot="end" class="date-icon" (click)="openDatePicker()"
                [src]="'/assets/images/svg/calendar.svg'">
              </ion-icon>
            </ion-item>
            <!-- Fallback validation if etd is empty -->
            <div *ngIf="onClickValidation && !shippingData.etd" class="error-message">
              ETD is required
            </div>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10" *ngIf="authService.isCustomer()">
          <ion-item class="site-form-control" lines="none" [ngClass]="{'is-invalid': types.invalid && onClickValidation,
              'disabled': authService.isCustomer() }">
            <ion-select #types="ngModel" [(ngModel)]="shippingData.paymentType" interface="action-sheet"
              required="required" name="types" label="Payment Type" labelPlacement="floating">
              <ion-select-option *ngFor="let type of paymentTypes" [value]="type.value">
                {{ type.display }}
              </ion-select-option>
            </ion-select>
            <ion-icon slot="end" class="dropdown-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
          </ion-item>
          <app-validation-message [field]="types" [onClickValidation]="onClickValidation">
          </app-validation-message>
        </div>

        <div class="margin-top-10 margin-bottom-10"
          *ngIf="authService.isCustomer() && shippingEditMode === 'edit' && shippingData.paymentStatus">
          <ng-container *ngIf="hasOtherInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'disabled': authService.isCustomer() && shippingEditMode === 'edit' && shippingData.paymentStatus }">
              <ion-select #statutypes="ngModel" [(ngModel)]="shippingData.paymentStatus" interface="action-sheet"
                name="statutypes" label="Payment Status" labelPlacement="floating">
                <ion-select-option *ngFor="let status of paymentStatus" [value]="status.value">
                  {{ status.display }}
                </ion-select-option>
              </ion-select>
              <ion-icon slot="end" class="dropdown-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
            </ion-item>
          </ng-container>
        </div>

        <div class="margin-top-10 margin-bottom-10"
          *ngIf="authService.isCustomer() && shippingEditMode === 'edit' && shippingData.etd">
          <ng-container *ngIf="hasOtherInfo">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{ 'disabled': authService.isCustomer() && shippingEditMode === 'edit' && shippingData.etd }">
              <ion-input [readonly]="authService.isCustomer() && shippingEditMode === 'edit' && shippingData.etd"
                label="ETD" labelPlacement="floating" name="etd" [value]="etdDisplay">
              </ion-input>
              <ion-icon slot="end" class="date-icon" [src]="'/assets/images/svg/calendar.svg'">
              </ion-icon>
            </ion-item>
          </ng-container>
        </div>

        <div class="shipment-status-container" *ngIf="authService.isDriver()">
          <span class="shipment-text">Shipment Secured Properly</span>
          <div>
            <div class="shipment-type-section-container ion-text-left">
              <div class="shipment-type-section-item self-padding" (click)="onViewChange('YES')"
                [ngClass]="{'selected': view === 'YES'}">
                <ion-checkbox [checked]="view==='YES'" mode="ios" shape="round"></ion-checkbox> <span>
                  Yes
                </span>
              </div>
              <div class="shipment-type-section-item" (click)="onViewChange('NO')"
                [ngClass]="{'selected': view === 'NO'}">
                <ion-checkbox [checked]="view==='NO'" mode="ios" shape="round"></ion-checkbox> <span>
                  No
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="margin-top-10 margin-bottom-10">
          <ng-container *ngIf="hasOtherInfo">
            <ion-item class="site-form-control" lines="none">
              <ion-textarea label="Shipment Summary" labelPlacement="floating" name="summary" #summary="ngModel"
                [(ngModel)]="shippingData.summary" maxlength="500">
              </ion-textarea>
            </ion-item>
          </ng-container>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="cancel()">
            <span class="cancel-text">Back</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="submitProfile(otherInfoForm.form)">
            <span>Next</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
        </div>

      </form>
    </div>

  </div>
</ion-content>