import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { DatePicker } from '@pantrist/capacitor-date-picker';
import { Subscription } from 'rxjs';
import { ShippingBasicInfo } from 'src/modals/shipping-info';
import { CommonService } from 'src/services/common.service';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-other-info',
  templateUrl: './other-info.component.html',
  styleUrls: ['./other-info.component.scss'],
  standalone: false
})
export class OtherInfoComponent implements OnInit {

  onClickValidation!: boolean;
  shippingData: ShippingBasicInfo = new ShippingBasicInfo();
  activeSubscriptions: Subscription = new Subscription();
  shipmentStatus: Array<{ display: string, value: string }> = [
    { display: 'New', value: 'NEW' },
    { display: 'Assigned', value: 'ASSIGNED' },
    { display: 'In Transit', value: 'IN_TRANSIT' },
    { display: 'Delivered', value: 'DELIVERED' },
    { display: 'Completed', value: 'COMPLETED' }
  ];
  shipmentTypes: Array<{ display: string, value: string }> = [
    { display: 'Rush', value: 'RUSH' },
    { display: 'Flat Deck', value: 'FLAT_DECK' },
    { display: 'Truck & Trailer', value: 'TRUCK_&_TRAILER' },
    { display: 'Regular', value: 'REGULAR' }
  ];
  paymentTypes: Array<{ display: string, value: string }> = [
    { display: 'Prepaid', value: 'PREPAID' },
    { display: 'Collect', value: 'COLLECT' }
  ];
  paymentStatus: Array<{ display: string, value: string }> = [
    { display: 'Pending', value: 'PENDING' },
    { display: 'Invoiced', value: 'INVOICED' }
  ];
  view: string = "YES";
  user: any;
  driverName!: string;
  hasOtherInfo = true;
  etdDisplay: string = '';
  backUrl!: string;
  shippingEditMode: any;
  selectedCustomer: any;
  customerDetail: any;

  constructor(
    private readonly navController: NavController,
    public commonService: CommonService,
    private toastService: ToastService,
    private readonly localStorageService: LocalStorageService,
    public readonly authService: AuthService
  ) {

  }

  ngOnInit(): void {
    this.loadShippingData();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;
    this.user = this.authService.getUser();

    if (this.authService.isDriver()) {
      this.backUrl = '/portal/basic/info';
      this.selectedCustomer = this.localStorageService.getObject("SELECTED_CUSTOMER");
      this.customerDetail = this.localStorageService.getObject("CUSTOMER_DETAILS");
    }
    else {
      this.backUrl = '/client/portal/basic/info';
    }

    this.shippingEditMode = this.localStorageService.get("SHIPPING_MODE");

    this.loadShippingData();
    this.initializeDefaults();
    this.setDriverName();
    this.evaluateOtherInfoVisibility();
  }

  private loadShippingData(): void {
    const storedData = this.localStorageService.getObject("SHIPPING_INFO");
    this.shippingData = storedData ? ShippingBasicInfo.fromResponse(storedData) : new ShippingBasicInfo();

    if (this.shippingData.etd) {
      this.etdDisplay = this.commonService.formatDisplayDate(this.shippingData.etd);
    }
  }

  private initializeDefaults(): void {
    if (this.authService.isDriver()) {
      this.shippingData.status = 'ASSIGNED';
      if (!this.shippingData.paymentStatus) {
        this.shippingData.paymentStatus = 'PENDING';
      }
    }
    this.shippingData.driver = null;
    if (!this.shippingData.paymentType) {
      this.shippingData.paymentType = 'PREPAID';
    }
    this.view = this.shippingData.isSecured ? 'YES' : 'NO';
  }

  private setDriverName(): void {
    const first = this.commonService.formatText(this.user?.firstName);
    const last = this.commonService.formatText(this.user?.lastName);
    this.driverName = `${first} ${last}`.trim();
  }

  private evaluateOtherInfoVisibility(): void {
    const info = this.shippingData;

    // ETD and Summary are no longer required fields
    const missingRequiredFields = !info.shipmentType;
    if (missingRequiredFields) {
      this.hasOtherInfo = false;
      setTimeout(() => {
        this.hasOtherInfo = true;
      }, 0);
    } else {
      this.hasOtherInfo = true;
    }
  }

  onPaymentTypeChange(): void {
    this.localStorageService.setObject("SHIPPING_INFO", this.shippingData);
  }

  onViewChange(newView: string) {
    this.view = newView;
    this.shippingData.isSecured = newView === 'YES';
  }

  // openDatePicker(type: 'startDate' | 'endDate') {
  //   const today = new Date();
  //   let dateToUse: Date = today;
  //   let minDate: string | undefined;
  //   let maxDate: string | undefined;

  //   if (type === 'startDate') {
  //     dateToUse = this.shippingData.startDate ? new Date(this.shippingData.startDate) : today;
  //   } else {
  //     if (!this.shippingData.startDate) {
  //       this.toastService.show("Please select 'Start Date' first.");
  //       return;
  //     }
  //     dateToUse = this.shippingData.endDate ? new Date(this.shippingData.endDate) : today;
  //     minDate = `${new Date(this.shippingData.startDate).getDate().toString().padStart(2, '0')}/${(new Date(this.shippingData.startDate).getMonth() + 1).toString().padStart(2, '0')}/${new Date(this.shippingData.startDate).getFullYear()}`;
  //   }

  //   const formattedDate = `${dateToUse.getDate().toString().padStart(2, '0')}/${(dateToUse.getMonth() + 1).toString().padStart(2, '0')}/${dateToUse.getFullYear()}`;

  //   DatePicker.present({
  //     mode: 'date',
  //     format: 'dd/MM/yyyy',
  //     min: minDate,
  //     date: formattedDate,
  //   }).then(date => {
  //     if (date && date.value) {
  //       const [day, month, year] = date.value.split('/');
  //       if (type === 'startDate') {
  //         this.shippingData.startDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  //       } else {
  //         this.shippingData.endDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  //       }
  //     }
  //   }).catch(error => {
  //   });
  // }

  openDatePicker() {
    const selectedDate = this.shippingData.etd
      ? new Date(this.shippingData.etd)
      : new Date();

    const formattedDate = selectedDate.toLocaleDateString('en-US').split('/').join('/');

    DatePicker.present({
      mode: 'date',
      format: 'MM/dd/yyyy',
      date: formattedDate
    }).then(date => {
      if (date?.value) {
        const [month, day, year] = date.value.split('/');

        // Save backend format
        this.shippingData.etd = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;

        // Save display format
        this.etdDisplay = `${month.padStart(2, '0')}/${day.padStart(2, '0')}/${year}`;
      }
    }).catch(error => {
    });
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid || !this.shippingData.etd) {
      return;
    }

    this.localStorageService.setObject("SHIPPING_INFO", this.shippingData);
    if (this.authService.isDriver()) {
      this.navController.navigateForward("/portal/pickup/delivery", { animated: true });
      return;
    }
    this.navController.navigateForward("/client/portal/pickup/delivery", { animated: true });
  }

  cancel() {
    if (this.authService.isDriver()) {
      this.navController.navigateBack("/portal/basic/info", { animated: true });
      return;
    }
    this.navController.navigateBack("/client/portal/basic/info", { animated: true });
  }

}
