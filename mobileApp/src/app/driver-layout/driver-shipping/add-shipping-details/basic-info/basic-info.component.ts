import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import mask from './../../../../../shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { NgModel } from '@angular/forms';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { ToastService } from 'src/shared/toast.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ShippingBasicInfo } from 'src/modals/shipping-info';
import { ActivatedRoute } from '@angular/router';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';

@Component({
  selector: 'app-basic-info',
  templateUrl: './basic-info.component.html',
  styleUrls: ['./basic-info.component.scss'],
  standalone: false
})
export class BasicInfoComponent implements OnInit {

  onClickValidation!: boolean;
  refIdData!: string;
  selectedCustomerId!: string;
  shippingData: ShippingBasicInfo = new ShippingBasicInfo();
  activeSubscriptions: Subscription = new Subscription();
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;
  allCustomers: Array<any> = [];
  hasBasicInfo = true;
  displayPhoneNumber: string = '';
  backUrl!: string;

  isKeyContactEditable: boolean = true;
  isKeyContactEmailEditable: boolean = true;
  isKeyContactPhoneEditable: boolean = true;

  constructor(
    private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    public commonService: CommonService,
    private readonly localStorageService: LocalStorageService,
    private readonly route: ActivatedRoute,
    public readonly authService: AuthService
  ) { }

  ngOnInit(): void {
    const shippingData = this.localStorageService.getObject("SHIPPING_INFO");
    this.shippingData = shippingData ? ShippingBasicInfo.fromResponse(shippingData) : new ShippingBasicInfo();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;

    this.route.queryParams.subscribe(params => {
      const shouldReset = params['reset'];

      if (shouldReset) {
        this.handleReset(params);
      } else {
        this.restoreFromStorage(params);
      }
    });

    if (this.authService.isDriver()) {
      this.backUrl = '/portal/shipping';
      this.getAllCustomers();
    } else {
      this.backUrl = '/client/portal/shipping';
      this.getPickupLocationDetails();
    }

    // Format for display if phone exists
    // if (this.shippingData.contactPersonPhone) {
    //   this.displayPhoneNumber = this.commonService.formatPhoneForDisplay(this.shippingData.contactPersonPhone);
    // }

    this.shippingData.barcode = null;
  }

  private handleReset(params: any): void {
    this.isKeyContactEditable = true;
    this.isKeyContactEmailEditable = true;
    this.isKeyContactPhoneEditable = true;

    this.shippingData = new ShippingBasicInfo();
    this.hasBasicInfo = false;
    this.displayPhoneNumber = '';
    this.selectedCustomerId = '';

    this.localStorageService.remove("SHIPPING_INFO");
    this.localStorageService.remove("SELECTED_CUSTOMER");
    this.localStorageService.remove("SHIPPING_MODE");

    this.refIdData = params['refIdData'] || null;
    if (this.refIdData) {
      this.shippingData.refID = this.refIdData;
    }

    // Force UI refresh
    setTimeout(() => {
      this.hasBasicInfo = true;
    }, 0);
  }

  private restoreFromStorage(params: any): void {
    const storedShipping = this.localStorageService.getObject("SHIPPING_INFO");
    this.shippingData = storedShipping ? ShippingBasicInfo.fromResponse(storedShipping) : new ShippingBasicInfo();

    const selectedCustomer = this.localStorageService.getObject("SELECTED_CUSTOMER");
    if (storedShipping?.customer) {
      this.selectedCustomerId = storedShipping.customer;
    } else if (selectedCustomer?.id) {
      this.selectedCustomerId = selectedCustomer.id;
    }

    if (this.shippingData.contactPersonPhone && this.shippingData.contactPersonPhone.trim() !== '') {
      const digits = this.shippingData.contactPersonPhone.replace(/\D/g, '').slice(0, 10);
      this.shippingData.contactPersonPhone = digits;
      this.displayPhoneNumber = this.commonService.formatPhoneForDisplay(digits);
    }

    if (storedShipping) {
      if (storedShipping.contactPersonName && storedShipping.contactPersonName.trim() !== '') {
        this.isKeyContactEditable = false;
      } else {
        this.isKeyContactEditable = true;
      }

      if (storedShipping.contactPersonEmail && storedShipping.contactPersonEmail.trim() !== '') {
        this.isKeyContactEmailEditable = false;
      } else {
        this.isKeyContactEmailEditable = true;
      }

      if (storedShipping.contactPersonPhone && storedShipping.contactPersonPhone.trim() !== '') {
        this.isKeyContactPhoneEditable = false;
      } else {
        this.isKeyContactPhoneEditable = true;
      }
    } else {
      // No SHIPPING_INFO, always allow editing
      this.isKeyContactEditable = true;
      this.isKeyContactEmailEditable = true;
      this.isKeyContactPhoneEditable = true;
    }

    const mode = this.localStorageService.get("SHIPPING_MODE");
    if (mode === "edit") {
      this.shippingData.id = this.shippingData.id;
      this.shippingData.contactPersonPhone = this.commonService.formatPhoneForDisplay(this.shippingData.contactPersonPhone);
    }

    this.refIdData = params['refIdData'] || null;
    if (this.refIdData) {
      this.shippingData.refID = this.refIdData;
    }
  }

  getPickupLocationDetails() {
    this.loadingService.show();
    this.activeSubscriptions.add(
      this.dataService.getPickupLocationDetail().subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data[0];
          this.localStorageService.setObject("CUSTOMER_SHIPPING_PICKUP_DETAILS", data);
          this.getPickupDetails(data);
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  getPickupDetails(data: any) {
    if (data) {
      const customerDetail = data.customerDetail || {};

      if (!this.shippingData.contactPersonName && customerDetail.keyContact) {
        this.shippingData.contactPersonName = customerDetail.keyContact || null;
      }

      if (!this.shippingData.contactPersonEmail && customerDetail.keyContactEmail) {
        this.shippingData.contactPersonEmail = customerDetail.keyContactEmail || null;
      }

      if (!this.shippingData.contactPersonPhone && customerDetail.keyContactPhone) {
        const rawPhone = (customerDetail.keyContactPhone || null).replace(/\D/g, '').slice(0, 10);
        this.shippingData.contactPersonPhone = rawPhone;
        this.displayPhoneNumber = this.commonService.formatPhoneForDisplay(rawPhone);
      }

    }
  }

  protected get countryIsoCode(): string {
    const phone = this.shippingData?.contactPersonPhone;
    if (!phone || phone.trim() === '') return '';
    const code = maskitoGetCountryFromNumber(phone, metadata) ?? '';
    return code ? `/assets/images/icons/flags/ca.png` : '';
  }

  protected get pattern(): string {
    return '^\\+1\\d{10}$'; // +1 followed by exactly 10 digits
  }

  formatPhoneNumberss(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';
    const digits = value.replace(/\D/g, '').slice(0, 10);
    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }
    this.shippingData.contactPersonPhone = formatted;
    input.value = formatted;

    if (digits.length < 10) {
      userPhone.control.setErrors({ required: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    // Remove non-digits, limit to 10
    const digits = value.replace(/\D/g, '').slice(0, 10);

    // Format for display
    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.displayPhoneNumber = formatted;
    input.value = formatted;

    // Store only digits (no +1, no dashes, no spaces)
    this.shippingData.contactPersonPhone = digits;

    if (digits.length < 10) {
      userPhone.control.setErrors({ required: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  getAllCustomers() {
    this.loadingService.show();
    this.dataService.getAllCustomers().subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        if (response?.data?.length) {
          this.allCustomers = response.data.map((customer: any) => ({
            id: customer.id,
            fullName: customer.fullName || '',
            customerDetail: customer.customerDetail || {},
            addressDetail: customer.addressDetail || {}  // Store full object
          }));
        }
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  onCustomerChange() {
    const selectedCustomer = this.allCustomers.find(c => c.id === this.selectedCustomerId);
    if (selectedCustomer) {
      this.shippingData.customer = selectedCustomer.id;

      const detail = selectedCustomer.customerDetail || {};
      const address = selectedCustomer.addressDetail || {};

      // Check and populate or allow input
      if (detail.keyContact && detail.keyContact.trim() !== '') {
        this.shippingData.contactPersonName = detail.keyContact;
        this.isKeyContactEditable = false;
      } else {
        this.shippingData.contactPersonName = null;
        this.isKeyContactEditable = true;
      }

      if (detail.keyContactEmail && detail.keyContactEmail.trim() !== '') {
        this.shippingData.contactPersonEmail = detail.keyContactEmail;
        this.isKeyContactEmailEditable = false;
      } else {
        this.shippingData.contactPersonEmail = null;
        this.isKeyContactEmailEditable = true;
      }

      if (detail.keyContactPhone && detail.keyContactPhone.trim() !== '') {
        const rawPhone = detail.keyContactPhone.replace(/\D/g, '').slice(0, 10);
        this.shippingData.contactPersonPhone = rawPhone;
        this.displayPhoneNumber = this.commonService.formatPhoneForDisplay(rawPhone);
        this.isKeyContactPhoneEditable = false;
      } else {
        this.shippingData.contactPersonPhone = null;
        this.displayPhoneNumber = '';
        this.isKeyContactPhoneEditable = true;
      }

      const mode = this.localStorageService.get("SHIPPING_MODE");
      if (mode === 'edit') {
        this.shippingData.pickupCompanyName = detail.companyName || '';
        this.shippingData.pickupContactPersonName = detail.keyContact || '';
        this.shippingData.pickupContactPersonPhone = this.commonService.formatPhoneForDisplay(detail.keyContactPhone);

        this.shippingData.pickupAddressDetail.address = address.address || '';
        this.shippingData.pickupAddressDetail.city = address.city || '';
        this.shippingData.pickupAddressDetail.state = address.state || '';
        this.shippingData.pickupAddressDetail.pin = address.pin || '';
        this.shippingData.pickupAddressDetail.country = address.country || '';
        this.shippingData.pickupAddressDetail.latitude = address.latitude || '';
        this.shippingData.pickupAddressDetail.longitude = address.longitude || '';
      }

      this.localStorageService.setObject("SELECTED_CUSTOMER", selectedCustomer);
      this.localStorageService.setObject("SHIPPING_INFO", this.shippingData);
    }
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;
    if (!form.valid) return;

    this.localStorageService.setObject("SHIPPING_INFO", this.shippingData);
    if (this.authService.isDriver()) {
      this.navController.navigateForward("/portal/other/info", { animated: true });
      return;
    }
    this.navController.navigateForward("/client/portal/other/info", { animated: true });
  }

  cancel() {
    if (this.authService.isDriver()) {
      this.navController.navigateRoot("/portal/shipping", { animated: true });
      return;
    }
    this.navController.navigateRoot("/client/portal/shipping", { animated: true });
  }

}
