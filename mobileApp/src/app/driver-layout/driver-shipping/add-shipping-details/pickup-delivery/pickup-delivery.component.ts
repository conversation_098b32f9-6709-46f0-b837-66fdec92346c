import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { ToastService } from 'src/shared/toast.service';
import mask from './../../../../../shared/phone-number.mask';
import { maskitoGetCountryFromNumber } from '@maskito/phone';
import metadata from 'libphonenumber-js/min/metadata';
import { NgModel } from '@angular/forms';
import { ShippingBasicInfo } from 'src/modals/shipping-info';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { AuthService } from 'src/shared/authservice';
import { GooglePlacesService } from 'src/services/google-places-service';

@Component({
  selector: 'app-pickup-delivery',
  templateUrl: './pickup-delivery.component.html',
  styleUrls: ['./pickup-delivery.component.scss'],
  standalone: false
})
export class PickupDeliveryComponent implements OnInit {

  onClickValidation!: boolean;
  selectedCustomer: any;
  shippingData: ShippingBasicInfo = new ShippingBasicInfo();
  activeSubscriptions: Subscription = new Subscription();
  isApple: boolean = false;
  code: string = "";
  protected readonly mask = mask;
  hasPickupDeliveryData = true;
  displayPickupPhone: string = '';
  backUrl!: string;
  pickupDetails: any;
  isLocationSelectionPopupOpen: boolean = false;
  searchLocation: string | null = null;
  availablePlaces: any[] = [];
  phoneInvalid: boolean = false;
  user: any;
  customerDetail: any;

  constructor(
    private readonly navController: NavController,
    public commonService: CommonService,
    private toastService: ToastService,
    private readonly localStorageService: LocalStorageService,
    public readonly authService: AuthService,
    private googlePlaces: GooglePlacesService,
  ) {

  }

  ngOnInit(): void {
    this.loadShippingData();
  }

  ionViewWillEnter(): void {
    this.onClickValidation = false;
    this.user = this.authService.getUser();

    this.customerDetail = this.localStorageService.getObject("CUSTOMER_DETAILS");

    this.loadShippingData();
    this.evaluatePickupInfoVisibility();

    if (this.authService.isCustomer()) {
      this.getPickupDetails();
      this.backUrl = '/client/portal/other/info';
    }
    else {
      this.selectedCustomers();
      this.backUrl = '/portal/other/info';
    }
  }

  private loadShippingData(): void {
    const shippingData = this.localStorageService.getObject("SHIPPING_INFO");
    this.shippingData = shippingData ? ShippingBasicInfo.fromResponse(shippingData) : new ShippingBasicInfo();

    const mode = this.localStorageService.get("SHIPPING_MODE");
    if (mode === "edit" && this.shippingData.pickupContactPersonPhone) {
      const digits = this.shippingData.pickupContactPersonPhone.replace(/\D/g, '').slice(0, 10);
      this.shippingData.pickupContactPersonPhone = digits;
      this.displayPickupPhone = this.commonService.formatPhoneForDisplay(digits);
    }
  }

  private selectedCustomers(): void {
    this.selectedCustomer = this.localStorageService.getObject("SELECTED_CUSTOMER");

    if (this.selectedCustomer) {
      const customerDetail = this.selectedCustomer.customerDetail || {};
      const addressDetail = this.selectedCustomer.addressDetail || {};

      // Only prefill if field is empty — don't overwrite existing input
      if (!this.shippingData.pickupCompanyName) {
        this.shippingData.pickupCompanyName = customerDetail.companyName || '';
      }

      if (!this.shippingData.pickupContactPersonName) {
        this.shippingData.pickupContactPersonName = customerDetail.keyContact || '';
      }

      // if (!this.shippingData.pickupContactPersonPhone && customerDetail.keyContactPhone) {
      //   this.shippingData.pickupContactPersonPhone = this.commonService.formatPhoneForDisplay(customerDetail.keyContactPhone);
      // }
      if (!this.shippingData.pickupContactPersonPhone && customerDetail.keyContactPhone) {
        const rawPhone = (customerDetail.keyContactPhone || '').replace(/\D/g, '').slice(0, 10);
        this.shippingData.pickupContactPersonPhone = rawPhone;
        this.displayPickupPhone = this.commonService.formatPhoneForDisplay(rawPhone);
      }

      // For address — only fill if not already set
      if (!this.shippingData.pickupAddressDetail.address) {
        this.shippingData.pickupAddressDetail.address = addressDetail.address || '';
      }
      if (!this.shippingData.pickupAddressDetail.city) {
        this.shippingData.pickupAddressDetail.city = addressDetail.city || '';
      }
      if (!this.shippingData.pickupAddressDetail.state) {
        this.shippingData.pickupAddressDetail.state = addressDetail.state || '';
      }
      if (!this.shippingData.pickupAddressDetail.pin) {
        this.shippingData.pickupAddressDetail.pin = addressDetail.pin || '';
      }
      if (!this.shippingData.pickupAddressDetail.country) {
        this.shippingData.pickupAddressDetail.country = addressDetail.country || '';
      }
      this.shippingData.pickupAddressDetail.latitude = addressDetail.latitude || '';
      this.shippingData.pickupAddressDetail.longitude = addressDetail.longitude || '';
    }
  }

  private evaluatePickupInfoVisibility(): void {
    const info = this.shippingData;

    const missingRequiredFields = !info.pickupCompanyName || !info.pickupContactPersonName;
    if (missingRequiredFields) {
      this.hasPickupDeliveryData = false;
      setTimeout(() => {
        this.hasPickupDeliveryData = true;
      }, 0);
    } else {
      this.hasPickupDeliveryData = true;
    }
  }

  private getPickupDetails(): void {
    this.pickupDetails = this.localStorageService.getObject("CUSTOMER_SHIPPING_PICKUP_DETAILS");

    if (this.pickupDetails) {
      const customerDetail = this.pickupDetails.customerDetail || {};
      const addressDetail = this.pickupDetails.addressDetail || {};

      // Only prefill if field is empty — don't overwrite existing input
      if (!this.shippingData.pickupCompanyName && customerDetail.companyName) {
        this.shippingData.pickupCompanyName = customerDetail.companyName || '';
      }

      if (!this.shippingData.pickupContactPersonName && customerDetail.keyContact) {
        this.shippingData.pickupContactPersonName = customerDetail.keyContact || '';
      }

      if (!this.shippingData.pickupContactPersonPhone && customerDetail.keyContactPhone) {
        const rawPhone = (customerDetail.keyContactPhone || '').replace(/\D/g, '').slice(0, 10);
        this.shippingData.pickupContactPersonPhone = rawPhone;
        this.displayPickupPhone = this.commonService.formatPhoneForDisplay(rawPhone);
      }

      // For address — only fill if not already set
      if (!this.shippingData.pickupAddressDetail.address) {
        this.shippingData.pickupAddressDetail.address = addressDetail.address || '';
      }
      if (!this.shippingData.pickupAddressDetail.city) {
        this.shippingData.pickupAddressDetail.city = addressDetail.city || '';
      }
      if (!this.shippingData.pickupAddressDetail.state) {
        this.shippingData.pickupAddressDetail.state = addressDetail.state || '';
      }
      if (!this.shippingData.pickupAddressDetail.pin) {
        this.shippingData.pickupAddressDetail.pin = addressDetail.pin || '';
      }
      if (!this.shippingData.pickupAddressDetail.country) {
        this.shippingData.pickupAddressDetail.country = addressDetail.country || '';
      }
      this.shippingData.pickupAddressDetail.latitude = addressDetail.latitude || '';
      this.shippingData.pickupAddressDetail.longitude = addressDetail.longitude || '';
    }
  }

  openLocationSelectionPopup() {
    this.isLocationSelectionPopupOpen = true;
  }

  closeLocationSelectionPopup() {
    this.isLocationSelectionPopupOpen = false;
    this.searchLocation = null;
    this.availablePlaces = [];
  }

  async fetchPlaces(): Promise<void> {
    if (!this.searchLocation) {
      this.availablePlaces = [];
      return;
    }
    try {
      this.availablePlaces = await this.googlePlaces.fetchAutocomplete(this.searchLocation.trim());
    } catch (error: any) {
      this.availablePlaces = [];
    }
  }

  async onSelectLocation(place: any): Promise<void> {
    try {
      const result = await this.googlePlaces.fetchPlaceDetails(place.place_id);
      const components = result.address_components;

      const getComponent = (type: string) =>
        components.find((c: any) => c.types.includes(type))?.long_name || '';

      const lat = result.geometry?.location?.lat?.();
      const lng = result.geometry?.location?.lng?.();

      this.shippingData.pickupAddressDetail = {
        address: result.formatted_address,
        city: getComponent('locality') || getComponent('sublocality'),
        state: getComponent('administrative_area_level_1'),
        pin: getComponent('postal_code'),
        country: getComponent('country'),
        latitude: lat?.toString() || '',
        longitude: lng?.toString() || '',
      };
      this.searchLocation = this.shippingData.pickupAddressDetail.address;
      this.availablePlaces = [];
      this.toastService.show('Address fetched successfully!');
      this.closeLocationSelectionPopup();
    } catch (error: any) {
      this.toastService.show(error.message || 'Error fetching place details');
    }
  }

  protected get countryIsoCode(): string {
    const phone = this.shippingData?.pickupContactPersonPhone;
    if (!phone || phone.trim() === '') {
      return '';
    }

    const code = maskitoGetCountryFromNumber(phone, metadata) ?? '';
    //  return code ? `/assets/images/icons/flags/${code.toLowerCase()}.png` : '';
    return code ? `/assets/images/icons/flags/ca.png` : '';
  }

  protected get pattern(): string {
    return '^\\+1\\d{10}$'; // +1 followed by exactly 10 digits
  }

  formatPhoneNumber(event: any, userPhone: NgModel): void {
    const input = event.target;
    let value: string = input.value || '';

    const digits = value.replace(/\D/g, '').slice(0, 10);

    let formatted = digits;
    if (digits.length > 6) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      formatted = `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }

    this.displayPickupPhone = formatted;
    input.value = formatted;

    this.shippingData.pickupContactPersonPhone = digits;

    // Only valid if exactly 10 digits
    this.phoneInvalid = digits.length !== 10;

    if (this.phoneInvalid) {
      userPhone.control.setErrors({ invalid: true });
    } else {
      userPhone.control.setErrors(null);
    }
  }

  get isCityOnlySpaces(): boolean {
    const value = this.shippingData.pickupAddressDetail.city;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isProvinceOnlySpaces(): boolean {
    const value = this.shippingData.pickupAddressDetail.state;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isPostalOnlySpaces(): boolean {
    const value = this.shippingData.pickupAddressDetail.pin;
    return typeof value === 'string' && value.trim().length === 0;
  }

  get isCountryOnlySpaces(): boolean {
    const value = this.shippingData.pickupAddressDetail.country;
    return typeof value === 'string' && value.trim().length === 0;
  }

  async submitProfile(form: any): Promise<void> {
    this.onClickValidation = true;

    if (!form.valid) {
      return;
    }

    this.shippingData.pickupAddressDetail.city = this.shippingData.pickupAddressDetail.city?.trim() || null;
    this.shippingData.pickupAddressDetail.state = this.shippingData.pickupAddressDetail.state?.trim() || null;
    this.shippingData.pickupAddressDetail.pin = this.shippingData.pickupAddressDetail.pin?.trim() || null;
    this.shippingData.pickupAddressDetail.country = this.shippingData.pickupAddressDetail.country?.trim() || null;

    this.localStorageService.setObject("SHIPPING_INFO", this.shippingData);
    if (this.authService.isDriver()) {
      this.navController.navigateForward("/portal/delivery/location", { animated: true });
      return;
    }
    this.navController.navigateForward("/client/portal/delivery/location", { animated: true });
  }

  cancel() {
    if (this.authService.isDriver()) {
      this.navController.navigateBack("/portal/other/info", { animated: true });
      return;
    }
    this.navController.navigateBack("/client/portal/other/info", { animated: true });
  }

}
