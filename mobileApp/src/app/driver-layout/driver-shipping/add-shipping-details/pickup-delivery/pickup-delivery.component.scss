 .pickup-delivery-page {
     --background: white;
     height: 100%;

     .pickup-delivery-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 210px);
         padding: 0px 20px 10px 20px !important;
         overflow-y: auto;

         .shipment-detail-container {
             display: flex;
             flex-direction: column;
             justify-content: space-between;
             width: 100%;
             gap: 5px;

             .shipment-detail {
                 display: flex;
                 justify-content: space-between;
             }

             .shipment-label {
                 font-size: 13px;
             }

             .shipment-value {
                 font-size: 14px;
                 font-weight: 600;
             }
         }

         .form-container {
             min-height: 250px;
         }

         .info-text {
             font-size: 17px;
             font-weight: bold;
         }

         // Fetch address button styling
         .fetch-address-btn {
             --color: #FFEA00;
             --background: transparent;
             --border-radius: 50%;
             --padding-start: 8px;
             --padding-end: 8px;
             --padding-top: 8px;
             --padding-bottom: 8px;
             margin: 0;
             height: 40px;
             width: 40px;

             ion-icon {
                 font-size: 20px;
                 color: #FFEA00;
             }

             &:hover {
                 --background: rgba(255, 234, 0, 0.1);
             }
         }

         // Enhanced interactive buttons with ripple effects - 50% width each in row
         .shippment-btn-container {
             display: flex;
             flex-direction: row;
             justify-content: space-between;
             align-items: center;
             gap: 16px;
             margin-top: 30px;
             padding: 0;
             width: 100%;

             .site-button {
                 flex: 1;
                 max-width: calc(50% - 8px);
                 height: 54px;
                 border-radius: 16px;
                 font-weight: 600;
                 font-size: 16px;
                 text-transform: none;
                 box-shadow: none !important;
                 --box-shadow: none !important;
                 transition: all 0.3s ease;
                 position: relative;
                 overflow: hidden;
                 user-select: none;

                 &:hover {
                     // Remove complex transforms
                     box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
                 }

                 &:active {
                     transform: translateY(-1px);
                 }

                 &.ship-cancel-btn {
                     // Simple cancel button matching app design
                     background: #ffffff;
                     --background-activated: #ffffff !important;
                     color: #6c757d;
                     border: 1px solid #dee2e6;
                     text-transform: uppercase;
                     font-weight: 600;
                     transition: all 0.3s ease;

                     &:hover {
                         background: #f8f9fa;
                         color: #495057;
                         border-color: #adb5bd;
                     }

                     &:active {
                         background: #e9ecef;
                         color: #495057;
                         border-color: #adb5bd;
                     }

                     ion-ripple-effect {
                         color: rgba(108, 117, 125, 0.2);
                     }

                     // Animated border effect
                     &::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: -100%;
                         width: 100%;
                         height: 100%;
                         background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
                         transition: left 0.5s ease;
                         z-index: 1;
                     }

                     &:hover::before {
                         left: 100%;
                     }

                     // Subtle shake animation on focus
                     &:focus {
                         animation: subtle-shake 0.5s ease-in-out;
                     }
                 }

                 &.ship-submit-btn {
                     // Enhanced interactive Next button with white text
                     position: relative;
                     overflow: hidden;
                     text-transform: uppercase;
                     background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
                     color: #ffffff;
                     box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
                     transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

                     &:hover {
                         transform: translateY(-3px) scale(1.02);
                         box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                         border-color: #1a252f;
                         background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
                         color: #ffffff;
                     }

                     &:active {
                         transform: translateY(-1px) scale(0.98);
                         box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
                     }

                     // Enhanced ripple effect for Next button
                     ion-ripple-effect {
                         color: rgba(255, 255, 255, 0.3);
                         z-index: 2;
                     }

                     // Animated border effect
                     &::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: -100%;
                         width: 100%;
                         height: 100%;
                         background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                         transition: left 0.5s ease;
                         z-index: 1;
                     }

                     &:hover::before {
                         left: 100%;
                     }

                     // Subtle pulse animation on focus
                     &:focus {
                         animation: next-pulse 0.6s ease-in-out;
                     }

                     // Enhanced text styling
                     span {
                         font-weight: 700 !important;
                         letter-spacing: 0.8px;
                         text-transform: uppercase;
                         position: relative;
                         z-index: 3;
                         transition: all 0.3s ease;
                         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                     }

                     &:hover span {
                         letter-spacing: 1px;
                         transform: scale(1.05);
                     }
                 }

                 // Ensure ripple effect is visible
                 ion-ripple-effect {
                     z-index: 1;
                     pointer-events: none;
                 }
             }

             // Responsive design - keep buttons in row
             @media (max-width: 480px) {
                 gap: 12px;
                 margin-top: 20px;

                 .site-button {
                     max-width: calc(50% - 6px);
                     height: 48px;
                     font-size: 14px;
                 }
             }

             @media (max-width: 360px) {
                 gap: 8px;

                 .site-button {
                     max-width: calc(50% - 4px);
                     height: 44px;
                     font-size: 13px;
                 }
             }
         }

         // Keyframe animations for enhanced interactivity
         @keyframes subtle-shake {

             0%,
             100% {
                 transform: translateX(0);
             }

             25% {
                 transform: translateX(-2px);
             }

             75% {
                 transform: translateX(2px);
             }
         }

         @keyframes next-pulse {

             0%,
             100% {
                 transform: scale(1);
                 box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
             }

             50% {
                 transform: scale(1.05);
                 box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
             }
         }

     }
 }