 .add-documents-page {
     --background: white;
     height: 100%;

     .add-documents-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 160px);
         padding: 0px 20px 10px 20px !important;
         overflow-y: auto;

         .shipment-detail-container {
             display: flex;
             flex-direction: column;
             justify-content: space-between;
             width: 100%;
             gap: 5px;

             .shipment-detail {
                 display: flex;
                 justify-content: space-between;
             }

             .shipment-label {
                 font-size: 13px;
             }

             .shipment-value {
                 font-size: 14px;
                 font-weight: 600;
             }
         }

         .form-container {
             min-height: 250px;
         }

         .browse-file-button {
             --background: #FFEA00 !important;
             --background-activated: #FFEA00 !important;
             --color: black !important;
             /* --box-shadow: 0px 10px 20px rgba(58, 255, 223, 0.31); */
             min-height: 48px;
             --border-radius: 22px;
             text-transform: capitalize;
             color: black !important;
             font-weight: 600;
             font-size: 16px;
             letter-spacing: 0.5px;
             overflow: unset !important;

             // Ensure text and span are visible
             span {
                 color: black !important;
                 font-weight: 600;
                 font-size: 16px;
                 display: block;
                 text-transform: capitalize;
                 letter-spacing: 0.5px;
             }

             ion-label {
                 color: black !important;
             }
         }

         // Enhanced interactive buttons with ripple effects - 50% width each in row
         .shippment-btn-container {
             display: flex;
             flex-direction: row;
             justify-content: space-between;
             align-items: center;
             gap: 16px;
             margin-top: 30px;
             padding: 0;
             width: 100%;

             .site-button {
                 flex: 1;
                 max-width: calc(50% - 8px);
                 height: 54px;
                 border-radius: 16px;
                 font-weight: 600;
                 font-size: 16px;
                 text-transform: none;
                 box-shadow: none !important;
                 --box-shadow: none !important;
                 transition: all 0.3s ease;
                 position: relative;
                 overflow: hidden;
                 user-select: none;

                 &:hover {
                     // Remove complex transforms
                 }

                 &:active {
                     // Remove complex transforms
                 }

                 &.ship-cancel-btn {
                     // Simple cancel button matching app design
                     background: #ffffff;
                     --background-activated: #ffffff !important;
                     color: #6c757d;
                     border: 1px solid #dee2e6;
                     text-transform: uppercase;
                     font-weight: 600;
                     transition: all 0.3s ease;

                     &:hover {
                         background: #f8f9fa;
                         color: #495057;
                         border-color: #adb5bd;
                     }

                     &:active {
                         background: #e9ecef;
                         color: #495057;
                         border-color: #adb5bd;
                     }

                     ion-ripple-effect {
                         color: rgba(108, 117, 125, 0.2);
                     }



                     // Subtle shake animation on focus
                     &:focus {
                         animation: subtle-shake 0.5s ease-in-out;
                     }
                 }

                 &.ship-submit-btn {
                     // Enhanced interactive Submit button with white text
                     position: relative;
                     overflow: hidden;
                     text-transform: uppercase;
                     background: #2c3e50;
                     color: #ffffff;

                     transition: all 0.3s ease;

                     &:hover {
                         background: #1a252f;
                         color: #ffffff;
                     }

                     &:active {
                         background: #2c3e50;
                         color: #ffffff;
                     }

                     // Enhanced ripple effect for Submit button
                     ion-ripple-effect {
                         color: rgba(255, 255, 255, 0.3);
                         z-index: 2;
                     }

                     // Animated border effect
                     &::before {
                         content: '';
                         position: absolute;
                         top: 0;
                         left: -100%;
                         width: 100%;
                         height: 100%;
                         background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                         transition: left 0.5s ease;
                         z-index: 1;
                     }

                     &:hover::before {
                         left: 100%;
                     }

                     // Subtle pulse animation on focus
                     &:focus {
                         animation: submit-pulse 0.6s ease-in-out;
                     }

                     // Enhanced text styling
                     span {
                         font-weight: 700 !important;
                         letter-spacing: 0.8px;
                         text-transform: uppercase;
                         position: relative;
                         z-index: 3;
                         transition: all 0.3s ease;
                         text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                     }

                     &:hover span {
                         letter-spacing: 1px;
                         transform: scale(1.05);
                     }
                 }

                 // Ensure ripple effect is visible
                 ion-ripple-effect {
                     z-index: 1;
                     pointer-events: none;
                 }
             }

             // Responsive design - keep buttons in row
             @media (max-width: 480px) {
                 gap: 12px;
                 margin-top: 20px;

                 .site-button {
                     max-width: calc(50% - 6px);
                     height: 48px;
                     font-size: 14px;
                 }
             }

             @media (max-width: 360px) {
                 gap: 8px;

                 .site-button {
                     max-width: calc(50% - 4px);
                     height: 44px;
                     font-size: 13px;
                 }
             }
         }

         // Bold cancel button text with enhanced styling
         .cancel-text {
             font-weight: 700 !important;
             letter-spacing: 0.8px;
             text-transform: uppercase;
             position: relative;
             z-index: 3;
             transition: all 0.3s ease;
             text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

             // Subtle animation on hover
             .ship-cancel-btn:hover & {
                 letter-spacing: 1px;
                 transform: scale(1.05);
             }
         }

         // Keyframe animations for enhanced interactivity
         @keyframes subtle-shake {

             0%,
             100% {
                 transform: translateX(0);
             }

             25% {
                 transform: translateX(-2px);
             }

             75% {
                 transform: translateX(2px);
             }
         }

         @keyframes submit-pulse {

             0%,
             100% {
                 transform: scale(1);
                 box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
             }

             50% {
                 transform: scale(1.05);
                 box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
             }
         }
     }
 }