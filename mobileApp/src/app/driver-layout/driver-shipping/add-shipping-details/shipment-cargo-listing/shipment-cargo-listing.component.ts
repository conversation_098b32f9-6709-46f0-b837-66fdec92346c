import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AlertController, NavController } from '@ionic/angular';
import { SpecialRequestStates } from 'src/modals/cargoDetail';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-shipment-cargo-listing',
  templateUrl: './shipment-cargo-listing.component.html',
  styleUrls: ['./shipment-cargo-listing.component.scss'],
  standalone: false
})
export class ShipmentCargoListingComponent implements OnInit {

  filter: any;
  currentPage = 4;
  shipmentData: any;
  shippingList: Array<any> = new Array<any>();

  searchQuery: string = '';  // Variable to hold the search query
  filteredCargoList: any[] = [];
  backUrl!: string;
  specialRequestData: SpecialRequestStates = new SpecialRequestStates();
  calculationDetails: {
    totalItems: number | null;
    totalWeight: number | null;
    totalVolume: number | null;
    grandTotal: number | null;
  } = {
      totalItems: null,
      totalWeight: null,
      totalVolume: null,
      grandTotal: null,
    };
  shippingDetails: any;
  user: any;
  hasShipmentDetailResponse: boolean = false;

  constructor(private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private readonly localStorageService: LocalStorageService,
    public commonService: CommonService,
    private readonly route: ActivatedRoute,
    public readonly authService: AuthService,
    private alertController: AlertController,
  ) { }

  ngOnInit() { }

  ionViewDidLeave() {
    this.shippingList = [];
    this.filteredCargoList = [];
    this.searchQuery = '';
  }

  ionViewWillEnter() {
    this.shippingList = [];
    this.filteredCargoList = [];
    this.searchQuery = '';

    this.user = this.authService.getUser();

    this.filter = {} as any;
    this.filter.offset = 1;
    this.filter.tabType = 'PENDING'; // default is PENDING
    if (this.authService.isDriver()) {
      this.backUrl = '/portal/shipping';
    }
    else {
      this.backUrl = '/client/portal/shipping';
    }
    this.route.queryParams.subscribe(params => {
      this.shipmentData = params['shipmentData'] || null;
    });

    this.getCargoList();
    this.getShipmentDetailsById();
  }

  getCargoList() {
    if (!this.shipmentData) {
      this.toastService.show('Shipment data not found');
      return;
    }

    const payload = {
      filtering: {
        shipmentId: this.shipmentData
      }
    };

    this.loadingService.show();

    const apiCall = this.authService.isCustomer() ?
      this.dataService.getCustomerCargoList(payload) :
      this.dataService.getCargoList(payload)

    apiCall.subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const data = response.data || [];
        this.shippingList = data;
        this.filteredCargoList = [...this.shippingList];
      },
      error: (error) => {
        this.loadingService.hide();
        this.shippingList = [];
        this.filteredCargoList = [];
        this.toastService.show(error.message || 'An error occurred while loading cargo list');
      }
    });
  }

  getShipmentDetailsById() {
    this.hasShipmentDetailResponse = false;
    this.loadingService.show();
    this.dataService.getShipmentById(this.shipmentData).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.hasShipmentDetailResponse = true;

        const data = response.data;
        this.shippingDetails = data;
        this.localStorageService.setObject("OTHER_SHIPMENT_DETAILS", data);
      },
      error: (error) => {
        this.hasShipmentDetailResponse = false;
        this.loadingService.hide();
        this.toastService.show(error.message);
      }
    });
  }

  onChangeStatusTab(status: string) {
    this.filter.tabType = status;
  }

  setPage(page: number) {
    this.currentPage = page;
  }

  goToPreviousPage() {
    if (this.currentPage > 1) {
      this.setPage(this.currentPage - 1);
    }
  }

  goToNextPage() {
    if (this.currentPage < 7) {
      this.setPage(this.currentPage + 1);
    }
  }

  searchCargoList() {
    this.filterCargoList();
  }

  filterCargoList() {
    if (this.searchQuery.trim()) {
      const query = this.searchQuery.toLowerCase();
      this.filteredCargoList = this.shippingList.filter(cargo =>
        cargo.cargoType?.toLowerCase().includes(query) ||
        cargo.description?.toLowerCase().includes(query) ||
        cargo.rateType?.toLowerCase().includes(query) ||
        cargo.weightType?.toLowerCase().includes(query)
      );
    } else {
      this.filteredCargoList = [...this.shippingList];
    }
  }

  add() {
    this.localStorageService.remove("CARGO_DETAILS");
    this.localStorageService.remove("CARGO_MODE");
    if (this.authService.isDriver()) {
      this.navController.navigateForward('/portal/add/cargo/details', {
        queryParams: {
          shipmentData: this.shipmentData
        }, animated: true
      });
      return;
    }
    this.navController.navigateForward('/client/portal/add/cargo/details', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

  editCargoItem(item: any) {
    this.localStorageService.setObject("CARGO_DETAILS", item);
    this.localStorageService.set("CARGO_MODE", "edit");
    if (this.authService.isDriver()) {
      this.navController.navigateForward('/portal/add/cargo/details', {
        queryParams: {
          shipmentData: this.shipmentData
        }, animated: true
      });
      return;
    }
    this.navController.navigateForward('/client/portal/add/cargo/details', {
      queryParams: {
        shipmentData: this.shipmentData
      }, animated: true
    });
  }

  specialRequest() {
    if (this.shippingList.length <= 0) {
      this.toastService.show("No cargo found. Please add a new cargo to continue.")
      return;
    }
    if (this.authService.isDriver()) {
      this.goToDriverSpecialRequest();
      return;
    }
    if (this.authService.isCustomer()) {
      this.goToCustomerSpecialRequest();
      return;
    }
  }

  goToDriverSpecialRequest() {
    this.loadingService.show();
    this.dataService.getShipmentById(this.shipmentData).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const data = response.data;
        this.saveSpecialRequestDetails(data);
        this.saveDocuments(data);

        this.navController.navigateForward('/portal/special/request', {
          queryParams: {
            shipmentData: this.shipmentData
          }, animated: true
        });
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message);
      }
    });
  }

  goToCustomerSpecialRequest() {
    this.loadingService.show();
    this.dataService.getCustomerShipmentById(this.shipmentData).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        const data = response.data;
        this.saveSpecialRequestDetails(data);
        this.saveDocuments(data);
        this.saveCalculationDetails(data);

        this.navController.navigateForward('/client/portal/special/request', {
          queryParams: {
            shipmentData: this.shipmentData
          }, animated: true
        });
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message);
      }
    });
  }

  saveSpecialRequestDetails(data: any) {
    this.specialRequestData.isOversize = data.isOversize;
    this.specialRequestData.isRushRequest = data.isRushRequest;
    this.specialRequestData.isEnclosed = data.isEnclosed;
    this.specialRequestData.isFragile = data.isFragile;
    this.specialRequestData.isPerishable = data.isPerishable;
    this.specialRequestData.isDangerousGoods = data.isDangerousGoods;

    this.localStorageService.setObject("SPECIAL_REQUEST", this.specialRequestData);
  }

  saveCalculationDetails(data: any) {
    this.calculationDetails.totalItems = data.totalItems;
    this.calculationDetails.totalWeight = data.totalWeight;
    this.calculationDetails.totalVolume = data.totalVolume;
    this.calculationDetails.grandTotal = data.grandTotal;

    this.localStorageService.setObject("SHIPPING_CALCULATION_DETAILS", this.calculationDetails);
  }

  saveDocuments(data: any) {
    this.localStorageService.setObject("SHIPPING_DOCUMENTS", data.documents);
  }

  async deleteCargoItem(item: any) {
    const alert = await this.alertController.create({
      header: 'Confirm Delete',
      message: `Are you sure you want to delete this cargo?`,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel'
        },
        {
          text: 'Delete',
          role: 'destructive',
          handler: () => {
            this.performDelete(item.id);
          }
        }
      ]
    });
    await alert.present();
  }

  async performDelete(cargoId: string) {
    this.loadingService.show();

    const apiCall = (this.authService.isCustomer()
      ? this.dataService.deleteCustomerShipmentCargoItem(cargoId)
      : this.dataService.deleteShipmentCargoItem(cargoId)
    )
    apiCall.subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        this.filteredCargoList = this.filteredCargoList.filter(r => r.id !== cargoId);
        this.toastService.show('Cargo deleted successfully!');
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

}
