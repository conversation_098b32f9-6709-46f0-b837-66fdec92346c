<ion-content class="shipment-cargo-page">
  <app-customer-header [innerPage]="true" [headingText]="'Cargo Details'" [rightAction]="false"
    [backUrl]="backUrl" [hideBellIcon]="true"></app-customer-header>

  <div class="shipment-detail-container margin-top-20" *ngIf="hasShipmentDetailResponse">
    <div class="shipment-detail">
      <span class="shipment-label">Ref Id</span>
      <span class="shipment-value">{{ shippingDetails?.refID }}</span>
    </div>
    <div class="shipment-detail">
      <span class="shipment-label">Customer</span>
      <span class="shipment-value">
        {{
        authService.isDriver()
        ? (shippingDetails?.customerUserDetail?.fullName || 'N/A')
        : (user?.firstName + " " + user?.lastName || 'N/A')
        }}
      </span>
    </div>
    <div class="shipment-detail" *ngIf="authService.isDriver() && shippingDetails?.contactPersonPhone">
      <span class="shipment-label">Customer Phone</span>
      <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(shippingDetails?.contactPersonPhone)
        }}</span>
    </div>
    <div class="shipment-detail" *ngIf="authService.isCustomer() && user?.phoneNumber">
      <span class="shipment-label">Customer Phone</span>
      <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(user?.phoneNumber)
        }}</span>
    </div>
  </div>

  <div class="fixed-search common-page-search">
    <div class="padding-top">
      <ion-item class="site-form-control" lines="none">
        <i-feather class="map-pin-icon start-icon" name="Search" slot="start"></i-feather>
        <ion-input label="Search Here..." labelPlacement="floating" name="searchHere" [(ngModel)]="searchQuery"
          (ionInput)="searchCargoList()" [debounce]="500"></ion-input>
      </ion-item>
    </div>
    <div class="add-shipping-container" (click)="add()">
      <ion-icon class="add-shipping-icon" src="assets/images/svg/black-add-icon.svg" slot="start"></ion-icon>
      <span class="add-shipping-text">ADD</span>
    </div>
  </div>

  <div class="shipment-cargo-body-section">

    <div class="not-found-container" *ngIf="filteredCargoList.length <= 0">
      <ion-icon class="not-found-icon" src="/assets/images/svg/cargo-icon.svg"></ion-icon>
      <span class="not-found-text">No Cargo Found</span>
    </div>

    <div class="shipping-list-wrapper">
      <div class="shipping-card" *ngFor="let item of filteredCargoList"
        [ngClass]="{ active: item.status === 'ACTIVE' }">
        <div class="shipping-card-inner">
          <div class="left-section">
            <div class="cargo-info">
              <ion-icon class="cargo-icon" src="/assets/images/svg/cargo-icon.svg"></ion-icon>
              <div>
                <div class="label">{{ commonService.formatTextAllCapital(item.cargoType) }}</div>
                <div class="value">{{ commonService.formatText(item.rateType) }}</div>
              </div>
            </div>

            <div class="shipping-description margin-top-20">
              <div class="label">Description</div>
              <div class="value">{{ item.description }}</div>
            </div>

            <div class="shipping-details">
              <div>
                <div class="label">Volume</div>
                <div class="value">{{ item.volume || 0 }}</div>
              </div>
              <div>
                <div class="label">Weight (IN LBS)</div>
                <div class="value">{{ item.weight }}</div>
              </div>
              <div>
                <div class="label">Quantity</div>
                <div class="value">{{ item.quantity }}</div>
              </div>
            </div>
          </div>

          <div class="right-section">
            <div class="shipping-date">{{ commonService.formatDisplayDate(item.createdOn) }}</div>
            <div class="card-actions">
              <ion-icon class="edit-icon" src="/assets/images/svg/edit-icon.svg"
                (click)="editCargoItem(item)"></ion-icon>
              <ion-icon *ngIf="item.isDeletionAllowed" class="delete-icon" src="/assets/images/svg/delete-icon.svg"
                (click)="deleteCargoItem(item)"></ion-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="pagination-wrapper" *ngIf="filteredCargoList.length > 0">
        <img class="arrow-icon arrow-image" src="/assets/images/icons/left-arrow.png" (click)="goToPreviousPage()" />
        <div class="page-number-container">
          <div class="page-number" *ngFor="let page of [1, 2, 3, 4, 5, 6, 7]" [class.active]="page === currentPage"
            (click)="setPage(page)">
            {{ page }}
          </div>
        </div>
        <ion-icon class="arrow-icon" src="/assets/images/svg/right-arrow.svg" (click)="goToNextPage()"></ion-icon>
      </div> -->
    </div>
  </div>

  <div class="fixed-next-button">
    <ion-button class="site-button next-button" expand="full" shape="round" type="submit" (click)="specialRequest()">
      <span>Next</span>
    </ion-button>
  </div>

</ion-content>