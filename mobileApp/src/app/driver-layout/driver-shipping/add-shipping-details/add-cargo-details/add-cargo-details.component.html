<ion-content class="add-cargo-detail-page">
  <app-customer-header [innerPage]="true" [headingText]="'Cargo Details'" [rightAction]="false"
    [backUrl]="backUrl" [hideBellIcon]="true"></app-customer-header>

  <div class="add-cargo-detail-body-section">

    <div class="shipment-detail-container margin-top-20">
      <div class="shipment-detail">
        <span class="shipment-label">Ref Id</span>
        <span class="shipment-value">{{ shippingDetails?.refID }}</span>
      </div>
      <div class="shipment-detail">
        <span class="shipment-label">Customer</span>
        <span class="shipment-value">
          {{
          authService.isDriver()
          ? (shippingDetails?.customerUserDetail?.fullName || 'N/A')
          : (user?.firstName + " " + user?.lastName || 'N/A')
          }}
        </span>
      </div>
      <div class="shipment-detail" *ngIf="authService.isDriver() && shippingDetails?.contactPersonPhone">
        <span class="shipment-label">Customer Phone</span>
        <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(shippingDetails?.contactPersonPhone)
          }}</span>
      </div>
      <div class="shipment-detail" *ngIf="authService.isCustomer() && user?.phoneNumber">
        <span class="shipment-label">Customer Phone</span>
        <span class="shipment-value">+1 {{ commonService.formatPhoneForDisplay(user?.phoneNumber)
          }}</span>
      </div>
    </div>

    <div class="margin-top-20">
      <span class="info-text">Cargo Details</span>
    </div>
    <div class="form-container">
      <form class="custom-form" #addCargoDetailsForm="ngForm" novalidate>

        <div class="margin-top-20 margin-bottom-10">
          <ng-container *ngIf="hasCargoDetails">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': (desc.invalid || isDescriptionOnlySpaces) && onClickValidation}">
              <ion-textarea label="Description" labelPlacement="floating" name="desc" #desc="ngModel"
                [(ngModel)]="addCargoDetails.description" required>
              </ion-textarea>
            </ion-item>
            <app-validation-message *ngIf="desc.invalid && onClickValidation" [field]="desc"
              [onClickValidation]="onClickValidation"
              [customPatternMessage]="'Only alphabetic characters are allowed.'">
            </app-validation-message>

            <!-- Show custom spaces-only error only if field is valid but input is spaces -->
            <div class="error-message margin-top-5"
              *ngIf="!desc.invalid && isDescriptionOnlySpaces && onClickValidation">
              Description cannot be empty or just spaces.
            </div>
          </ng-container>
        </div>

        <div class="margin-top-10">
          <ng-container *ngIf="hasCargoDetails">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': types.invalid && onClickValidation}">
              <ion-select #types="ngModel" [(ngModel)]="addCargoDetails.cargoType" interface="action-sheet"
                required="required" name="types" label="Cargo Type" labelPlacement="floating">
                <ion-select-option *ngFor="let type of cargoTypes" [value]="type.value">
                  {{ type.display }}
                </ion-select-option>
              </ion-select>
              <ion-icon slot="end" class="down-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
            </ion-item>
            <app-validation-message [field]="types" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </ng-container>
        </div>

        <div class="margin-top-10">
          <ng-container *ngIf="hasCargoDetails">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': weighttypes.invalid && onClickValidation}">
              <ion-select #weighttypes="ngModel" [(ngModel)]="addCargoDetails.weightType" interface="action-sheet"
                required="required" name="weighttypes" (ionChange)="onWeightChange()" label="Weight Type"
                labelPlacement="floating">
                <ion-select-option *ngFor="let type of weightTypes" [value]="type.value">
                  {{ type.display }}
                </ion-select-option>
              </ion-select>
              <ion-icon slot="end" class="down-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
            </ion-item>
            <app-validation-message [field]="weighttypes" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </ng-container>
        </div>

        <!-- <div class="margin-top-10">
          <ng-container *ngIf="hasCargoDetails">
            <ion-item class="site-form-control" lines="none"
              [ngClass]="{'is-invalid': ratetypes.invalid && onClickValidation}">
              <ion-select #ratetypes="ngModel" [(ngModel)]="addCargoDetails.rateType" interface="action-sheet"
                required="required" name="ratetypes" label="Rate Type" labelPlacement="floating">
                <ion-select-option *ngFor="let type of rateTypes" [value]="type.value">
                  {{ type.display }}
                </ion-select-option>
              </ion-select>
              <ion-icon slot="end" class="down-arrow-icon" [src]="'/assets/images/svg/down-arrow.svg'"></ion-icon>
            </ion-item>
            <app-validation-message [field]="ratetypes" [onClickValidation]="onClickValidation">
            </app-validation-message>
          </ng-container>
        </div> -->

        <div class="common-fields-container">
          <div class="field-container small-field">
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="site-form-control" lines="none"
                [ngClass]="{'is-invalid': weight.invalid && onClickValidation}">
                <ion-input name="weight" #weight="ngModel" [(ngModel)]="addCargoDetails.weight" required="required"
                  mode="md" type="number" label="Weight" labelPlacement="floating" (ionInput)="onWeightChange()">
                </ion-input>
              </ion-item>
              <app-validation-message [field]="weight" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </ng-container>
          </div>

          <div class="field-container large-field">
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="site-form-control auto-cal-padding" lines="none"
                [ngClass]="{'is-invalid': calculate.invalid && onClickValidation}">
                <ion-input name="calculate" #calculate="ngModel" [(ngModel)]="addCargoDetails.weightInPounds"
                  required="required" mode="md" label="Weight LBS" labelPlacement="floating" readonly>
                </ion-input>
              </ion-item>
              <app-validation-message [field]="calculate" [onClickValidation]="onClickValidation">
              </app-validation-message>
            </ng-container>
          </div>
        </div>

        <div class="dimension-fields-container margin-top-10" *ngIf="authService.isDriver()">
          <div class="dimension-row">
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item" lines="none" [ngClass]="{'is-invalid': length.invalid && onClickValidation}">
                <ion-input #length="ngModel" name="length" [(ngModel)]="addCargoDetails.length" required label="L inch"
                  labelPlacement="floating" mode="md" type="number" (ngModelChange)="calculateVolume()">
                </ion-input>
              </ion-item>
            </ng-container>
            <span class="dim-symbol">×</span>
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item" lines="none" [ngClass]="{'is-invalid': width.invalid && onClickValidation}">
                <ion-input #width="ngModel" name="width" [(ngModel)]="addCargoDetails.width" required label="W inch"
                  labelPlacement="floating" mode="md" type="number" (ngModelChange)="calculateVolume()">
                </ion-input>
              </ion-item>
            </ng-container>
            <span class="dim-symbol">×</span>
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item" lines="none" [ngClass]="{'is-invalid': height.invalid && onClickValidation}">
                <ion-input #height="ngModel" name="height" [(ngModel)]="addCargoDetails.height" required label="H inch"
                  labelPlacement="floating" mode="md" type="number" (ngModelChange)="calculateVolume()">
                </ion-input>
              </ion-item>
            </ng-container>
            <span class="dim-symbol">=</span>
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item volume" lines="none"
                [ngClass]="{'is-invalid': volume.invalid && onClickValidation}">
                <ion-input required #volume="ngModel" name="volume" [(ngModel)]="addCargoDetails.volume" label="Volume"
                  labelPlacement="floating" mode="md" readonly>
                </ion-input>
              </ion-item>
            </ng-container>
          </div>
        </div>

        <div class="dimension-fields-container margin-top-10" *ngIf="authService.isCustomer()">
          <div class="dimension-row">
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item" lines="none" [ngClass]="{'is-invalid': length.invalid && onClickValidation}">
                <ion-input #length="ngModel" name="length" [(ngModel)]="addCargoDetails.length" label="L inch"
                  labelPlacement="floating" mode="md" type="number" (ngModelChange)="calculateVolume()">
                </ion-input>
              </ion-item>
            </ng-container>
            <span class="dim-symbol">×</span>
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item" lines="none" [ngClass]="{'is-invalid': width.invalid && onClickValidation}">
                <ion-input #width="ngModel" name="width" [(ngModel)]="addCargoDetails.width" label="W inch"
                  labelPlacement="floating" mode="md" type="number" (ngModelChange)="calculateVolume()">
                </ion-input>
              </ion-item>
            </ng-container>
            <span class="dim-symbol">×</span>
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item" lines="none" [ngClass]="{'is-invalid': height.invalid && onClickValidation}">
                <ion-input #height="ngModel" name="height" [(ngModel)]="addCargoDetails.height" label="H inch"
                  labelPlacement="floating" mode="md" type="number" (ngModelChange)="calculateVolume()">
                </ion-input>
              </ion-item>
            </ng-container>
            <span class="dim-symbol">=</span>
            <ng-container *ngIf="hasCargoDetails">
              <ion-item class="dim-item volume" lines="none"
                [ngClass]="{'is-invalid': volume.invalid && onClickValidation}">
                <ion-input #volume="ngModel" name="volume" [(ngModel)]="addCargoDetails.volume" label="Volume"
                  labelPlacement="floating" mode="md" readonly>
                </ion-input>
              </ion-item>
            </ng-container>
          </div>
        </div>

        <div class="margin-top-10 quantity-field-container">
          <ion-item class="site-form-control quantity-input-wrapper" lines="none"
            [ngClass]="{'is-invalid': quantity.invalid && onClickValidation}">

            <div class="quantity-left-group" slot="start">
              <span class="quantity-label">Quantity</span>
              <div class="vertical-separator"></div>
              <span class="quantity-display">{{ addCargoDetails.quantity }}</span>
              <!-- Hidden input for validation -->
              <ion-input type="number" name="quantity" #quantity="ngModel" [(ngModel)]="addCargoDetails.quantity"
                required min="1" style="display: none">
              </ion-input>
            </div>

            <div class="quantity-controls" slot="end">
              <ion-icon class="control-icon up" src="/assets/images/svg/up-icon.svg"
                (click)="incrementQuantity()"></ion-icon>
              <ion-icon class="control-icon down" src="/assets/images/svg/down-icon.svg"
                (click)="decrementQuantity()"></ion-icon>
            </div>
          </ion-item>
          <app-validation-message [field]="quantity" [onClickValidation]="onClickValidation"></app-validation-message>
        </div>

        <div class="shippment-btn-container">
          <ion-button class="margin-top-20 site-button ship-cancel-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="cancel()">
            <span class="cancel-text">Back</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
          <ion-button class="margin-top-20 site-button ship-submit-btn interactive-button" expand="full" shape="round"
            type="submit" (click)="submitProfile(addCargoDetailsForm.form)">
            <span>Submit</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
        </div>

      </form>
    </div>

  </div>
</ion-content>