<ion-content class="view-document-page">
  <app-customer-header [innerPage]="true" [headingText]="'Documents'" [rightAction]="false" [backUrl]="backUrl"
    [hideBellIcon]="true"></app-customer-header>

  <div class="view-document-body-section">

    <!-- <div class="margin-top-20" *ngIf="popImages.length > 0">
      <span class="info-text">POP Images</span>
    </div>

    <div class="view-document-container margin-top-20" *ngIf="popImages.length > 0">
      <img *ngFor="let img of popImages" class="view-document" [src]="img.secureUrl" alt="{{img.originalName}}"
        (click)="viewImage(img.secureUrl,img.originalName)" />
    </div> -->

    <div class="margin-top-20" *ngIf="podImagesLoaded && podImages.length > 0">
      <span class="info-text">POD Images</span>
    </div>

    <div class="view-document-container margin-top-20" *ngIf="podImagesLoaded && podImages.length > 0">
      <img *ngFor="let img of podImages" class="view-document" [src]="img.secureUrl" alt="{{img.originalName}}"
        (click)="viewImage(img.secureUrl,img.originalName)" />
    </div>

    <div class="margin-top-20" *ngIf="podImagesLoaded && signatureImage">
      <span class="info-text">Signatures</span>
    </div>

    <div class="signature-display-section margin-top-20" *ngIf="podImagesLoaded && signatureImage">
      <div class="signature-container">
        <img [src]="signatureImage" alt="POD Signature" class="signature-image" />
      </div>
    </div>

  </div>
</ion-content>