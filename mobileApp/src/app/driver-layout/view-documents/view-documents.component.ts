import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ModalController, NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { FullImageComponent } from 'src/shared/full-image/full-image.component';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-view-documents',
  templateUrl: './view-documents.component.html',
  styleUrls: ['./view-documents.component.scss'],
  standalone: false
})
export class ViewDocumentsComponent implements OnInit, OnDestroy {

  private subscription: Subscription = new Subscription();
  shipmentId: string = '';
  backUrl!: string;
  popImages: Array<any> = new Array<any>();
  podImages: Array<any> = new Array<any>();
  podImagesLoaded: boolean = false;
  signatureImage: string | null = null;

  constructor(private readonly navController: NavController,
    private readonly dataService: DataService,
    private readonly loadingService: LoadingService,
    private readonly toastService: ToastService,
    private readonly route: ActivatedRoute,
    public readonly commonService: CommonService,
    public readonly authService: AuthService,
    private modalCtrl: ModalController
  ) {

  }

  ngOnInit() {
  }

  ionViewWillEnter() {
    this.subscription.add(
      this.route.queryParams.subscribe(params => {
        this.shipmentId = params['shipmentId'] || '';
      })
    );

    if (this.authService.isDriver()) {
      this.backUrl = '/portal/shipping';
    } else {
      this.backUrl = '/client/portal/shipping';
    }

    //  this.getPopDocuments();
    this.getPodDocuments();
  }

  getPopDocuments() {
    this.loadingService.show();
    this.subscription.add(
      this.dataService.getPopImages(this.shipmentId).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data;

          this.popImages = data.popImages || [];
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
    );
  }

  getPodDocuments() {
    this.podImagesLoaded = false;
    this.loadingService.show();

    this.subscription.add(
      this.dataService.getPodImages(this.shipmentId).subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data;

          this.podImages = data.podImages || [];
          this.signatureImage = data.podSignatures || null;
          this.podImagesLoaded = true;
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
          this.podImagesLoaded = true;
        }
      })
    );
  }

  async viewImage(imageUrl: string, fileName?: string, showDelete: boolean = false) {
    const modal = await this.modalCtrl.create({
      component: FullImageComponent,
      componentProps: { imageUrl, fileName, showDelete },
      cssClass: 'full-image-modal'
    });
    await modal.present();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

}
