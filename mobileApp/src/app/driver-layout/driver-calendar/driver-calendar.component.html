<ion-content class="calendar-tab-page">
  <app-customer-header [innerPage]="true" [headingText]="'Calendar view'" [rightAction]="false" [hideBellIcon]="true"
    [backUrl]="'/portal/dashboard'"></app-customer-header>

  <div class="calendar-tab-body-section">

    <div class="calendar-header">
      <h2>{{ currentDate | date: 'MMMM, y' }}</h2>
      <div class="calendar-icon-header-container">
        <div class="calendar-icon-header">
          <ion-icon name="chevron-back-outline" (click)="goToPreviousMonth()"></ion-icon>
        </div>
        <div class="calendar-icon-header">
          <ion-icon name="chevron-forward-outline" (click)="goToNextMonth()"></ion-icon>
        </div>
        <!-- Debug button to show API response -->
        <!-- <div class="calendar-icon-header debug-button" (click)="showApiResponse()">
          <ion-icon name="bug-outline"></ion-icon>
        </div> -->
      </div>
    </div>

    <div class="calendar-grid">
      <div class="day-header" *ngFor="let day of ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']">
        {{ day }}
      </div>

      <div class="day-cell" *ngFor="let week of weeks">
        <ng-container *ngFor="let day of week">
          <div class="date-box" [class.inactive]="!isSameMonth(day)" [class.selected]="isSelected(day)"
            (click)="selectDate(day)">
            <div class="date">{{ day.getDate() }}</div>
            <div class="status-dots">
              <span *ngFor="let status of getStatus(day)" [class]="status + '-dot'"></span>
            </div>
          </div>
        </ng-container>
      </div>
    </div>

    <div class="shipment-schedule-container">
      <div class="schedule-header">
        <span class="schedule-text margin-top-25 margin-left-5">Schedule</span>
        <!-- <div class="refresh-button" (click)="refreshSchedule()">
          <ion-icon name="refresh-outline"></ion-icon>
        </div> -->
      </div>
      <div class="shipment-list margin-top-20" *ngIf="selectedDateShipments.length > 0; else noShipments">
        <div class="shipment-item" *ngFor="let shipment of selectedDateShipments"
          (click)="openShipmentDetails(shipment)">
          <div class="icon-circle" [style.background-color]="getIconColor(shipment.statusColor)">
            <ion-icon name="ellipse"></ion-icon>
          </div>
          <div class="shipment-details">
            <div class="shipment-title-section">
              <span class="shipment-title">{{ shipment.status }}</span>
              <span class="etd-date">{{ commonService.formatDisplayDate(shipment.etd) }}</span>
            </div>
            <div class="shipment-time">{{ shipment.time }}</div>
            <div class="shipment-status">{{ shipment.customerName }}</div>
          </div>
        </div>
      </div>

      <ng-template #noShipments>
        <div class="no-shipments-message margin-top-20">
          <p>No shipments scheduled for {{ selectedDate | date: 'MMM dd, yyyy' }}</p>
        </div>
      </ng-template>
    </div>

  </div>
</ion-content>

<!-- Shipment Details Modal -->
<div class="modal-overlay" *ngIf="isModalOpen" (click)="closeModal()">
  <div class="shipment-details-modal" (click)="$event.stopPropagation()">
    <div class="modal-header">
      <h3>Shipment Details</h3>
      <ion-button fill="clear" (click)="closeModal()">
        <ion-icon name="close-outline"></ion-icon>
      </ion-button>
    </div>

    <div class="modal-content" *ngIf="selectedShipment">
      <!-- Status -->
      <div class="detail-item status-item">
        <div class="detail-value status-value">{{ selectedShipment.status }}</div>
      </div>

      <!-- Ref ID -->
      <div class="detail-item">
        <div class="detail-label">Ref</div>
        <div class="detail-value">{{ selectedShipment.refId }}</div>
      </div>

      <!-- Status -->
      <!-- <div class="detail-item">
        <div class="detail-label">Status</div>
        <div class="detail-value">{{ selectedShipment.status }}</div>
      </div> -->

      <!-- Customer -->
      <div class="detail-item">
        <div class="detail-label">Customer</div>
        <div class="detail-value">{{ selectedShipment.customerName }}</div>
      </div>

      <!-- From -->
      <div class="detail-item">
        <div class="detail-label">From</div>
        <div class="detail-value">{{ selectedShipment.from }}</div>
      </div>

      <!-- To -->
      <div class="detail-item">
        <div class="detail-label">To</div>
        <div class="detail-value">{{ selectedShipment.to }}</div>
      </div>

      <!-- ETD -->
      <div class="detail-item">
        <div class="detail-label">ETD</div>
        <div class="detail-value">{{ commonService.formatDisplayDate(selectedShipment.etd) }}</div>
      </div>

      <!-- Edit Button (only for NEW or ASSIGNED status) -->
      <div class="edit-button-container" *ngIf="isShipmentEditable(selectedShipment.status)">
        <ion-button class="site-button edit-button text-capitalize" expand="full" (click)="editShipment()">
          Edit Shipment
        </ion-button>
      </div>
    </div>
  </div>
</div>