import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { CommonService } from 'src/services/common.service';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { AuthService } from 'src/shared/authservice';
import { EventService } from 'src/shared/event.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-notification-list',
  templateUrl: './notification-list.component.html',
  styleUrls: ['./notification-list.component.scss'],
  standalone: false
})
export class NotificationListComponent implements OnInit {

  user: any;
  filter: any;
  allNotificationsRead: boolean = false;
  allNotifications: Array<any> = new Array<any>();
  isMarkAllNotificationModalOpen: boolean = false;
  totalNotificationsCount: number = 0;
  backUrl!: string;

  constructor(private readonly dataService: DataService,
    private readonly authService: AuthService,
    private readonly toastService: ToastService,
    private readonly localStorageService: LocalStorageService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    public readonly commonService: CommonService,
    private readonly eventService: EventService,
    private readonly changeDetectorRef: ChangeDetectorRef
  ) {

  }

  ngOnInit() {

  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();

    if (this.authService.isDriver()) {
      this.backUrl = '/portal/dashboard';
    } else {
      this.backUrl = '/client/portal/dashboard';
    }

    this.allNotifications = new Array<any>();
    this.filter = {} as any;
    this.filter.offset = 1;

    this.myNotifications();
  }

  myNotifications() {
    const payload = {
    };
    this.loadingService.show();
    this.dataService.myNotifications(payload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();
        this.allNotifications = [...this.allNotifications, ...response.data];
      },
      error: (error: any) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  readNotification(notification: any) {
    if (notification.isRead) {
      this.handleNotificationAction(notification);
      return;
    }

    const payload = [{
      id: notification.id,
      isRead: true,
      isDeleted: true
    }];
    this.loadingService.show();
    this.dataService.readSingleNotification(payload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        this.eventService.publish({ key: 'notification:read', value: "SINGLE" });
        // Update the clicked notification's state to reflect it's read and deleted
        this.allNotifications = this.allNotifications.map(n =>
          n.id === notification.id
            ? { ...n, isRead: true, isDeleted: true }
            : n
        );

        // Now open the modal or perform the action based on notification type
        this.handleNotificationAction(notification);
      },
      error: (error: any) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      },
    });
  }

  handleNotificationAction(notification: any) {
    let queryParams: any = {
      targetId: notification.entityPkId,
      targetType: notification.entityName,
      message: notification.message,
      status: notification.status
    };

    if (notification.entityName === "SHIPMENT") {
      if (notification.status === "NEW" || notification.status === "ASSIGNED") {
        queryParams.refresh = 'true';
        queryParams.goTab = 'PENDING';
      } else if (notification.status === "IN_TRANSIT") {
        queryParams.refresh = 'true';
        queryParams.goTab = 'IN_TRANSIT_TAB';
      } else if (notification.status === "DELIVERED" || notification.status === "COMPLETED") {
        queryParams.refresh = 'true';
        queryParams.goTab = 'COMPLETED';
      }

      if (this.authService.isDriver()) {
        this.navController.navigateForward('/portal/shipping', {
          queryParams,
          animated: true
        });
      } else {
        this.navController.navigateForward('/client/portal/shipping', {
          queryParams,
          animated: true
        });
      }
    } else if (notification.entityName === "QUOTATION") {
      this.navController.navigateForward('/client/portal/quotation', {
        queryParams,
        animated: true
      });
    }

    setTimeout(() => this.changeDetectorRef.detectChanges(), 200);
  }

  closeModal() {
    this.isMarkAllNotificationModalOpen = false;
  }

  openMarkAllNotificationModal() {
    if (this.allNotifications.length <= 0) {
      // this.toastService.show('No notifications to mark as read');
      // TO DO disabled button
      return;
    }
    this.isMarkAllNotificationModalOpen = true;
  }

  onMarkAllRead() {
    this.readAllNotifications();
    this.closeModal();
  }

  readAllNotifications() {
    const payload = {
    };
    this.loadingService.show();
    this.dataService.readAllNotifications(payload)
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          this.allNotifications = this.allNotifications.map(notification => ({
            ...notification,
            isRead: true
          }));
          this.toastService.show('All notifications marked as read');
          this.eventService.publish({ key: 'notification:read', value: "ALL" });
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message || 'An error occurred');
        },
      });
  }

}
