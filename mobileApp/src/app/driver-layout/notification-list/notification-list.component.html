<ion-content class="notification-list-page">
  <app-customer-header [innerPage]="true" [headingText]="'Notification'" [isMarkAllAction]="true"
    (markAllActionCallback)="openMarkAllNotificationModal()" [backUrl]="backUrl"></app-customer-header>

  <div class="notification-list-body-section">
    <!-- Empty State -->
    <div class="no-bookings-container" *ngIf="allNotifications.length === 0">
      <div class="no-bookings-icon">
        <ion-icon src="/assets/images/svg/no-notification.svg"></ion-icon>
      </div>
      <p class="no-bookings-message">
        You're all caught up! No new notifications at the moment. Check back later for updates.
      </p>
    </div>

    <!-- Notification Cards -->
    <div *ngIf="allNotifications.length > 0">
      <div class="notification-card" *ngFor="let notification of allNotifications" [ngClass]="{
            'unread-notification': !notification.isRead,
            'read-notification': notification.isRead
          }" (click)="readNotification(notification)">
        <div class="notification-image">
          <img src="/assets/images/svg/bell-icon-with-background.svg" alt="Notification Image">
        </div>
        <div class="notification-content">
          <div class="notification-heading">{{ notification.title }}</div>
          <div class="notification-description">{{ notification.message }}</div>
        </div>
        <div class="notification-time">
          <div class="date-text">{{ commonService.formatNotificationDate(notification.createdOn).date }}</div>
          <div class="time-text">{{ commonService.formatNotificationDate(notification.createdOn).time }}</div>
        </div>
      </div>
    </div>

  </div>
</ion-content>

<ion-modal class="site-custom-popup job-invitation-popup" #markAllModal [isOpen]="isMarkAllNotificationModalOpen"
  [backdropDismiss]="false">
  <ng-template>
    <div class="site-custom-popup-container heading-setting">
      <div class="site-custom-popup-header no-padding-bottom padding-top-10">
        <i-feather name="X" (click)="closeModal()"></i-feather>
        <h1>Confirmation!</h1>
      </div>
      <div class="site-custom-popup-body popup-normal-heading ion-padding no-padding-top">
        <div class="custom-modal-notifications"><span>Are you sure you want to mark all notifications as read?</span>
        </div>
        <div class="main-modal-dismiss button-gap margin-top-20">
          <ion-button class="site-full-rounded-button primary-button notification-list-no-button" shape="round"
            type="submit" (click)="closeModal()">No</ion-button>
          <ion-button class="site-full-rounded-button primary-button yes-button" shape="round" type="submit"
            (click)="onMarkAllRead()">Yes</ion-button>
        </div>
      </div>
    </div>
  </ng-template>
</ion-modal>