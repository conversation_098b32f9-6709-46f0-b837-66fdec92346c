 .notification-list-page {
     --background: white;
     height: 100%;

     .notification-list-body-section {
         display: inline-block;
         width: 100%;
         height: calc(100vh - 140px); // Adjusted for additional dropdown
         padding: 10px 20px 10px 20px !important;
         overflow-y: auto;

         .no-bookings-container {
             //  display: flex;
             flex-direction: column;
             align-items: center;
             justify-content: center;
             text-align: center;
             color: black;
             height: 100%;
             padding: 16px;

             .no-bookings-icon {
                 font-size: 60px;
                 color: black;
                 margin-bottom: 20px;
             }

             .no-bookings-title {
                 font-size: 24px;
                 font-weight: bold;
                 margin-bottom: 8px;
                 color: black;
             }

             .no-bookings-message {
                 font-size: 16px;
                 color: black;
                 font-weight: 500;
                 margin-bottom: 20px;
             }
         }

         .notification-card {
             display: flex;
             align-items: flex-start;
             justify-content: space-between;
             padding: 11px 7px;
             border: 1px solid #ddd;
             border-radius: 8px;
             box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
             background-color: #fff;
             max-width: 400px;
             margin: 10px auto;
             font-family: Arial, sans-serif;
             transition: background-color 0.3s ease;

             &.unread-notification {
                 background-color: #e0f0ff; // Light blue for unread notifications
             }

             &.read-notification {
                 background-color: white; // Yellow for read notifications
             }
         }

         .notification-image {
             flex-shrink: 0;
             display: flex;
             justify-content: center;
             align-items: center;
             width: 30px;
             height: 50px;
             margin-right: 8px;

             img {
                 width: 100%;
                 height: 100%;
                 border-radius: 50%;
                 object-fit: cover;
             }
         }

         .notification-content {
             flex-grow: 1;
             //  margin-left: 8px;
         }

         .notification-heading {
             font-size: 14px;
             font-weight: bold;
             color: #333;
         }

         .notification-description {
             font-size: 11px;
             color: #666;
             margin-top: 4px;
             max-height: 110px;
             overflow: hidden;
             display: -webkit-box;
             -webkit-line-clamp: 2;
             -webkit-box-orient: vertical;
             transition: max-height 0.3sease-in-out;
         }

         .notification-time {
             font-size: 13px;
             color: #999;
             margin-bottom: 15px;
             white-space: nowrap;

             .date-text {
                 margin-top: 6px;
             }

             .time-text {
                 text-align: center;
                 margin-top: 3px;
             }
         }

     }
 }