import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NavController } from '@ionic/angular';
import { AuthService } from 'src/shared/authservice';
import { DataService } from 'src/services/data.service'; // Add this import for notification service
import { ToastService } from 'src/shared/toast.service'; // Handle errors
import { EventService } from 'src/shared/event.service';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { CommonService } from 'src/services/common.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-customer-header',
  templateUrl: './customer-header.component.html',
  styleUrls: ['./customer-header.component.scss'],
  standalone: false
})
export class CustomerHeaderComponent implements OnInit {
  @Input() innerPage: boolean = false;
  @Input() headingText: string = "";
  @Input() rightAction: boolean = false;
  @Input() rightActionIcon: string = "";
  @Input() logoutAction: boolean = false;
  @Input() homeAction: boolean = false;
  @Input() showBackIcon: boolean = true;
  @Input() showAddNew: boolean = false;
  @Input() addNewText: string = "Add New";
  @Input() notificationCount: number = 0; // Dynamic notification count
  @Input() isMarkAllAction: boolean = false; // New input for mark-all-read action
  @Input() hideBellIcon: boolean = false;  // New input to hide bell icon

  @Output() rightActionCallback: EventEmitter<any> = new EventEmitter<any>();
  @Output() logoutActionCallback: EventEmitter<any> = new EventEmitter<any>();
  @Output() homeActionCallback: EventEmitter<any> = new EventEmitter<any>();
  @Output() addNewCallback: EventEmitter<any> = new EventEmitter<any>();
  @Output() markAllActionCallback: EventEmitter<any> = new EventEmitter<any>(); // New output for mark-all-read action

  @Input() backUrl: string | null = null;
  @Input() profileImageUrl: string | null = null;

  user: any;
  private intervalId: any; // For periodic updates
  eventSub!: Subscription;

  constructor(private authService: AuthService,
    private navController: NavController,
    private dataService: DataService, // For fetching unread count
    private toastService: ToastService, // For showing errors
    private eventService: EventService,
    private localStorageService: LocalStorageService,
    public commonService: CommonService
  ) {

  }

  ngOnInit() {
    this.eventSub = this.eventService.event.subscribe((data: any) => {
      if (!data) {
        return;
      }
      if (data.key === "notification:read") {
        if (data.value === "ALL") {
          // TO DO ZERO
          this.notificationCount = 0;
        } else if (data.value === "SINGLE") {
          // TO DO count = count - 1
          this.notificationCount = this.notificationCount - 1;
        }
      }
      data = undefined;
    });
    this.user = this.authService.getUser();
    if (this.user) {
      this.fetchUnreadNotificationCount();
      this.startPeriodicUpdate(); // Start periodic updates
    }
  }

  ionViewWillEnter() {
    this.user = this.authService.getUser();
    if (this.user) {
      this.fetchUnreadNotificationCount();
    }
  }

  back() {
    if (this.backUrl) {
      this.navController.navigateBack(this.backUrl);
    } else {
      this.navController.back({ animated: true });
    }
  }

  onRightActionClick() {
    this.rightActionCallback.emit(null);
  }

  onLogoutActionClick() {
    this.logoutActionCallback.emit(null);
  }

  onHomeActionClick() {
    this.homeActionCallback.emit(null);
  }

  onAddNewClick() {
    this.addNewCallback.emit(null);
  }

  onMarkAllActionClick() {
    this.markAllActionCallback.emit();
  }

  fetchUnreadNotificationCount() {
    // Get current date
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0]; // "yyyy-MM-dd"

    const payload = {
      filtering: {
        currentDate: formattedDate
      }
    };
    this.dataService.getUnreadNotificationCount(payload).subscribe({
      next: (response: any) => {
        this.notificationCount = response.data?.totalCount || 0; // Update notification count
      },
      error: (error: any) => {
        this.notificationCount = 0; // Reset count in case of error
        // this.toastService.show(error.message || 'Unable to fetch notifications');
      },
    });
  }

  startPeriodicUpdate() {
    this.intervalId = setInterval(() => {
      if (this.commonService.isChangePasswordModalOpen) {
        return;
      }

      this.user = this.authService.getUser();
      if (this.user) {
        this.fetchUnreadNotificationCount();
      }
    }, 30000); // Update every 30 seconds (adjust as needed)
  }

  stopPeriodicUpdate() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  ngOnDestroy() {
    this.stopPeriodicUpdate(); // Clear interval on component destruction
    if (this.eventSub) {
      this.eventSub.unsubscribe(); // Unsubscribe from event
    }
  }

}
