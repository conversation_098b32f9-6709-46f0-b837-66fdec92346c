<div class="common-header-section">
  <img class="header-bg" src="assets/images/icons/common-header.png" alt="header background" />

  <div class="header-content">
    <div class="header-icon" *ngIf="!innerPage">
      <img src="assets/images/icons/user.png" alt="User" />
    </div>
    <div class="header-icon" *ngIf="innerPage && showBackIcon" (click)="back()">
      <i-feather name="arrow-left" class="back-icon"></i-feather>
    </div>

    <!-- Title in center -->
    <div class="header-title"> {{ headingText }} </div>

    <div class="right-actions-container">
      <!-- Add New Button -->
      <ion-button *ngIf="showAddNew" class="add-new-header-btn" fill="clear" (click)="onAddNewClick()">
        <ion-icon name="add-circle-outline"></ion-icon>
      </ion-button>

      <!-- Right Action Icon -->
      <div class="right-icon-container" *ngIf="rightAction" (click)="onRightActionClick()">
        <ion-icon class="right-icon" [name]="rightActionIcon || 'filter-outline'" *ngIf="rightActionIcon"
          slot="end"></ion-icon>
        <ion-icon class="right-icon" src="assets/images/svg/ship-filter.svg" *ngIf="!rightActionIcon"
          slot="end"></ion-icon>
      </div>

      <!-- Bell Icon - Only show if hideBellIcon is false -->
      <div class="right-icon-container" *ngIf="!rightAction && !isMarkAllAction && !hideBellIcon">
        <img src="/assets/images/svg/bell-icon-with-background.svg" alt="notification" (click)="onRightActionClick()" />
        <span class="notification-count" *ngIf="notificationCount > 0">{{ notificationCount }}</span>
      </div>

      <!-- Mark All Icon Condition -->
      <div class="right-icon-container" *ngIf="isMarkAllAction">
        <img class="notification-mark-all-image" src="/assets/images/svg/check-all.svg" alt="markAll"
          (click)="onMarkAllActionClick()" />
        <span class="notification-count" *ngIf="notificationCount > 0">{{ notificationCount }}</span>
      </div>

      <!-- Logout Action Icon -->
      <div class="right-icon-container" *ngIf="logoutAction" (click)="onLogoutActionClick()">
        <ion-icon class="logout-icon" src="assets/images/svg/logout-icon.svg" slot="end"></ion-icon>
      </div>

      <!-- Home Action Icon -->
      <div class="right-icon-container" *ngIf="homeAction" (click)="onHomeActionClick()">
        <ion-icon class="logout-icon" src="assets/images/svg/home-icon.svg" slot="end"></ion-icon>
      </div>

    </div>
  </div>
</div>