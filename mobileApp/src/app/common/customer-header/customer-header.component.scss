.common-header-section {
    //  position: fixed;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: calc(135px + env(safe-area-inset-top));

    .header-bg {
        height: calc(140px + env(safe-area-inset-top));
        width: 100%;
    }

    .header-content {
        position: absolute;
        top: calc(env(safe-area-inset-top) + 40px);
        left: 0;
        width: 100%;
        padding: 0 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        z-index: 2;
    }

    .header-icon {
        //  background-color: #fff;
        width: 43px;
        height: 43px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .back-icon {
            width: 28px;
            height: 28px;
            object-fit: cover;
        }

        .back-icon {
            stroke: #000; // Optional styling
        }
    }

    .header-title {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        font-weight: bold;
        font-size: 14px;
        color: #000;
        text-align: center;
        //  white-space: nowrap;
    }

    .right-actions-container {
        display: flex;
        align-items: center;
        gap: 10px;

        .add-new-header-btn {
            --color: #000000;
            --padding-start: 4px;
            --padding-end: 4px;
            margin: 0;
            min-width: auto;
            width: auto;

            ion-icon {
                font-size: 32px;
                margin: 0;
            }
        }

        .right-icon-container {
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .right-icon {
                background: white;
                font-size: 24px;
                border: 1px solid black;
                border-radius: 25px;
                padding: 11px;
                color: #000000;
            }

            .logout-icon {
                background: white;
                font-size: 24px;
                border-radius: 25px;
                padding: 11px;
                color: #dc3545;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                border-radius: 50%;

                &.notification-mark-all-image {
                    width: 35px;
                    height: 35px;
                }

                &.sorting-icon {
                    width: 25px;
                    height: 25px;
                }
            }

            .notification-count {
                position: absolute;
                top: -7px;
                right: 7px;
                background-color: red;
                color: white;
                font-size: 12px;
                font-weight: bold;
                width: 23px;
                height: 23px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                line-height: 20px;
            }

        }
    }
}