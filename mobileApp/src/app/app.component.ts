import { Component, ViewChild } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { SplashScreen } from '@capacitor/splash-screen';
import { Badge } from '@capawesome/capacitor-badge';
import { IonRouterOutlet, NavController, Platform } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { StatusBarService } from 'src/services/status-bar.service';
import { RestResponse } from 'src/shared/auth.model';
import { EventService } from 'src/shared/event.service';
import { FcmService } from 'src/shared/fcm.service';
import { ToastService } from 'src/shared/toast.service';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
  standalone: false,
})
export class AppComponent {
  @ViewChild(IonRouterOutlet, { static: false }) routerOutlet!: IonRouterOutlet;
  exitShown!: boolean;

  constructor(private platform: Platform,
    private navCtrl: NavController,
    private toastService: ToastService,
    private router: Router,
    private eventService: EventService,
    private dataService: DataService,
    private statusBarService: StatusBarService,
    private fcmService: FcmService,

  ) {

  }

  ngOnInit(): void {
    this.initializeApp();
    this.eventService.event.subscribe((data) => {
      if (!data) {
        return;
      }
      if (data.key === "http:forbidden") {
        this.navCtrl.navigateRoot('/account/login', { animated: true });
      }
      // if (data.key === "http:logout") {
      //   this.processLogout(data.value)
      // }
      data = undefined;
    });
  }

  private initializeApp(): void {
    this.platform.ready().then(() => {
      setTimeout(() => {
        SplashScreen.hide();
      }, 1000);

      // Configure status bar
      this.configureStatusBar();

      this.initializeBackButton();
      if (this.platform.is('cordova')) {
        this.fcmService.initializeNotificationReceiver();
        // Initialize Firebase token and request notification permissions
        this.fcmService.initializeFirebaseToken();
      }
      this.clearNotificationCount();
    });
  }

  private async configureStatusBar(): Promise<void> {
    try {
      // Initialize status bar service
      await this.statusBarService.initialize();

      // Listen for route changes to update status bar dynamically
      this.router.events.subscribe(async (event) => {
        if (event instanceof NavigationEnd) {
          await this.statusBarService.setStatusBarForRoute(event.url);
        }
      });
    } catch (error) {
    }
  }

  clearNotificationCount() {
    Badge.isSupported()
      .then(result => {
        if (result.isSupported) {
          Badge.clear();
        }
      });
  }

  initializeBackButton(): void {
    this.exitShown = false;
    // this.platform.backButton
    //   .subscribe(() => {
    this.platform.backButton.subscribeWithPriority(
      10,
      (processNext: () => void) => {

        const url = this.router.url;
        const isMainPage =
          url.startsWith('/portal/dashboard') ||
          url.startsWith('/portal/booking') ||
          url.startsWith('/portal/payment') ||
          url.startsWith('/portal/membership') ||
          url.startsWith('/portal/account') ||
          url.startsWith('/sale-portal');

        if (isMainPage) {
          if (!this.exitShown) {
            this.exitShown = true;
            this.toastService.show('Press again to exit');
            return;                       // swallow first press
          }
          (navigator as any).app.exitApp();
          return;
        }

        /* let Ionic handle normal back navigation */
        processNext();
      }
    );
  }

  processLogout(uuid: string) {
    if (!uuid) {
      return;
    }
    const input = {} as any;
    input.deviceId = uuid;
    this.dataService.logout(input)
      .subscribe({
        next: (response: RestResponse) => {
        }, error: (error: any) => {
        }
      });
  }

}
