.login-page {
    z-index: *********;
    // Remove top padding/margin for iOS safe area
    --padding-top: 0;
    --offset-top: 0;

    // Safe area yellow section for login screen only
    .safe-area-yellow-section {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: calc(env(safe-area-inset-top, 0px));
        background-color: #FFEA00;
        z-index: 1000;
    }

    .custom-form {
        height: 100%;
    }

    .login-header-section {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        // Add top margin to account for fixed yellow section
        margin-top: calc(env(safe-area-inset-top, 0px));

        .back-button {
            position: absolute;
            top: 40px;
            left: 10px;

            ion-icon {
                font-size: 30px;
                color: #000; // You can adjust this color
            }
        }

        .bees-logo-image {
            position: absolute;
            top: 40%;
            left: 52%;
            transform: translate(-50%, -50%);
            height: 55px;
        }
    }

    .login-page-container {
        z-index: *********;
        padding: 0px 20px 0px;
        text-align: center;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        background-color: var(--ion-color-primary-contrast);
        min-height: 0%;

        .login-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            text-align: center;

            .page-heading {
                font-size: 22px;
                color: var(--ion-color-warning-contrast);
                font-weight: 400;
                margin-top: 10px;
            }

            .page-heading-title {
                font-size: 14px;
            }
        }

        .lock-container {
            display: flex;
            align-items: center;
            justify-content: center;

            .lock-icon {
                font-size: 40px;
                border: 1px solid #D4D4D4;
                border-radius: 18px;
                padding: 12px;
            }
        }

        .form-container {
            min-height: 250px;

            .forgot-password {
                font-size: 14px;
                letter-spacing: 0;
                color: black;
                font-weight: 600;
                text-align: right;
                margin-right: 5px;
            }

            .login-button {
                --background: #FFEA00 !important;
                --background-activated: #FFEA00 !important;
                //  --color: black !important;
                //  --box-shadow: 0px 10px 20px rgba(58, 255, 223, 0.31);
                --box-shadow: 0 6px 18px rgba(0, 0, 0, 0.25);
                min-height: 54px;
                --border-radius: 22px;
                text-transform: uppercase;
                color: black !important;
                font-weight: 600;
                font-size: 16px;
                letter-spacing: 0.5px;

                // Ensure text and span are visible
                span {
                    color: black !important;
                    font-weight: 600;
                    font-size: 16px;
                    display: block;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                ion-label {
                    color: black !important;
                }

                ion-ripple-effect {
                    color: rgba(0, 0, 0, 0.3);
                    z-index: 2;
                }
            }

            // Fallback for any other ion-button in form
            ion-button:not(.login-button) {
                --background: #FFEA00;
                --box-shadow: 0px 10px 20px rgba(58, 255, 223, 0.31);
                min-height: 54px;
                --border-radius: 22px;
                text-transform: uppercase;
                color: black;
                font-weight: 600;
                font-size: unset;
            }
        }

        .register-container {
            text-align: center;
            margin-top: 75px;

            p {
                font-size: 14px;
                color: var(--ion-color-primary);
                font-weight: 600;
            }

            a {
                color: black;
                font-weight: 600;
                text-decoration: underline;
            }
        }

    }
}