import { Component, OnInit } from '@angular/core';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import mask from './../../../shared/phone-number.mask';
import { AuthenticationModel } from 'src/modals/authentication-model';
import { RestResponse } from 'src/shared/auth.model';
import { DataService } from 'src/services/data.service';
import { ToastService } from 'src/shared/toast.service';
import { AuthService } from 'src/shared/authservice';
import { LocalStorageService } from 'src/shared/local-storage.service';
import { LoadingService } from 'src/services/loading.service';

import { StatusBarService } from 'src/services/status-bar.service';
import { Style } from '@capacitor/status-bar';
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  standalone: false
})
export class LoginComponent implements OnInit {

  onClickValidation!: boolean;  // Flag to control validation state
  subscription: Subscription = new Subscription(); // Subscription for login request
  loginData: AuthenticationModel = new AuthenticationModel(); // Login data object
  passwordFieldType!: string;
  isProcessing: boolean = false;

  protected readonly mask = mask;

  constructor(private navController: NavController,
    private readonly dataService: DataService,
    private readonly toastService: ToastService,
    private readonly authService: AuthService,
    private readonly localStorageService: LocalStorageService,
    private readonly loadingService: LoadingService,
    private statusBarService: StatusBarService
  ) {
  }

  ngOnInit(): void {
    this.init();
  }

  ionViewWillEnter() {
    // Use predefined color scheme
    this.statusBarService.setColorScheme('authentication');

    // Or set custom colors
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#FFEA00', // Orange
      style: Style.Light // Light text on dark background
    });

    this.init();
  }

  init() {
    this.loginData = new AuthenticationModel();
    this.onClickValidation = false;
    this.passwordFieldType = "password";
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  forgotPassword() {
    this.navController.navigateForward("/account/forgot/password", { animated: true });
  }

  async login(form: any): Promise<any> {
    this.onClickValidation = !form.valid;
    if (!form.valid || !this.loginData.isValidLoginRequest(form)) {
      return;
    }
    this.loadingService.show();
    this.subscription = this.dataService.login(this.loginData)
      .subscribe({
        next: (response: RestResponse) => {
          this.loadingService.hide();
          const data = response.data;
          this.redirectToDashboard(data);
        },
        error: (error: any) => {
          this.loadingService.hide();
          this.toastService.show(error.message);
        }
      })
  }

  redirectToDashboard(data: any) {
    // Store token and user data in local storage
    data.token.expires_at = new Date(data.token.expires).getTime();
    this.localStorageService.setObject('token', data.token);
    this.localStorageService.setObject('user', data.user);

    // Navigate to the appropriate dashboard based on user role
    if (this.authService.isDriver()) {
      if (data.user?.isPasswordChanged === false) {
        this.navController.navigateForward("/portal/change/password", { animated: true });
        return;
      }
      this.navController.navigateRoot("/portal/dashboard");
      return;
    }
    if (this.authService.isCustomer()) {
      this.navController.navigateRoot("/client/portal/dashboard");
      return;
    }
    this.toastService.show("Sorry, Something went wrong while processing your request. Please contact to administrator");
    this.authService.logout();
    this.loginData = new AuthenticationModel();
  }

  goToRegister() {
    this.navController.navigateForward("/account/register", { animated: true });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
