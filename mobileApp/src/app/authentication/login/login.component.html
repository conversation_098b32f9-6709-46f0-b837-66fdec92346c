<ion-content class="login-page">

  <div class="safe-area-yellow-section"></div>

  <div class="login-header-section">
    <img src="/assets/images/icons/common-header.png" />
    <img class="bees-logo-image" src="/assets/images/icons/bees-logo.png" />
  </div>

  <div class="login-page-container">

    <div class="lock-container">
      <ion-icon class="lock-icon" src="/assets/images/svg/lock-icon.svg" slot="start"></ion-icon>
    </div>

    <div class="login-container">
      <span class="page-heading">Hello,<strong> Welcome Back</strong></span>
      <span class="page-heading-title">Login to your account below</span>
    </div>

    <!-- Login Form -->
    <div class="form-container">
      <form class="custom-form" #loginForm="ngForm" novalidate (ngSubmit)="login(loginForm.form)">

        <!-- Email <PERSON>gin -->
        <div class="margin-top-30 margin-bottom-10">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':username.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/email-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input #emailInput label="Email Id" labelPlacement="floating" required name="username"
              #username="ngModel" pattern="^[a-z0-9._%+\-]+@[a-z0-9.\-]+\.[a-z]{2,3}$" [(ngModel)]="loginData.email">
            </ion-input>
          </ion-item>
          <app-validation-message [field]="username" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Please provide a valid email address.'">
          </app-validation-message>
        </div>

        <!-- Password Field -->
        <div class="margin-bottom-15">
          <ion-item class="site-form-control" lines="none"
            [ngClass]="{'is-invalid':userPassword.invalid && onClickValidation}">
            <ion-icon src="/assets/images/svg/password-icon.svg" slot="start" class="start-icon"></ion-icon>
            <ion-input mode="md" label="Password" labelPlacement="floating" [type]="passwordFieldType"
              name="userPassword" #userPassword="ngModel" [(ngModel)]="loginData.password" required
              pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}">
            </ion-input>
            <i-feather name="eye" (click)="eyePassword()"
              *ngIf="loginData.password?.length && passwordFieldType !== 'password'"></i-feather>
            <i-feather name="eye-off" (click)="eyePassword()"
              *ngIf="loginData.password?.length && passwordFieldType === 'password'"></i-feather>
          </ion-item>
          <app-validation-message [field]="userPassword" [onClickValidation]="onClickValidation"
            [customPatternMessage]="'Password must contain a capital letter, number and special character & should be greater than 8 characters'">
          </app-validation-message>
        </div>

        <!-- Forgot Password -->
        <div class="forgot-password margin-bottom-20" (click)="forgotPassword()">
          <a class="forgot-password">Forgot Password?</a>
        </div>

        <!-- Submit -->
        <div>
          <ion-button class="login-button margin-top-30" expand="full" shape="round" type="submit">
            <span>Sign in</span>
            <ion-ripple-effect></ion-ripple-effect>
          </ion-button>
        </div>
      </form>
    </div>

    <!-- Register -->
    <div class="register-container">
      <p>
        Don't have an account? <a (click)="goToRegister()">Sign up</a>
      </p>
    </div>

  </div>
</ion-content>