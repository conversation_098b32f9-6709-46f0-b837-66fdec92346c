import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription } from 'rxjs/internal/Subscription';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from 'src/shared/auth.model';
import { ToastService } from 'src/shared/toast.service';
import { StatusBarService } from 'src/services/status-bar.service';
import { Style } from '@capacitor/status-bar';
@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
  standalone: false
})
export class ResetPasswordComponent implements OnInit, OnDestroy {

  onClickValidation: boolean = false;
  passwordFieldType: string = 'password';
  data: any = {};
  subscription: Subscription = new Subscription(); // Subscription to manage observable lifecycle

  constructor(
    private readonly toastService: ToastService,
    private readonly navController: NavController,
    private readonly loadingService: LoadingService,
    private readonly dataService: DataService,
    private statusBarService: StatusBarService,
    private readonly route: ActivatedRoute
  ) { }

  ngOnInit() {
    this.resetForm();
    this.route.queryParams.subscribe(params => {
      this.data = {
        resetPasswordOtpData: params['resetPasswordOtpData'] || null,
      };
      if (!this.data.resetPasswordOtpData) {
        this.toastService.show("Sorry, Somethings went wrong. Please try again after sometime");
        this.navController.navigateRoot("/account/login", { animated: true });
        return;
      }
    });
  }

  resetForm() {
    this.data = {} as any;
    this.onClickValidation = false;
  }

  ionViewWillEnter() {
    // Use predefined color scheme
    this.statusBarService.setColorScheme('authentication');

    // Or set custom colors
    this.statusBarService.setCustomStatusBar({
      backgroundColor: '#FFEA00', // Orange
      style: Style.Light // Light text on dark background
    });
  }

  validatePasswords(): boolean {
    if (this.data.password !== this.data.confirmPassword) {
      this.toastService.show('New Password and Confirm Password do not match');
      return false;
    }
    return true;
  }

  eyePassword() {
    if (this.passwordFieldType === "password") {
      this.passwordFieldType = "text";
    } else {
      this.passwordFieldType = "password";
    }
  }

  goToLoginPage() {
    this.resetForm();
    this.navController.navigateRoot("/account/login", { animated: true });
  }

  async resetPassword(form: any): Promise<void> {
    this.onClickValidation = true;
    if (!form.valid || !this.validatePasswords()) {
      return;
    }
    this.loadingService.show();

    const resetPasswordPayload = {
      token: this.data.resetPasswordOtpData?.token,
      uniqueCode: this.data.resetPasswordOtpData?.uniqueCode,
      password: this.data.password
    };
    this.dataService.resetPassword(resetPasswordPayload).subscribe({
      next: (response: RestResponse) => {
        this.loadingService.hide();

        this.toastService.show(response.message);
        setTimeout(() => {
          this.navController.navigateBack("/account/login");
        }, 200);
      },
      error: (error) => {
        this.loadingService.hide();
        this.toastService.show(error.message || 'An error occurred');
      }
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

}
