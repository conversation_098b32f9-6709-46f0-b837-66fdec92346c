.otp-forgot-password-page {
    z-index: *********;

    // Safe area yellow section
    .safe-area-yellow-section {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: calc(env(safe-area-inset-top, 0px));
        background-color: #FFEA00;
        z-index: 1000;
    }

    .custom-form {
        height: 100%;
    }

    .otp-forgot-password-header-section {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        // Add top margin to account for fixed yellow section
        margin-top: calc(env(safe-area-inset-top, 0px));

        .back-button {
            position: absolute;
            top: 40px;
            left: 10px;

            ion-icon {
                font-size: 30px;
                color: #000; // You can adjust this color
            }
        }

        .bees-logo-image {
            position: absolute;
            top: 40%;
            left: 52%;
            transform: translate(-50%, -50%);
            height: 55px;
        }
    }

    .otp-forgot-password-page-container {
        z-index: *********;
        padding: 0px 20px 0px;
        text-align: center;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        background-color: var(--ion-color-primary-contrast);
        min-height: 0%;

        .otp-forgot-password-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
            text-align: center;

            .page-heading {
                font-size: 22px;
                color: var(--ion-color-warning-contrast);
                font-weight: 400;
                margin-top: 10px;
            }

            .forgot-heading-title {
                font-size: 12px;
            }
        }

        .otp-forgot-password-container {
            display: flex;
            align-items: center;
            justify-content: center;

            .otp-forgot-password-icon {
                font-size: 80px;
                border-radius: 65px;
                padding: 25px;
                background: #FFEA00;
            }
        }

        .form-container {
            min-height: 250px;

            .otp-validate-container {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin: 0px auto;
                margin-top: 10px;

                &.disabled {
                    pointer-events: none;
                    opacity: 0.5;
                }

                ion-input {
                    display: inline-block;
                    max-width: 50px;
                    height: 50px;
                    --padding-start: 8px;
                    border: 2px solid var(--ion-color-input-border);
                    border-radius: 8px;
                    text-align: center;
                    --padding-top: 15px;
                    --padding-bottom: 15px;
                    --padding-end: 8px;

                    .input-wrapper {
                        .input-highlight {
                            height: unset;
                        }
                    }

                    &.is-invalid {
                        border: 2px solid var(--ion-color-warning-dark-red) !important;
                    }
                }
            }

            .otp-continue-button {
                --background: black !important;
                --color: white !important;
                min-height: 54px;
                --border-radius: 18px;
                text-transform: uppercase;
                color: white !important;
                font-weight: 600;
                font-size: 14px;
                letter-spacing: 0.5px;

                // Ensure text and span are visible
                span {
                    color: white !important;
                    font-weight: 600;
                    font-size: 14px;
                    display: block;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }

                ion-label {
                    color: white !important;
                }
            }

            // Fallback for any other ion-button in form
            ion-button:not(.otp-continue-button) {
                --background: black;
                min-height: 54px;
                --border-radius: 18px;
                text-transform: uppercase;
                color: white;
                font-weight: 400;
                font-size: 12px;
            }
        }

        .resend-otp-text {
            color: #29385b;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;

            .green-text {
                color: var(--ion-color-green-text) !important;
            }
        }

    }
}