import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy } from '@angular/router';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpAuthInterceptor } from 'src/shared/http.interceptor';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LoginComponent } from './authentication/login/login.component';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { SharedModuleModule } from 'src/shared/shared-module.module';
import { IonicStorageModule } from '@ionic/storage-angular';
import { IconsModule } from 'src/shared/icon.module';
import { MaskitoDirective } from '@maskito/angular';
import { ForgotPasswordComponent } from './authentication/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './authentication/reset-password/reset-password.component';
import { OnboardingPage } from './onboarding/onboarding.page';
import { ImageCropperModule } from 'ngx-image-cropper';
import { OtpForgotPasswordComponent } from './authentication/otp-forgot-password/otp-forgot-password.component';
import { DriverEmailComponent } from './driver-layout/profile-management/driver-email/driver-email.component';
import { DriverAddressComponent } from './driver-layout/profile-management/driver-address/driver-address.component';

@NgModule({
  declarations: [
    AppComponent,
    OnboardingPage,
    DriverEmailComponent,
    DriverAddressComponent,
    LoginComponent,
    ForgotPasswordComponent,
    OtpForgotPasswordComponent,
    ResetPasswordComponent,
  ],
  imports: [
    BrowserModule,
    IonicModule.forRoot(),
    IonicStorageModule.forRoot(),
    AppRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    NgxMaskDirective,
    SharedModuleModule,
    IconsModule,
    MaskitoDirective,
    ImageCropperModule,
  ],
  providers: [{ provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
  provideHttpClient(withInterceptorsFromDi()),
  {
    provide: HTTP_INTERCEPTORS,
    useClass: HttpAuthInterceptor,
    multi: true
  },
  provideNgxMask()
  ],
  bootstrap: [AppComponent],
})
export class AppModule { }
