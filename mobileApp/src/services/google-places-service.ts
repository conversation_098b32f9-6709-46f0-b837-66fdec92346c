import { Injectable } from "@angular/core";

declare var google: any;

@Injectable({ providedIn: 'root' })
export class GooglePlacesService {

    private autocompleteService = new google.maps.places.AutocompleteService();
    private placesService: any;

    constructor() {
        const dummyDiv = document.createElement('div');
        this.placesService = new google.maps.places.PlacesService(dummyDiv);
    }

    fetchAutocomplete(input: string): Promise<any[]> {
        return new Promise((resolve, reject) => {
            this.autocompleteService.getPlacePredictions(
                { input, componentRestrictions: { country: 'ca' }, types: ['address'] },
                (predictions: any[] | PromiseLike<any[]>, status: any) => {
                    if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
                        resolve(predictions);
                    } else {
                        reject(status);
                    }
                }
            );
        });
    }

    fetchPlaceDetails(placeId: string): Promise<any> {
        return new Promise((resolve, reject) => {
            this.placesService.getDetails(
                { placeId },
                (place: any, status: any) => {
                    if (status === google.maps.places.PlacesServiceStatus.OK) {
                        resolve(place);
                    } else {
                        reject(status);
                    }
                }
            );
        });
    }
}
