import { Injectable } from '@angular/core';
import { Device } from '@capacitor/device';
import { BarcodeFormat, BarcodeScanner } from '@capacitor-mlkit/barcode-scanning';

@Injectable({ providedIn: 'root' })
export class BarcodeScannerService {

    scanBarcode(): Promise<string | null> {
        return new Promise(async (resolve, reject) => {
            const granted = await this.requestPermissions();
            if (!granted) return;

            try {
                const deviceType = (await Device.getInfo()).platform;
                if (deviceType !== 'ios') {
                    const { available } = await BarcodeScanner.isGoogleBarcodeScannerModuleAvailable();
                    if (!available) await BarcodeScanner.installGoogleBarcodeScannerModule();
                }

                const { barcodes } = await BarcodeScanner.scan({
                    formats: [BarcodeFormat.Code128, BarcodeFormat.QrCode],
                });
                resolve(barcodes[0].displayValue as string);
            } catch (error) {
                reject(error);
            }
        });
    }

    async requestPermissions() {
        const { camera } = await BarcodeScanner.checkPermissions();
        return camera;
    }

}
