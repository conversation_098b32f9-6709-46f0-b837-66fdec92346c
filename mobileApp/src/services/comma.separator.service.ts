import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'commaSeparator',
  standalone: false,
})
export class CommaSeparatorPipe implements PipeTransform {

  transform(value: number | null): string {
    if (value == null) {
      return '';
    }

    // Convert number to string
    let numStr = value.toString();

    // Format for Indian number system
    let lastThree = numStr.slice(-3);
    let otherNumbers = numStr.slice(0, -3);
    if (otherNumbers !== '') {
      lastThree = ',' + lastThree;
    }
    let result = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + lastThree;

    return result;
  }
}
