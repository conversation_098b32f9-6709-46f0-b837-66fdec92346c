import { Injectable } from "@angular/core";
import { <PERSON><PERSON><PERSON>ontroller } from "@ionic/angular";
import { MaskitoElementPredicate, MaskitoOptions } from "@maskito/core";
import { CommaSeparatorPipe } from 'src/services/comma.separator.service';
import { FullImageComponent } from "src/shared/full-image/full-image.component";

@Injectable({
  providedIn: 'root'
})

export class CommonService {

  public activeTab: string = ''; // e.g., 'shipping', 'fuel', etc.
  isChangePasswordModalOpen: boolean = false;

  constructor(private modalCtrl: ModalController) {
  }

  readonly postalCodeMask: MaskitoOptions = {
    mask: [
      /[A-Za-z]/, /\d/, /[A-Za-z]/, ' ', /\d/, /[A-Za-z]/, /\d/
    ]
  };

  readonly phoneMask: MaskitoOptions = {
    mask: [
      '+', '9', '1', ' ', // Country code +91 and space
      /\d/, /\d/, /\d/, /\d/, /\d/, // First segment (5 digits)
      ' ', // Space
      /\d/, /\d/, /\d/, /\d/, /\d/  // Second segment (5 digits)
    ]
  };

  readonly maskPredicate: MaskitoElementPredicate = async (el) => (el as HTMLIonInputElement).getInputElement();

  static isNullOrUndefined(value: any): boolean {
    return value === null || value === undefined;
  }

  formatText(type: string | null): string {
    if (!type) return '';

    return type
      .toLowerCase()
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  formatTextAllCapital(type: string): string {
    if (!type) return '';

    return type
      .split('_') // Split the string by underscores
      .map(word => word.toUpperCase()) // Ensure all words are uppercase
      .join(' '); // Join the words with spaces
  }

  formatPhoneNumberForApi(phoneNumber: string): string {
    if (!phoneNumber) {
      return '';
    }

    // Extract only the dial code (e.g., "+1")
    const dialCodeMatch = phoneNumber.match(/^\+[\d]+/);
    const dialCode = dialCodeMatch ? dialCodeMatch[0] : '';

    // Remove the dial code from the phone number
    const localNumber = phoneNumber.replace(dialCode, '');

    // Remove any non-digit characters and trim
    return localNumber.replace(/[^\d]/g, '').trim();
  }

  formatPhoneForDisplay(phone: string | null): string {
    if (!phone) return '';
    const digits = phone.replace(/\D/g, '').slice(0, 10);
    if (digits.length > 6) {
      return `${digits.slice(0, 3)}-${digits.slice(3, 6)} ${digits.slice(6)}`;
    } else if (digits.length > 3) {
      return `${digits.slice(0, 3)}-${digits.slice(3)}`;
    }
    return digits;
  }

  formatDate(dateString?: string): string {
    if (!dateString) {
      return 'N/A';
    }

    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear().toString().slice(-2);
      const hours = date.getHours();
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const ampm = hours >= 12 ? 'pm' : 'am';
      const displayHours = hours % 12 || 12;

      return `${day}-${month}-${year}, ${displayHours}:${minutes}${ampm}`;
    } catch (error) {
      return 'N/A';
    }
  }

  formatDisplayDate(isoDate: string | null | undefined): string {
    if (!isoDate) return '';
    try {
      const date = new Date(isoDate);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${month}/${day}/${year}`;
    } catch {
      return '';
    }
  }

  formatNotificationDate(dateString: string): { date: string; time: string } {
    const date = new Date(dateString);

    // Format options
    const dateOptions: Intl.DateTimeFormatOptions = { day: '2-digit', month: 'short', year: 'numeric' };
    const timeOptions: Intl.DateTimeFormatOptions = { hour: '2-digit', minute: '2-digit', hour12: false };

    const formattedDate = date.toLocaleDateString('en-US', dateOptions); // e.g., 17 Dec 2024
    const formattedTime = date.toLocaleTimeString('en-US', timeOptions); // e.g., 10:39

    return { date: formattedDate, time: formattedTime };
  }

  async viewImage(imageUrl: string) {
    const modal = await this.modalCtrl.create({
      component: FullImageComponent,
      componentProps: { imageUrl },
      cssClass: 'full-image-modal'
    });
    await modal.present();
  }

  changeInputFocus(event: any) {
    const pattern = /\d/;
    if (!pattern.test(event.detail.value)) {
      event.target.value = "";
      event.preventDefault();
      return;
    }
    const targetValueLength = event.detail.value.length;
    // Handle other key presses
    if (targetValueLength === 1) {
      const nextElement = event.target.closest('ion-input').nextElementSibling?.querySelector('input');
      if (nextElement) {
        nextElement.focus();
      }
    }
  }

  handleBackspace(event: any) {
    const keyCode = event.keyCode || event.which;

    // Handle backspace key press
    if (keyCode === 8 && event.target.value.length === 0) {
      const previousElement = event.target.closest('ion-input').previousElementSibling?.querySelector('input');
      if (previousElement) {
        previousElement.focus();
        previousElement.value = ''; // Clear the previous input value
      }
    }
  }

  getOTPFromInputs(otpInput1: any, otpInput2: any, otpInput3: any, otpInput4: any, otpInput5?: any, otpInput6?: any) {
    const input1 = otpInput1 != null ? otpInput1.toString().charAt(0) : '';
    const input2 = otpInput2 != null ? otpInput2.toString().charAt(0) : '';
    const input3 = otpInput3 != null ? otpInput3.toString().charAt(0) : '';
    const input4 = otpInput4 != null ? otpInput4.toString().charAt(0) : '';
    const input5 = otpInput5 != null ? otpInput5.toString().charAt(0) : '';
    const input6 = otpInput6 != null ? otpInput6.toString().charAt(0) : '';
    return `${input1 || ''}${input2 || ''}${input3 || ''}${input4 || ''}${input5 || ''}${input6 || ''}`;
  }

}
