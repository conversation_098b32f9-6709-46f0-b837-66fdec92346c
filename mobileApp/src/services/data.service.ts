import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { RestResponse } from 'src/shared/auth.model';

@Injectable({
  providedIn: 'root'
})
export class DataService {

  constructor(private readonly http: HttpClient) {
  }

  getRecords(path: string): Observable<any> {
    return this.http
      .get(environment.BaseApiUrl + path, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  getRecordsById(path: string, payload: any): Observable<any> {
    return this.http
      .get(environment.BaseApiUrl + path + payload, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  saveRecord(path: string, resource: any): Observable<any> {
    return this.http
      .post(environment.BaseApiUrl + path, resource, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  updateRecord(path: string, resource: any): Observable<any> {
    return this.http
      .put(environment.BaseApiUrl + path, resource, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  removeRecord(path: string): Observable<any> {
    return this.http
      .delete(environment.BaseApiUrl + path, { headers: environment.AppHeaders })
      .pipe(catchError(this.handleError));
  }

  postUrl(path: string): Observable<RestResponse> {
    return this.http
      .post<RestResponse>(environment.BaseApiUrl + path, null, { headers: environment.AppHeaders })
      .pipe(
        catchError((error) => this.handleError(error))
      );
  }

  protected handleError(error: HttpErrorResponse): Promise<any> {
    if (error.status === 404) {
      return Promise.reject({ "message": error.message ? error.message : 'Something went wrong while processing your request' });
    }
    const internalError: any = JSON.parse(JSON.stringify(error));
    if (!internalError.message) {
      internalError.message = 'Something went wrong while processing your request';
    }
    return Promise.reject(internalError);
  }

  customerOnboarding(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/register', data);
  }

  login(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/account/app/login', data);
  }

  forgotPassword(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/forgot/password', data);
  }

  otpVerify(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/forgot/verfication', data);
  }

  resendOtp(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/forgot/otp/resend', data);
  }

  resetPassword(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/reset/password', data);
  }

  changePassword(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/account/change/password', data);
  }

  uploadFile(data: FormData): Observable<RestResponse> {
    return this.http.post<RestResponse>(`${environment.BaseApiUrl}/api/file/group/items/upload`, data);
  }

  getShipments(data: any = {}): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipments', data);
  }

  getCalendarShipments(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/driver/shipments/calender', data);
  }

  getClientCalendarShipments(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/customer/shipments/calender', data);
  }

  getRefId(): Observable<RestResponse> {
    return this.getRecords('/api/shipment/refId');
  }

  getQuotationRefId(): Observable<RestResponse> {
    return this.getRecords('/api/quotation/refId');
  }

  getAllCustomers(): Observable<RestResponse> {
    return this.saveRecord('/api/account/app/customer/shipment/selection', {});
  }

  getCitySelection(): Observable<RestResponse> {
    return this.saveRecord('/api/app/address/city/selection', {});
  }

  saveShipment(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipment', data);
  }

  getCargoList(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/ShipmentItems', data);
  }

  saveCargoItem(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipmentitem', data);
  }

  updateCargoItem(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/app/driver/shipmentitem', data);
  }

  saveShipmentSpecials(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipment/specials', data);
  }

  startShipment(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipment/start', data);
  }

  getShipmentById(id: string): Observable<RestResponse> {
    const path = `/api/app/shipment/${id}`;
    return this.getRecordsById(path, '');
  }

  updateShipment(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/app/driver/shipment', data);
  }

  getPopImages(id: string): Observable<RestResponse> {
    const path = `/api/app/driver/shipment/pop/documents/${id}`;
    return this.getRecordsById(path, '');
  }

  getPodImages(id: string): Observable<RestResponse> {
    const path = `/api/app/driver/shipment/pod/documents/${id}`;
    return this.getRecordsById(path, '');
  }

  submitPOD(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/shipment/pod', data);
  }

  getCustomerShipments(data: any = {}): Observable<RestResponse> {
    return this.saveRecord('/api/app/Customer/shipments', data);
  }

  getCustomerShipmentById(id: string): Observable<RestResponse> {
    const path = `/api/app/Customer/shipment/${id}`;
    return this.getRecordsById(path, '');
  }

  saveCustomerShipment(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/customer/shipment', data);
  }

  updateCustomerShipment(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/app/customer/shipment', data);
  }

  getCustomerCargoList(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/customer/ShipmentItems', data);
  }

  saveCustomerCargoItem(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/customer/shipmentitem', data);
  }

  updateCustomerCargoItem(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/app/customer/shipmentitem', data);
  }

  saveCustomerShipmentSpecials(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/customer/shipment/specials', data);
  }

  getAllQuotations(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/customer/Quotations', data);
  }

  getQuotationById(id: string): Observable<RestResponse> {
    const path = `/api/app/customer/quotation/${id}`;
    return this.getRecordsById(path, '');
  }

  getQuotationDetails(id: string): Observable<RestResponse> {
    const path = `/api/website/customer/quotation/detail/${id}`;
    return this.getRecordsById(path, '');
  }

  getQuotationGrandTotal(id: string): Observable<RestResponse> {
    const path = `/api/website/customer/quotation/${id}`;
    return this.getRecordsById(path, '');
  }
  getPickupLocationDetail(): Observable<RestResponse> {
    return this.saveRecord('/api/account/app/customer/detail', null);
  }

  saveClientQuotation(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/customer/quotation', data);
  }

  updateClientQuotation(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/app/customer/quotation', data);
  }

  getClientQuotationCargoList(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/customer/QuotationItems', data);
  }

  saveClientQuotationCargoItem(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/customer/quotationitem', data);
  }

  updateClientQuotationCargoItem(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/app/customer/quotationitem', data);
  }

  saveUpdateQuotationSpecials(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/customer/quotation/specials', data);
  }

  getCustomerDashboardData(): Observable<RestResponse> {
    return this.saveRecord('/api/customer/app/dashboard/count', null);
  }

  getDriverDashboardData(): Observable<RestResponse> {
    return this.saveRecord('/api/driver/dashboard/count', null);
  }

  searchDriverShipment(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/search/driver/shipments', data);
  }

  searchCustomerShipment(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/search/customer/shipments', data);
  }

  // User Profile APIs
  getUserProfile(): Observable<RestResponse> {
    return this.getRecords('/api/customer/app/userprofile');
  }

  updateUserProfile(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/customer/app/userprofile', data);
  }

  getDriverUserProfile(): Observable<RestResponse> {
    return this.getRecords('/api/app/driver/userprofile');
  }

  updateDriverUserProfile(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/app/driver/userProfile', data);
  }

  // Fuel Receipt APIs
  saveFuelReceipt(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/fuelreceipt', data);
  }

  editFuelReceipt(data: any): Observable<RestResponse> {
    return this.updateRecord('/api/app/driver/fuelreceipt', data);
  }

  getFuelReceipts(data: any = {}): Observable<RestResponse> {
    return this.saveRecord('/api/app/driver/fuelReceipts', data);
  }

  getFuelReceiptById(id: string): Observable<RestResponse> {
    const path = `/api/app/driver/fuelreceipt/${id}`;
    return this.getRecordsById(path, '');
  }

  getVehicleSelection(): Observable<RestResponse> {
    return this.saveRecord('/api/vehicle/selection', null);
  }

  deleteFuelReceipt(id: string): Observable<RestResponse> {
    const path = `/api/app/driver/fuelreceipt/${id}`;
    return this.removeRecord(path);
  }

  deleteShipmentCargoItem(id: string): Observable<RestResponse> {
    const path = `/api/app/driver/shipmentitem/${id}`;
    return this.removeRecord(path);
  }

  deleteCustomerShipmentCargoItem(id: string): Observable<RestResponse> {
    const path = `/api/app/customer/shipmentitem/${id}`;
    return this.removeRecord(path);
  }

  deleteQuotationCargoItem(id: string): Observable<RestResponse> {
    const path = `/api/app/customer/quotationitem/${id}`;
    return this.removeRecord(path);
  }

  logout(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/account/app/logout`, input);
  }

  saveToken(input: any): Observable<RestResponse> {
    return this.saveRecord(`/api/userdevice`, input);
  }

  myNotifications(data: any): Observable<RestResponse> {
    return this.saveRecord('/api/notifications', data);
  }

  readAllNotifications(data: any): Observable<RestResponse> {
    const path = '/api/view/all/notifications';
    return this.saveRecord(path, data);
  }

  readSingleNotification(data: any): Observable<RestResponse> {
    const path = '/api/view/notifications';
    return this.saveRecord(path, data);
  }

  getUnreadNotificationCount(data: any): Observable<RestResponse> {
    return this.saveRecord(`/api/Notifications/count`, data);
  }

}


