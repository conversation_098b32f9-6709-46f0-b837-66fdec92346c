import { Injectable } from '@angular/core';
import { Geolocation } from '@capacitor/geolocation';
import { Platform, ToastController } from '@ionic/angular';
import { HttpClient } from '@angular/common/http';
import { TrackMyLocation } from 'track-my-location/src';
import { Capacitor } from '@capacitor/core';
import { AndroidSettings, IOSSettings, NativeSettings } from 'capacitor-native-settings';

@Injectable({
  providedIn: 'root',
})
export class TrackMyLocationService {
  private trackingFilePath = 'temp/live_tracking.kml';
  private isFileInitialized = false; // To check if the KML file has been initialized
  private lastCoordinates: { latitude: number; longitude: number } | null = null;

  constructor(
    private toastController: ToastController,
    private http: HttpClient,
    private platform: Platform
  ) { }

  async showGPSDialog() {
    const toast = await this.toastController.create({
      message: 'Please enable GPS to use location services.',
      duration: 3000,
      position: 'bottom',
    });
    await toast.present();
  }

  async sendObject(userPayload: object) {
    const platform = Capacitor.getPlatform();
    await TrackMyLocation.sendObject({ userPayload: userPayload })
      .then((res: any) => {
        // alert("return value is " + JSON.stringify(res.value));
      })
      .catch((err: any) => {
        alert("return value is " + JSON.stringify(err));
      });
  }

  async stopService() {
    const platform = Capacitor.getPlatform();
    await TrackMyLocation.stopService()
      .then((res: any) => {
        console.log("Location service stopped successfully:", res);
      })
      .catch((err: any) => {
        console.error("Error stopping location service:", err);
      });
  }

  async isServiceRunning(): Promise<boolean> {
    try {
      const result = await TrackMyLocation.isServiceRunning();
      return result.isRunning;
    } catch (error) {
      console.error("Error checking if service is running:", error);
      return false;
    }
  }

  async checkAndEnableGPS() {
    try {
      // Try to get the location
      await Geolocation.getCurrentPosition({
        enableHighAccuracy: true
      });
      console.log('GPS is enabled');
      return true;
    } catch (error) {
      console.warn('GPS is disabled:', error);
      return false;
      // this.openLocationSettings();
    }
  }

  async openLocationSettings() {
    try {
      if (this.platform.is('android')) {
        // Open app settings on Android
        await NativeSettings.openAndroid({
          option: AndroidSettings.Location // Opens app-specific settings (no direct location setting available)
        });
        console.log('Opened Android app settings');
      } else if (this.platform.is('ios')) {
        // Open app settings on iOS
        await NativeSettings.openIOS({
          option: IOSSettings.App // iOS only supports 'App' option
        });
        console.log('Opened iOS app settings');
      }
    } catch (error) {
      console.error('Error opening settings:', error);
    }
  }


  async checkPreciseLocation() {
    try {
      if (this.platform.is('android')) {
        const position = await Geolocation.getCurrentPosition({
          enableHighAccuracy: true // Requesting high accuracy
        });

        // If position is obtained with high accuracy, we assume precise location is enabled
        console.log('Precise location enabled:', position);
      } else if (this.platform.is('ios')) {
        const permission = await Geolocation.requestPermissions();

        // iOS: check the permission and the location accuracy
        if (permission.location === 'granted') {
          // Check if high accuracy is enabled by getting the location with high accuracy
          const position = await Geolocation.getCurrentPosition({
            enableHighAccuracy: true
          });
          console.log('Precise location enabled:', position);
        } else {
          console.log('Precise location is not enabled on iOS');
        }
      }
    } catch (error) {
      console.error('Error checking precise location:', error);
    }
  }

  async getCurrentPosition() {
    const coordinates = await Geolocation.getCurrentPosition();

    console.log('Current position:', coordinates.coords.latitude);
    return coordinates.coords;
  }

  openLocationInGoogleMaps(latitude: number, longitude: number) {
    const url = `https://www.google.com/maps?q=${latitude},${longitude}`;
    window.open(url, '_blank');
  }

}
