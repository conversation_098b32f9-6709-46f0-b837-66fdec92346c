import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PolicyLinksService {
  
  constructor() { }

  /**
   * Get the privacy policy URL using the base URL from environment
   * @returns The full privacy policy URL
   */
  getPrivacyPolicyUrl(): string {
    return `${environment.BaseApiUrl}/privacy-policy`;
  }

  /**
   * Get the terms of use URL using the base URL from environment
   * @returns The full terms of use URL
   */
  getTermsOfUseUrl(): string {
    return `${environment.BaseApiUrl}/terms-of-use`;
  }
}
