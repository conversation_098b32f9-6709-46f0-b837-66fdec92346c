import { Injectable } from "@angular/core";
import { Loading<PERSON>ontroller, ToastController } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { ERROR_TOAST_COLOR, SUCCESS_TOAST_COLOR } from 'src/environments/environment.prod';
import { CommonService } from './common.service';

@Injectable({
    providedIn: 'root'
})

export class LoadingService {
    [x: string]: any;
    toastSubscription!: Subscription;
    dataLoaded: boolean;
    constructor(
        private loadingController: LoadingController,
        public toastController: ToastController
    ) {
        this.presentCustomToast();
        this.dataLoaded = false;
    }

    async presentCustomToast() {
        const toast = await this.toastController.create({
          duration: 3000,
          position: 'bottom',
          translucent: true, // for styling
          cssClass: 'custom-toast', // for additional styling with CSS
        });
   //     toast.present();
      }

    async networkToast(message: string, toastColor: string) {
        const duration = 3000; // Default duration
        let cssClass = '';
        
        if (toastColor === ERROR_TOAST_COLOR) {
          cssClass = 'errorToastClass';
        } else {
          cssClass = 'successToastClass';
        }
    
        const toast = await this.toastController.create({
          message: message,
          duration: duration,
          cssClass: cssClass
        });
    
        toast.present();
      }

    async successToast(message: string, addPixelsY?: number) {
        const toast = await this.toastController.create({
            message: message,
            duration: 3000,
            cssClass: "successToastClass",
          });
          toast.present();
    }

    async errorToast(message: string, addPixelsY?: number) {
        const toast = await this.toastController.create({
            message: message,
            duration: 3000,
            cssClass: "errorToastClass",
          });
          toast.present();
    }

    show() {
        const body = document.getElementsByTagName("body")[0];
        body.style.overflow = "hidden";
        const loading: any = document.getElementById("loading");
        if (!loading) {
          return;
        }
        loading.style.display = 'flex';
      }
    
      hide() {
        const body = document.getElementsByTagName("body")[0];
        body.style.overflow = "auto";
        const loading: any = document.getElementById("loading");
        if (!loading) {
          return;
        }
        loading.style.display = 'none';
      }

    unsubscribeToast() {
        if (CommonService.isNullOrUndefined(this.toastSubscription)) {
            return;
        }
        setTimeout(() => {
            this.toastSubscription.unsubscribe();
        }, 3000);
    }

    toogleDataLoading(dataLoaded: boolean) {
        this.dataLoaded = dataLoaded;
    }
}
