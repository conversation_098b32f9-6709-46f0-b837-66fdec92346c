// Define the AddressDetail class
export class AddressDetail {
  address: string | null = null;
  city: string | null = null;
  state: string | null = null;
  country: string | null = null;
  pin: string | null = null;
  latitude: string | null = null;
  longitude: string | null = null;
}

export class CustomerDetail {
  companyCountryCode: string | null = "+1-CA";
  accountsCountryCode: string | null = "+1-CA";
  keyContactCountryCode: string | null = "+1-CA";
  step: number | null = 1
  companyName: string | null = null;
  companyEmail: string | null = null;
  companyPhone: string | null = null;
  accountsPayableEmail: string | null = null;
}

export class ProfileDetail {
  // roleName: string | null = 'ROLE_CUSTOMER';
  email: string | null = null;
  password: string | null = null;
  firstName: string | null = null;
  lastName: string | null = null;
  phoneNumber: string = "";
  //  profileImageUrl: string | null = null;
  countryCode: string | null = "+1-CA";
  addressDetail: AddressDetail = new AddressDetail();
  customerDetail: CustomerDetail = new CustomerDetail();

  isValidBasicRequest(form: any) {
    if (!this.firstName || this.firstName.trim() === '') {
      form.controls.firstName.setErrors({ invalid: true });
      return false;
    }
    if (!this.lastName || this.lastName.trim() === '') {
      form.controls.lastName.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  isValidEmailRequest(form: any) {
    if (!this.email || this.email.trim() === '') {
      form.controls.firstName.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  static fromResponse(data: any): ProfileDetail {
    const record = new ProfileDetail();
    record.email = data.email;
    record.password = data.password;
    record.firstName = data.firstName;
    record.lastName = data.lastName;
    record.phoneNumber = data.phoneNumber;
    //  record.profileImageUrl = data.profileImageUrl;
    record.countryCode = data.countryCode;
    record.addressDetail = data.addressDetail;
    record.customerDetail = data.customerDetail
    return record;
  }
}
