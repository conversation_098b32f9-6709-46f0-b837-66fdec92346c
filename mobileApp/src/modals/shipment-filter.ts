export interface ShipmentFilter {
  tabType: 'PENDING' | 'IN_TRANSIT_TAB' | 'COMPLETED';
  offset: number;
  pickupCity?: string;
  deliveryCity?: string;
  customerId?: string;
  shipmentType?: string;
}

export interface CityOption {
  id: string;
  name: string;
  province?: string;
}

export interface CustomerOption {
  id: string;
  name: string;
  companyName?: string;
}

export interface ShipmentTypeOption {
  value: string;
  label: string;
}

export const SHIPMENT_TYPES: ShipmentTypeOption[] = [
  { value: 'RUSH', label: 'Rush' },
  { value: 'FLAT_DECK', label: 'Flat Deck' },
  { value: 'TRUCK_TRAILER', label: 'Truck & Trailer' },
  { value: 'REGULAR', label: 'Regular' }
];
