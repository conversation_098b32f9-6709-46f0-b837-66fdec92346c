export class QuotationCargoDetails {
    id: string | null = null;
    quotation: string | null = null;
    quantity: number | null = null;
    description: string | null = null;
    cargoType: string | null = null;
    weightType: string | null = null;
    weightInPounds: number | null = null;
    rateType: string | null = null;
    weight: number | null = null;  // ✅ changed to number
    length: number | null = null;  // ✅ changed to number
    volume: number | null = null;
    width: number | null = null;   // ✅ changed to number
    height: number | null = null;  // ✅ changed to number

    static fromResponse(data: any): QuotationCargoDetails {
        const detail = new QuotationCargoDetails();
        detail.id = data.id ?? null;
        detail.quotation = data.quotation ?? null;
        detail.quantity = data.quantity ?? null;
        detail.description = data.description ?? null;
        detail.cargoType = data.cargoType ?? null;
        detail.weightType = data.weightType ?? null;
        detail.weightInPounds = data.weightInPounds ?? null;
        detail.rateType = data.rateType ?? null;
        detail.weight = data.weight ?? null;
        detail.length = data.length ?? null;
        detail.volume = data.volume ?? null;
        detail.width = data.width ?? null;
        detail.height = data.height ?? null;
        return detail;
    }
}

export class QuotationSpecialRequestStates {
    id: string | null = null;
    isOversize: boolean = false;
    isRushRequest: boolean = false;
    isEnclosed: boolean = false;
    isFragile: boolean = false;
    isPerishable: boolean = false;
    isDangerousGoods: boolean = false;
    //  oversizeComment: string = '';

    static fromResponse(data: any): QuotationSpecialRequestStates {
        const request = new QuotationSpecialRequestStates();
        request.id = data.id ?? null; // Support both field names for backward compatibility
        request.isOversize = data.isOversize ?? false;
        request.isRushRequest = data.isRushRequest ?? false;
        request.isEnclosed = data.isEnclosed ?? false;
        request.isFragile = data.isFragile ?? false;
        request.isPerishable = data.isPerishable ?? false;
        request.isDangerousGoods = data.isDangerousGoods ?? false;

        //  request.oversizeComment = data.oversizeComment ?? '';

        return request;
    }
}
