export class PickupAddressDetail {
    address: string | null = null;
    city: string | null = null;
    state: string | null = null;
    country: string | null = null;
    pin: string | null = null;
    latitude: string | null = null;
    longitude: string | null = null;

    static fromResponse(data: any): PickupAddressDetail {
        const addr = new PickupAddressDetail();
        addr.address = data.address ?? null;
        addr.city = data.city ?? null;
        addr.state = data.state ?? null;
        addr.country = data.country ?? null;
        addr.pin = data.pin ?? null;
        addr.latitude = data.latitude ?? null;
        addr.longitude = data.longitude ?? null;
        return addr;
    }
}

export class DeliveryAddressDetail {
    address: string | null = null;
    city: string | null = null;
    state: string | null = null;
    country: string | null = null;
    pin: string | null = null;
    latitude: string | null = null;
    longitude: string | null = null;

    static fromResponse(data: any): DeliveryAddressDetail {
        const addr = new DeliveryAddressDetail();
        addr.address = data.address ?? null;
        addr.city = data.city ?? null;
        addr.state = data.state ?? null;
        addr.country = data.country ?? null;
        addr.pin = data.pin ?? null;
        addr.latitude = data.latitude ?? null;
        addr.longitude = data.longitude ?? null;
        return addr;
    }
}

export class QuotationBasicInfo {
    id: string | null = null;
    refID: string | null = null;
    contactPersonEmail: string | null = null;
    contactPersonName: string | null = null;
    contactPersonPhone: string | null = null;
    contactPersonCountryCode: string | null = "+1-CA";
    summary: string | null = null;
    pickupContactPersonName: string | null = null;
    pickupCompanyName: string | null = null;
    pickupContactPersonPhone: string | null = null;
    pickupContactCountryCode: string | null = "+1-CA";
    pickupAddressDetail: PickupAddressDetail = new PickupAddressDetail();
    deliveryCompanyName: string | null = null;
    deliveryContactPersonName: string | null = null;
    deliveryContactPersonPhone: string | null = null;
    deliveryContactCountryCode: string | null = "+1-CA";
    deliveryAddressDetail: DeliveryAddressDetail = new DeliveryAddressDetail();

    static fromResponse(data: any): QuotationBasicInfo {
        const info = new QuotationBasicInfo();
        info.id = data.id ?? null;
        info.refID = data.refID ?? null;
        info.contactPersonEmail = data.contactPersonEmail ?? null;
        info.contactPersonName = data.contactPersonName ?? null;
        info.contactPersonPhone = data.contactPersonPhone ?? null;
        info.contactPersonCountryCode = data.contactPersonCountryCode ?? null;
        info.summary = data.summary ?? null;
        info.pickupContactPersonName = data.pickupContactPersonName ?? null;
        info.pickupCompanyName = data.pickupCompanyName ?? null;
        info.pickupContactPersonPhone = data.pickupContactPersonPhone ?? null;
        info.pickupContactCountryCode = data.pickupContactCountryCode ?? null;
        info.deliveryCompanyName = data.deliveryCompanyName ?? null;
        info.deliveryContactPersonName = data.deliveryContactPersonName ?? null;
        info.deliveryContactPersonPhone = data.deliveryContactPersonPhone ?? null;
        info.deliveryContactCountryCode = data.deliveryContactCountryCode ?? null;

        if (data.pickupAddressDetail) {
            info.pickupAddressDetail = PickupAddressDetail.fromResponse(data.pickupAddressDetail);
        }
        if (data.deliveryAddressDetail) {
            info.deliveryAddressDetail = DeliveryAddressDetail.fromResponse(data.deliveryAddressDetail);
        }

        return info;
    }

}
