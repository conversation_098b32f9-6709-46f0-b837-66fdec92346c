// Define the AddressDetail class
export class AddressDetail {
  address: string | null = null;
  city: string | null = null;
  state: string | null = null;
  country: string | null = null;
  pin: string | null = null;
  latitude: string | null = null;
  longitude: string | null = null;
}

export class CustomerDetail {
  companyCountryCode: string | null = "+1-CA";
  accountsCountryCode: string | null = "+1-CA";
  keyContactCountryCode: string | null = "+1-CA";
  step: number | null = 3
  companyName: string | null = null;
  accountsPayableEmail: string | null = null;
}

export class UserProfileDetail {
  id: string | null = null;
  email: string | null = null;
  firstName: string | null = null;
  lastName: string | null = null;
  phoneNumber: string = "";
  profileImageUrl: string | null = null;
  countryCode: string | null = "+1-CA";
  addressDetail: AddressDetail = new AddressDetail();
  customerDetail: CustomerDetail = new CustomerDetail();

  isValidBasicRequest(form: any) {
    if (!this.firstName || this.firstName.trim() === '') {
      form.controls.firstName.setErrors({ invalid: true });
      return false;
    }
    if (!this.lastName || this.lastName.trim() === '') {
      form.controls.lastName.setErrors({ invalid: true });
      return false;
    }
    return true;
  }

  static fromResponse(data: any): UserProfileDetail {
    const record = new UserProfileDetail();
    record.id = data.id ?? null;
    record.email = data.email;
    record.firstName = data.firstName;
    record.lastName = data.lastName;
    record.phoneNumber = data.phoneNumber;
    record.profileImageUrl = data.profileImageUrl;
    record.countryCode = data.countryCode;
    record.addressDetail = data.addressDetail;
    record.customerDetail = data.customerDetail
    return record;
  }
}
