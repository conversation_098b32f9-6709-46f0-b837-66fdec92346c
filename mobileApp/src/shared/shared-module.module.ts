import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { NgxMaskPipe } from 'ngx-mask';
import { ImageCropperModule } from 'ngx-image-cropper';
import { IconsModule } from './icon.module';
import { CustomMaskDirective } from './custom.mask.directive';
import { ValidationMessageComponent } from './validation-message/validation-message.component';
import { CustomerHeaderComponent } from '../app/common/customer-header/customer-header.component';
import { FileCropperComponent } from './file-cropper/file-cropper.component';
import { FullImageComponent } from './full-image/full-image.component';
import { CommaSeparatorPipe } from '../services/comma.separator.service';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ImageCropperModule,
    IconsModule
  ],
  declarations: [
    CustomMaskDirective,
    ValidationMessageComponent,
    CustomerHeaderComponent,
    FileCropperComponent,
    FullImageComponent,
    CommaSeparatorPipe,
  ],
  exports: [
    CustomMaskDirective,
    ValidationMessageComponent,
    CustomerHeaderComponent,
    FileCropperComponent,
    FullImageComponent,
    CommaSeparatorPipe,
    ImageCropperModule,
    IconsModule
  ],
  providers: [
    NgxMaskPipe
  ]
})
export class SharedModuleModule { }
