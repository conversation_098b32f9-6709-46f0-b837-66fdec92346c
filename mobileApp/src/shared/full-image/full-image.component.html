<ion-content>
  <div class="header-section">
    <img class="header-bg" src="/assets/images/icons/common-header.png" alt="header background" />
  </div>

  <!-- Image Preview Container -->
  <div class="image-preview-container">
    <span class="image-text">{{fileName}}</span>
    <div class="full-image-container margin-top-25">
      <img [src]="imageUrl" alt="Full-Screen Image" class="full-screen-image">
      <div class="delete-icon-container" *ngIf="showDelete" (click)="removeImage()">
        <ion-icon class="delete-icon" src="/assets/images/svg/delete-icon.svg"></ion-icon>
      </div>
    </div>
    <div>
      <ion-button class="margin-top-50" expand="full" shape="round" type="submit" (click)="close()">
        Back
      </ion-button>
    </div>
  </div>

</ion-content>