import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { AuthService } from './authservice';
import { EventService } from './event.service';

@Injectable()
export class HttpAuthInterceptor implements HttpInterceptor {

  constructor(private readonly eventService: EventService,
    private readonly authService: AuthService) {
  }

  intercept(req: HttpRequest<any>, next: HttpHand<PERSON>): Observable<HttpEvent<any>> {
    const token = this.authService.getToken();
    const headers = {} as any;
    const startUrl = environment.BaseApiUrl + '/app/';
    const isAuthorizationRequest = req.url.startsWith(startUrl);
    if (token?.expires && new Date(token.expires).getTime() > new Date().getTime()
      && !isAuthorizationRequest) {
      headers.Authorization = 'Bearer ' + token.accessToken;
    }
    const authReq = req.clone({ setHeaders: headers });
    return next.handle(authReq).pipe(catchError((error) => {
      if (error.status === 401 || error.error.message === 'Un-Authorized Request') {
        this.authService.logout();
        this.eventService.publish({ key: 'http:forbidden', value: 'Session Expired' });
      }
      return Promise.reject(error.error);
    }));
  }
}
