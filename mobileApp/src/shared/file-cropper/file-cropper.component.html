<div class="image-cropper-container" *ngIf="isOpenCrop">
  <image-cropper [resizeToWidth]="600" [imageBase64]="request.imageBase64" [maintainAspectRatio]="true"
    [aspectRatio]="1 / 1" format="png" (imageCropped)="imageCropped($event)">
  </image-cropper>
  <div class="action-button-section">
    <ion-button class="site-full-rounded-button text-capitalize primary-button no-margin" shape="round" type="button"
      [disabled]="!isImageReady" (click)="upload()">
      Continue
    </ion-button>
  </div>
</div>