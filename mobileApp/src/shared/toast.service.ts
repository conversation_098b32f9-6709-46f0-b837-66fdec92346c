import { Injectable } from '@angular/core';
import { ToastController } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class ToastService {

  constructor(private toastController: ToastController) {
  }

  async show(message: string): Promise<void> {
    const toast = await this.toastController.create({
      message: message,
      duration: 1500,
      position: 'bottom',
      mode: 'md',
      animated: true,
      swipeGesture: "vertical"
    });

    await toast.present();
  }
}
