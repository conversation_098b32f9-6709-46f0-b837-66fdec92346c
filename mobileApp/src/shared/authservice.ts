import { Injectable } from '@angular/core';
import { EventService } from './event.service';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  constructor(private readonly localStorageService: LocalStorageService,
    private readonly eventService: EventService) { }

  getToken(): any {
    const token: any = this.localStorageService.getObject('temp-token');
    if (token) {
      return token;
    }
    const tempToken: any = this.localStorageService.getObject('token');
    return tempToken;
  }

  getUser() {
    const tempUser: any = this.localStorageService.getObject('temp-user');
    if (tempUser) {
      return tempUser;
    }
    const user: any = this.localStorageService.getObject('user');
    return user;
  }

  getRoles(): Array<string> {
    const user = this.getUser();
    if (!user) {
      return new Array<string>();
    }
    return user.roles;
  }

  isCustomer() {
    const result = this.hasRoles(["ROLE_CUSTOMER"]);
    return result;
  }

  isDriver() {
    const result = this.hasRoles(["ROLE_DRIVER"]) || this.hasRoles(["ROLE_OFFICE_ADMIN"]);
    return result;
  }

  logout() {
    const uuid = this.localStorageService.get("device-uuid");
    this.eventService.publish({ key: 'http:logout', value: uuid });
    this.localStorageService.remove("device-uuid");
    //  setTimeout(() => {
    this.localStorageService.remove('temp-token');
    this.localStorageService.remove('temp-user');
    this.localStorageService.remove('token');
    this.localStorageService.remove('user');
    this.localStorageService.remove('FUEL_EDIT_MODE');
    this.localStorageService.remove('FUEL_EDIT_ID');
    this.localStorageService.remove("SHIPPING_INFO");
    this.localStorageService.remove("SELECTED_CUSTOMER");
    this.localStorageService.remove("SHIPPING_MODE");
    this.localStorageService.remove("CARGO_DETAILS");
    this.localStorageService.remove("CARGO_MODE");
    this.localStorageService.remove("SPECIAL_REQUEST");
    this.localStorageService.remove("SHIPPING_CALCULATION_DETAILS");
    this.localStorageService.remove("SHIPPING_DOCUMENTS");
    this.localStorageService.remove("CUSTOMER_SHIPPING_PICKUP_DETAILS");
    this.localStorageService.remove("QUOTATION_INFO");
    this.localStorageService.remove("QUOTATION_PICKUP_DETAILS");
    this.localStorageService.remove("QUOTATION_MODE");
    this.localStorageService.remove("QUOTATION_CARGO_DETAILS");
    this.localStorageService.remove("QUOTATION_CARGO_MODE");
    this.localStorageService.remove("QUOTATION_SPECIAL_REQUEST");
    this.localStorageService.remove("QUOTATION_SHOW_CALCULATIONS");
    this.localStorageService.remove("QUOTATION_CALCULATION_DETAILS");
    this.localStorageService.remove("QUOTATION_DOCUMENTS");
    this.localStorageService.remove("UPDATE_CUSTOMER_PROFILE");
    this.localStorageService.remove("OTHER_SHIPMENT_DETAILS");
    this.localStorageService.remove("OTHER_QUOTATION_DETAILS");
    this.localStorageService.remove("CUSTOMER_DETAILS");
    //   this.localStorageService.clearAll();
    //  }, 500);
  }

  hasRoles(roles: Array<string>) {
    if (!roles || roles.length === 0) {
      return true;
    }
    const userRoles: Array<string> = this.getRoles();
    return roles.some((e) => {
      return userRoles.indexOf(e) >= 0;
    });
  }

  hasValidToken(): boolean {
    const token: any = this.getToken();
    if (!token) {
      return false;
    }
    return token.accessToken && token.expires_at && token.expires_at > new Date().getTime();
  }

  isAuthorizedUser(roles: Array<string>) {
    if (!this.hasValidToken()) {
      this.logout();
    }
    return { hasAccess: this.hasValidToken(), hasRoleAccess: roles.some(x => this.getRoles().indexOf(x) !== -1) };
  }
}
