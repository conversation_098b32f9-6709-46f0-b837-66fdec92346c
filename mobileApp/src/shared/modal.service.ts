import { Injectable } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  popover?: HTMLIonPopoverElement;

  constructor(public modalController: ModalController) {
  }

  async presentModal(inputComponent: any, data: any, iCssClass?: string) {
    const modal = await this.modalController.create({
      component: inputComponent,
      componentProps: data,
      cssClass: iCssClass
    });
    await modal.present();
    return modal;
  }

  dismiss(iComplete: boolean, iData: any) {
    this.modalController.dismiss({ complete: iComplete, data: iData });
  }

  onComplete(modal: any, callback: any) {
    if (modal && modal !== null) {
      modal.onWillDismiss().then((data: any) => {
        callback(data.data);
      });
    }
  }
}
