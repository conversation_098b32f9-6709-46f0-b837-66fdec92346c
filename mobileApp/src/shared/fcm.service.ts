import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Device } from '@capacitor/device';
import { PushNotifications } from '@capacitor/push-notifications';
import { NavController, Platform } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from './auth.model';
import { EventService } from './event.service';
import { LocalStorageService } from './local-storage.service';
import { ToastService } from './toast.service';
import { AuthService } from './authservice';
import { FCMToken } from './fcm-token-plugin';

@Injectable({
  providedIn: 'root'
})
export class FcmService {

  constructor(private router: Router,
    private toastService: ToastService,
    private readonly loadingService: LoadingService,
    private dataService: DataService,
    private readonly navController: NavController,
    private eventService: EventService,
    private localStorageService: LocalStorageService,
    private authService: AuthService,
    private platform: Platform) { }

  async initializeFirebaseToken() {
    let permStatus = await PushNotifications.checkPermissions();
    if (permStatus.receive === 'prompt') {
      permStatus = await PushNotifications.requestPermissions();
    }
    if (permStatus.receive !== 'granted') {
      this.toastService.show("Sorry, Aplication will able to send you new job notification.");
      return;
    }
    await PushNotifications.register();
  }

  initializeNotificationReceiver() {
    PushNotifications.addListener(
      'registration',
      (token) => {
        console.log('My token: ' + token.value);

        // Check if this is a proper FCM token or APNS token
        if (this.platform.is('ios') && this.isAPNSToken(token.value)) {
          console.log('📱 iOS APNS token detected, waiting for FCM token from native side...');
          // Don't process APNS tokens on iOS, wait for FCM token
          // Listen for FCM tokens from native iOS
    if (this.platform.is('ios')) {
      this.setupIOSFCMTokenListener();
    }

          return;
        }

        console.log('🚀 Processing FCM token:', token.value);
        this.processToken(token.value);
      }
    );

    // Listen for FCM tokens from native iOS
    if (this.platform.is('ios')) {
      this.setupIOSFCMTokenListener();
    }

    PushNotifications.addListener(
      'registrationError',
      (error: any) => {
        console.log('Error: ' + error.error);
      });

    PushNotifications.addListener(
      'pushNotificationReceived',
      async (notification: any) => {
        console.log('Push received:');
        console.log(notification);
        if (!notification || !notification.data) {
          console.log("Failed to read content from notification");
          return;
        }
        this.eventService.publish({ key: 'fcm:notification', value: notification.data });
      }
    );

    PushNotifications.addListener(
      'pushNotificationActionPerformed',
      async (notification: any) => {
        console.log(`Push notification action performed ${notification.actionId}`);
        console.log(notification.notification);
        if (!notification || !notification.notification || !notification.notification.data) {
          return;
        }
        this.eventService.publish({ key: 'fcm:notification', value: notification.notification.data });
      }
    );
  }

  async processToken(token: string) {
    const uuid = this.localStorageService.get("device-uuid");
    if (uuid) {
      console.log("Device Token has been already saved");
      return;
    }
    try {
      const info = await Device.getId();
      const uuid: string = info.identifier;
      const data = {} as any;
      data.uuid = uuid;
      data.token = token;
      this.saveTokenOnServer(data);
    } catch (e) {
      console.log(e);
      this.toastService.show('Failed to get user device id');
    }
  }

  async saveTokenOnServer(data: any) {
    if (!this.authService.hasValidToken()) {
      return;
    }
    this.dataService.saveToken(data)
      .subscribe({
        next: (response: RestResponse) => {
          this.localStorageService.set("fcm-token", data.token);
          this.localStorageService.set("device-uuid", data.uuid);
        }, error: (error: any) => {
          console.log("error::", error.message);
          this.toastService.show(error.message);
        }
      })
  }

  // Helper method to detect APNS tokens (iOS native tokens are typically 64 hex characters)
  private isAPNSToken(token: string): boolean {
    // APNS tokens are typically 64 characters of hex (no colons or special chars)
    // FCM tokens contain colons and are much longer
    return token.length === 64 && /^[a-fA-F0-9]+$/.test(token);
  }

  // Setup iOS FCM token listener
  private setupIOSFCMTokenListener() {
    console.log('🎧 Setting up iOS FCM token listener...');
    console.log('🔍 Platform check - iOS:', this.platform.is('ios'));

    try {
      // Listen for FCM token events from native plugin
      FCMToken.addListener('fcmTokenReceived', async (data: { token: string }) => {
        console.log('🎯 *** FCM TOKEN RECEIVED FROM NATIVE iOS ***');
        console.log('🎯 Received FCM token from native iOS plugin:', data.token);
        console.log('📏 FCM token length:', data.token.length);
        console.log('🔍 Token starts with:', data.token.substring(0, 20) + '...');

        // Also call the handleNativeFCMToken method for additional processing
        this.handleNativeFCMToken(data.token);

        // For iOS FCM tokens, always send to server (bypass UUID check)
        try {
          const info = await Device.getId();
          const uuid: string = info.identifier;
          const tokenData = {
            uuid: uuid,
            token: data.token
          };

          console.log('📤 Sending iOS FCM token to server...');
          console.log('📋 Token data:', { uuid: uuid, tokenLength: data.token.length });
          this.saveTokenOnServer(tokenData);
        } catch (e) {
          console.log('❌ Error processing iOS FCM token:', e);
          this.toastService.show('Failed to get user device id');
        }
      });

      console.log('✅ iOS FCM token listener setup complete');
    } catch (error) {
      console.log('❌ Error setting up iOS FCM token listener:', error);
    }
  }

  // Method to handle FCM token from native iOS side
  public handleNativeFCMToken(fcmToken: string) {
    console.log('� *** HANDLE NATIVE FCM TOKEN CALLED ***');
    console.log('�🎯 Received FCM token from native iOS:', fcmToken);
    console.log('📏 FCM token length:', fcmToken.length);
    console.log('🔍 Token format check - contains colon:', fcmToken.includes(':'));
    console.log('� Token starts with:', fcmToken.substring(0, 20) + '...');
    console.log('🔍 Token ends with:', '...' + fcmToken.substring(fcmToken.length - 20));

    // Verify this is a proper FCM token (not APNS)
    if (this.isAPNSToken(fcmToken)) {
      console.log('⚠️ Warning: This appears to be an APNS token, not FCM token');
    } else {
      console.log('✅ Confirmed: This is a proper FCM token');
    }

    // Process the proper FCM token using regular flow
    this.processToken(fcmToken);
  }
}
