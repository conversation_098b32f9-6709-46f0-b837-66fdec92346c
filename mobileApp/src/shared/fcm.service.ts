import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Device } from '@capacitor/device';
import { PushNotifications } from '@capacitor/push-notifications';
import { NavController } from '@ionic/angular';
import { DataService } from 'src/services/data.service';
import { LoadingService } from 'src/services/loading.service';
import { RestResponse } from './auth.model';
import { EventService } from './event.service';
import { LocalStorageService } from './local-storage.service';
import { ToastService } from './toast.service';
import { AuthService } from './authservice';

@Injectable({
  providedIn: 'root'
})
export class FcmService {

  constructor(private router: Router,
    private toastService: ToastService,
    private readonly loadingService: LoadingService,
    private dataService: DataService,
    private readonly navController: NavController,
    private eventService: EventService,
    private localStorageService: LocalStorageService,
    private authService: AuthService) { }

  async initializeFirebaseToken() {
    let permStatus = await PushNotifications.checkPermissions();
    if (permStatus.receive === 'prompt') {
      permStatus = await PushNotifications.requestPermissions();
    }
    if (permStatus.receive !== 'granted') {
      this.toastService.show("Sorry, Aplication will able to send you new job notification.");
      return;
    }
    await PushNotifications.register();
  }

  initializeNotificationReceiver() {
    PushNotifications.addListener(
      'registration',
      (token) => {
        console.log('My token: ' + token.value);
        this.processToken(token.value);
      }
    );

    PushNotifications.addListener(
      'registrationError',
      (error: any) => {
        console.log('Error: ' + error.error);
      });

    PushNotifications.addListener(
      'pushNotificationReceived',
      async (notification: any) => {
        console.log('Push received:');
        console.log(notification);
        if (!notification || !notification.data) {
          console.log("Failed to read content from notification");
          return;
        }
        this.eventService.publish({ key: 'fcm:notification', value: notification.data });
      }
    );

    PushNotifications.addListener(
      'pushNotificationActionPerformed',
      async (notification: any) => {
        console.log(`Push notification action performed ${notification.actionId}`);
        console.log(notification.notification);
        if (!notification || !notification.notification || !notification.notification.data) {
          return;
        }
        this.eventService.publish({ key: 'fcm:notification', value: notification.notification.data });
      }
    );
  }

  async processToken(token: string) {
    const uuid = this.localStorageService.get("device-uuid");
    if (uuid) {
      console.log("Device Token has been already saved");
      return;
    }
    try {
      const info = await Device.getId();
      const uuid: string = info.identifier;
      const data = {} as any;
      data.uuid = uuid;
      data.token = token;
      this.saveTokenOnServer(data);
    } catch (e) {
      console.log(e);
      this.toastService.show('Failed to get user device id');
    }
  }

  async saveTokenOnServer(data: any) {
    if (!this.authService.hasValidToken()) {
      return;
    }
    this.dataService.saveToken(data)
      .subscribe({
        next: (response: RestResponse) => {
          this.localStorageService.set("fcm-token", data.notificationToken);
          this.localStorageService.set("device-uuid", data.deviceId);
        }, error: (error: any) => {
          console.log("error::", error.message);
          this.toastService.show(error.message);
        }
      })
  }
}
