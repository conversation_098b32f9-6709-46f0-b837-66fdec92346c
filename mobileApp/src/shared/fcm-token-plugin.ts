import { registerPlugin } from '@capacitor/core';
import type { PluginListenerHandle } from '@capacitor/core';

export interface FCMTokenPlugin {
  getFCMToken(): Promise<{ token: string }>;
  addListener(
    eventName: 'fcmTokenReceived',
    listenerFunc: (data: { token: string }) => void,
  ): Promise<PluginListenerHandle> & PluginListenerHandle;
}

export const FCMToken = registerPlugin<FCMTokenPlugin>('FCMTokenPlugin');
