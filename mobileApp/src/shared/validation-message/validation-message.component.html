<div class="error-message" *ngIf="isFieldInvalid">
  <div *ngIf="field?.errors?.required">
    {{fieldErrorMessage || 'Please provide a valid value.'}}
  </div>
  <div *ngIf="field?.errors?.pattern">
    {{customPatternMessage || panCardPatternMessage || 'Please provide a valid value.'}}
  </div>
  <div style="margin-top: 10px;" *ngIf="field?.errors?.minlength">
    Please provide a valid value. Min allowed length: {{field.errors.minlength.requiredLength}}.
  </div>
  <div *ngIf="field?.errors?.maxlength || field?.errors?.customMax">
    Please provide a valid value. Max allowed length: {{field?.errors?.maxlength?.requiredLength ||
    field?.errors?.customMax.max}}
  </div>
  <div *ngIf="field?.errors?.email">
    Please provide a valid email address.
  </div>
  <div *ngIf="field?.errors?.mobile">
    Please provide a valid mobile number.
  </div>
  <div *ngIf="field?.errors?.mask">
    {{getMaskErrorMessage()}}
  </div>
  <div *ngIf="field?.errors?.incorrect">
    Please provide a valid value.
  </div>
  <div *ngIf="field?.errors?.customMinAmount">
    Please provide a valid value. Minimum required: {{field?.errors?.minAmount}}
  </div>
  <div *ngIf="field?.errors?.customMaxAmount">
    Please provide a valid value. Maximum allowed: {{field?.errors?.maxAmount}}
  </div>
</div>

<div class="error-message" *ngIf="comparableField && onClickValidation && isFieldMismatch">
  Passwords do not match
</div>
