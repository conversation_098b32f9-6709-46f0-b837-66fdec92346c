import { NgModule } from '@angular/core';

import { FeatherModule } from 'angular-feather';
import {
  Camera, Github, Heart, ArrowLeft, Eye, EyeOff, X, MapPin, Navigation, Plus, Edit2, Info, XCircle, Check, CheckCircle, ChevronRight, Calendar, Clock, Trash2,
  Home, Tool, User, Search, Edit, Settings, CreditCard, HelpCircle, LogOut, Save, Flag, Briefcase, ChevronDown, Lock, DollarSign, Send, Image, MessageCircle, Filter,Bell
} from 'angular-feather/icons';

// Select some icons (use an object, not an array)
const icons = {
  Camera,
  Heart,
  Github,
  ArrowLeft,
  Eye,
  EyeOff,
  X,
  MapPin,
  Navigation,
  Plus,
  Edit2,
  Info,
  XCircle,
  Check,
  CheckCircle,
  ChevronRight,
  Calendar,
  Clock,
  Trash2,
  Home,
  Tool,
  User,
  Search,
  Edit,
  Settings,
  CreditCard,
  HelpCircle,
  LogOut,
  Save,
  Flag,
  Briefcase,
  ChevronDown,
  Lock,
  DollarSign,
  Send,
  Image,
  MessageCircle,
  Filter,
  Bell
};

@NgModule({
  imports: [
    FeatherModule.pick(icons)
  ],
  exports: [
    FeatherModule
  ]
})
export class IconsModule { }
