/* Container for the customer dashboard */
.customer-dashboard-page {
    --background: #FCFCFC;
    height: 100%;

    .customer-body-section {
        height: 100vh;
        display: inline-block;
        width: 100%;
        padding: 20px 20px 10px 20px !important;
        overflow-y: auto;

        .form-container {
            min-height: 250px;
        }

        &.home-page {
            .logistics-text {
                font-size: 17px;
                font-weight: bold;
            }

            .logistics-management-container {
                .logistics-management {
                    display: flex;
                    gap: 15px;
                }

                .total-shipment-container {
                    display: flex;
                    flex-direction: column;
                    background: white;
                    box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
                    border-radius: 25px;
                    padding: 25px 20px;
                    margin-bottom: auto;
                    align-items: center;
                    gap: 10px;

                    &.upcoming-shipments {
                        padding: 25px 0px;
                    }

                    &.progress-shipments {
                        padding: 25px 28px;
                    }

                    &.complete-shipments {
                        padding: 25px 12px;
                        margin-left: 10px;
                    }
                }

                .shipment-icon {
                    font-size: 60px;
                }

                .shipment-text {
                    font-size: 10px;
                    font-weight: 600;
                    margin-left: 11px;
                    margin-right: 11px;
                }

                .shipment-count {
                    font-size: 20px;
                    font-weight: bold;
                }
            }
        }

    }
}