{"object": {"artifacts": [{"kind": "xcframework", "packageRef": {"identity": "capacitor-swift-pm", "kind": "remoteSourceControl", "location": "https://github.com/ionic-team/capacitor-swift-pm.git", "name": "capacitor-swift-pm"}, "path": "/Users/<USER>/Documents/IOTASOL/bees-express/mobileApp/track-my-location/.build/artifacts/capacitor-swift-pm/Cordova/Cordova.xcframework", "source": {"checksum": "082a11346f21e1be71f17cb19316ec25907f0af93d9521e4c4f50cc8a9bb7ab3", "type": "remote", "url": "https://github.com/ionic-team/capacitor-swift-pm/releases/download/7.4.2/Cordova.xcframework.zip"}, "targetName": "Cordova"}, {"kind": "xcframework", "packageRef": {"identity": "capacitor-swift-pm", "kind": "remoteSourceControl", "location": "https://github.com/ionic-team/capacitor-swift-pm.git", "name": "capacitor-swift-pm"}, "path": "/Users/<USER>/Documents/IOTASOL/bees-express/mobileApp/track-my-location/.build/artifacts/capacitor-swift-pm/Capacitor/Capacitor.xcframework", "source": {"checksum": "a312d9ea41cf5b00fb27a5592f84372868507b39ef13ac5a6dfb32d70d75fd85", "type": "remote", "url": "https://github.com/ionic-team/capacitor-swift-pm/releases/download/7.4.2/Capacitor.xcframework.zip"}, "targetName": "Capacitor"}], "dependencies": [{"basedOn": null, "packageRef": {"identity": "capacitor-swift-pm", "kind": "remoteSourceControl", "location": "https://github.com/ionic-team/capacitor-swift-pm.git", "name": "capacitor-swift-pm"}, "state": {"checkoutState": {"revision": "3fdb4ac03e3c20e86dd9b6feb15d0b129a6ee987", "version": "7.4.2"}, "name": "sourceControlCheckout"}, "subpath": "capacitor-swift-pm"}]}, "version": 6}