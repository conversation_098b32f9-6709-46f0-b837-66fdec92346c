#!/usr/bin/env bash -eoux pipefail

build_capacitor_simulator() {
	xcodebuild archive \
		-scheme Capacitor \
		-workspace Capacitor.xcworkspace \
		-destination "generic/platform=iOS Simulator" \
		-configuration Debug \
		-archivePath ./Build/iOS-Simulator \
		SKIP_INSTALL=NO \
		BUILD_LIBRARY_FOR_DISTRIBUTION=YES \
		DEBUG_INFORMATION_FORMAT="dwarf-with-dsym"
}

build_capacitor_ios() {
	xcodebuild archive \
		-scheme Capacitor \
		-workspace Capacitor.xcworkspace \
		-destination "generic/platform=iOS" \
		-archivePath ./Build/iOS \
		SKIP_INSTALL=NO \
		SWIFT_SERIALIZE_DEBUGGING_OPTIONS=NO \
		DEBUG_INFORMATION_FORMAT="dwarf-with-dsym" \
		BUILD_LIBRARY_FOR_DISTRIBUTION=YES
}

create_xcframeworks() {
	build_capacitor_simulator
	build_capacitor_ios

	rm -rf ./Build/iOS-Simulator.xcarchive/Products/Library/Frameworks/Capacitor.framework/Frameworks
	rm -rf ./Build/iOS.xcarchive/Products/Library/Frameworks/Capacitor.framework/Frameworks

	xcodebuild -create-xcframework \
		-framework ./Build/iOS-Simulator.xcarchive/Products/Library/Frameworks/Capacitor.framework \
		-debug-symbols $PWD/Build/iOS-Simulator.xcarchive/dSYMs/Capacitor.framework.dSYM \
		-framework ./Build/iOS.xcarchive/Products/Library/Frameworks/Capacitor.framework \
		-debug-symbols $PWD/Build/iOS.xcarchive/dSYMs/Capacitor.framework.dSYM \
		-output Capacitor.xcframework

	xcodebuild -create-xcframework \
		-framework ./Build/iOS-Simulator.xcarchive/Products/Library/Frameworks/Cordova.framework \
		-debug-symbols $PWD/Build/iOS-Simulator.xcarchive/dSYMs/Cordova.framework.dSYM \
		-framework ./Build/iOS.xcarchive/Products/Library/Frameworks/Cordova.framework \
		-debug-symbols $PWD/Build/iOS.xcarchive/dSYMs/Cordova.framework.dSYM \
		-output Cordova.xcframework

}

git clone https://github.com/ionic-team/capacitor capacitor-checkout
cd capacitor-checkout/ios/Capacitor
git checkout $1

create_xcframeworks

mv Capacitor.xcframework ../../../
mv Cordova.xcframework ../../../
