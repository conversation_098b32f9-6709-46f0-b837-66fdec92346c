name: Pre-Release

on:
  push:
    branches:
      - "release/**"

jobs:
  release-version:
    runs-on: ubuntu-latest
    outputs:
      release-version: ${{ steps.step1.outputs.release_version }}
    steps:
      - id: step1
        run: echo "release_version=${GITHUB_REF#refs/*/*/}" >> $GITHUB_OUTPUT
  create-pre-release:
    needs: release-version
    uses: ./.github/workflows/build-and-release.yaml
    with:
      release-version: ${{ needs.release-version.outputs.release-version }}
      draft-flag: -draft
    secrets: inherit
