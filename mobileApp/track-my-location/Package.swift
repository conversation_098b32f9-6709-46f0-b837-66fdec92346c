// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "TrackMyLocation",
    platforms: [.iOS(.v14)],
    products: [
        .library(
            name: "TrackMyLocation",
            targets: ["TrackMyLocationPlugin"])
    ],
    dependencies: [
        .package(url: "https://github.com/ionic-team/capacitor-swift-pm.git", from: "7.0.0")
    ],
    targets: [
        .target(
            name: "TrackMyLocationPlugin",
            dependencies: [
                .product(name: "Capacitor", package: "capacitor-swift-pm"),
                .product(name: "Cordova", package: "capacitor-swift-pm")
            ],
            path: "ios/Sources/TrackMyLocationPlugin"),
        .testTarget(
            name: "TrackMyLocationPluginTests",
            dependencies: ["TrackMyLocationPlugin"],
            path: "ios/Tests/TrackMyLocationPluginTests")
    ]
)