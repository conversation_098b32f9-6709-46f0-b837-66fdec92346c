import { WebPlugin } from '@capacitor/core';

import type { TrackMyLocationPlugin } from './definitions';

export class TrackMyLocationWeb extends WebPlugin implements TrackMyLocationPlugin {
  // async echo(options: { value: string }): Promise<{ value: string }> {
  //   console.log('ECHO', options);
  //   return options;
  // }

  // async sendToken(options: { token: string; }): Promise<{ value: string; }> {
  //   // alert(options.msg);
  //   return {value: options.token}
  // }

  // async trackMyLocation(options: { isTracking: boolean; }): Promise<{ value: boolean; }> {
  //   // alert(options.msg);
  //   return {value: options.isTracking}
  // }

  async sendObject(options: { userPayload: object; }): Promise<{ value: object; }> {
    return {value: options.userPayload}
  }

  async stopService(): Promise<{ message: string }> {
    console.log('Location service stopped (web implementation)');
    return { message: 'Location service stopped successfully' };
  }

  async isServiceRunning(): Promise<{ isRunning: boolean }> {
    console.log('Checking if location service is running (web implementation)');
    return { isRunning: false };
  }
  

  // sendToken(options: { token: string; }): Promise<{ value: string; }> {
  //   return {value: options.token}
  // }
}
