declare module "@capacitor/core" {
  interface PluginRegistery {
    TrackMyLocationPlugin: TrackMyLocationPlugin 
  }
}

export interface TrackMyLocationPlugin {
  // echo(options: { value: string }): Promise<{ value: string }>;
  // sendToken(options: {token: string}): Promise<{ value: string }>;
  // trackMyLocation(options: {isTracking: boolean}): Promise<{ value: boolean }>;
  sendObject(options: {userPayload: object}): Promise<{ value: object }>;
  stopService(): Promise<{ message: string }>;
  isServiceRunning(): Promise<{ isRunning: boolean }>;
}
