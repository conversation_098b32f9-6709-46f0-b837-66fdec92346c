import Foundation
import CoreLocation
import UserNotifications

public struct UserPayload {
    let authToken: String
    let isTracking: Bool
    let meetingId: String?
    let type: String?
}

@objc public class TrackMyLocation: NSObject {
    private var locationManager: CLLocationManager?
    private var authToken: String = ""
    private var isTracking: Bool = false
    private var meetingId: String? = nil
    private var type: String? = nil
    private let locationUpdateInterval: TimeInterval = 30.0 // 30 seconds
    private var lastLocationUpdate: Date = Date()

    override init() {
        super.init()
        setupLocationManager()
    }

    private func setupLocationManager() {
        print("🔧 Setting up location manager...")
        locationManager = CLLocationManager()
        locationManager?.delegate = self
        locationManager?.desiredAccuracy = kCLLocationAccuracyBest
        locationManager?.distanceFilter = 10 // Update when moved 10 meters

        print("✅ Location manager configured:")
        print("   - Delegate: \(locationManager?.delegate != nil ? "Set" : "Not set")")
        print("   - Desired accuracy: \(locationManager?.desiredAccuracy ?? 0)")
        print("   - Distance filter: \(locationManager?.distanceFilter ?? 0)")

        // Request notification permissions
        requestNotificationPermissions()
    }

    private func debugLocationManagerState() {
        guard let locationManager = locationManager else {
            print("❌ Location manager is nil")
            return
        }

        print("🔍 Location Manager Debug Info:")
        print("   - Location services enabled: \(CLLocationManager.locationServicesEnabled())")
        print("   - Authorization status: \(locationManager.authorizationStatus.rawValue)")
        print("   - Delegate set: \(locationManager.delegate != nil)")
        print("   - Desired accuracy: \(locationManager.desiredAccuracy)")
        print("   - Distance filter: \(locationManager.distanceFilter)")
        print("   - Is updating location: \(locationManager.location != nil)")

        if #available(iOS 9.0, *) {
            print("   - Background updates allowed: \(locationManager.allowsBackgroundLocationUpdates)")
            print("   - Pauses automatically: \(locationManager.pausesLocationUpdatesAutomatically)")
        }
    }

    private func requestNotificationPermissions() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            if let error = error {
                print("Notification permission error: \(error)")
            }
        }
    }

    public func startLocationTracking(authToken: String, isTracking: Bool, meetingId: String?, type: String?, completion: @escaping (Bool, String?) -> Void) {
        print("🚀 Starting location tracking...")
        self.authToken = authToken
        self.isTracking = isTracking
        self.meetingId = meetingId
        self.type = type

        // Save all parameters to UserDefaults for persistence
        UserDefaults.standard.set(authToken, forKey: "auth_token")
        UserDefaults.standard.set(meetingId, forKey: "meeting_id")
        UserDefaults.standard.set(type, forKey: "type")
        UserDefaults.standard.synchronize()

        print("💾 Saved parameters:")
        print("   - Auth Token: \(authToken.isEmpty ? "EMPTY" : String(authToken.prefix(10)) + "...")")
        print("   - Meeting ID: \(meetingId ?? "nil")")
        print("   - Type: \(type ?? "nil")")

        guard let locationManager = locationManager else {
            print("❌ Location manager not initialized")
            completion(false, "Location manager not initialized")
            return
        }

        // Debug current state
        debugLocationManagerState()

        // Check location authorization
        let currentStatus = locationManager.authorizationStatus
        print("🔐 Current authorization status: \(currentStatus.rawValue)")

        switch currentStatus {
        case .notDetermined:
            print("📱 Requesting always authorization...")
            locationManager.requestAlwaysAuthorization()
            completion(true, nil) // Will handle in delegate
        case .denied, .restricted:
            print("❌ Location permission denied or restricted")
            completion(false, "Location permission denied")
        case .authorizedWhenInUse:
            print("⚠️ Only when-in-use - requesting always...")
            locationManager.requestAlwaysAuthorization()
            startLocationUpdates()
            completion(true, nil)
        case .authorizedAlways:
            print("✅ Always authorization - starting updates...")
            startLocationUpdates()
            completion(true, nil)
        @unknown default:
            print("❓ Unknown authorization status")
            completion(false, "Unknown location authorization status")
        }
    }

    private func startLocationUpdates() {
        guard let locationManager = locationManager else {
            print("❌ Location manager is nil")
            return
        }

        print("🔍 Checking location services...")
        print("Location services enabled: \(CLLocationManager.locationServicesEnabled())")
        print("Authorization status: \(locationManager.authorizationStatus.rawValue)")

        if CLLocationManager.locationServicesEnabled() {
            // Enable background location updates BEFORE starting updates
            if #available(iOS 9.0, *) {
                do {
                    locationManager.allowsBackgroundLocationUpdates = true
                    locationManager.pausesLocationUpdatesAutomatically = false
                    print("✅ Background location updates enabled")
                } catch {
                    print("❌ Failed to enable background updates: \(error)")
                }
            }

            locationManager.startUpdatingLocation()
            print("✅ Started location updates")

            // Show local notification that tracking started
            //showLocationTrackingNotification()

            print("Location tracking started with token: \(authToken)")
        } else {
            print("❌ Location services not enabled")
        }
    }

    private func showLocationTrackingNotification() {
        let content = UNMutableNotificationContent()
        content.title = "Location Tracking Active"
        content.body = "Tracking your location in the background"
        content.sound = .default

        let request = UNNotificationRequest(identifier: "location_tracking", content: content, trigger: nil)

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error showing notification: \(error)")
            }
        }
    }

    private func sendLocationToServer(location: CLLocation) {
        let savedToken = UserDefaults.standard.string(forKey: "auth_token") ?? authToken
        let savedMeetingId = UserDefaults.standard.string(forKey: "meeting_id") ?? meetingId
        let savedType = UserDefaults.standard.string(forKey: "type") ?? type

        print("🌐 ===== SENDING LOCATION TO SERVER =====")
        print("📍 Location: \(location.coordinate.latitude), \(location.coordinate.longitude)")
        print("🔑 Auth Token: \(savedToken.isEmpty ? "EMPTY" : String(savedToken.prefix(10)) + "...")")
        print("🆔 Meeting ID: \(savedMeetingId ?? "nil")")
        print("🏷️ Type: \(savedType ?? "nil")")
        print("⏰ Timestamp: \(Date())")

        guard let url = URL(string: "https://bees-express-dev.azurewebsites.net/api/driverlocation") else {
            print("❌ Invalid URL")
            return
        }

        print("🎯 Target URL: \(url.absoluteString)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(savedToken)", forHTTPHeaderField: "Authorization")
      request.setValue("utf-8", forHTTPHeaderField: "charset")
        request.timeoutInterval = 30.0

        let parameters: [String: Any] = [
            "latitude": String(location.coordinate.latitude),
            "longitude": String(location.coordinate.longitude),
            "meetingId": savedMeetingId ?? NSNull(),
            "type": savedType ?? NSNull()
        ]
        print("parameters: \(parameters)")

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: parameters, options: [])
            request.httpBody = jsonData

            if let jsonString = String(data: jsonData, encoding: .utf8) {
                print("📦 JSON Request Body: \(jsonString)")
            }
        } catch {
            print("❌ Failed to serialize JSON: \(error)")
            return
        }

        print(" Headers: \(request.allHTTPHeaderFields ?? [:])")
        print("⏱️ Timeout: \(request.timeoutInterval) seconds")
        print("🚀 Making HTTP request...")

        let startTime = Date()
        URLSession.shared.dataTask(with: request) { data, response, error in
            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)

            print("⏱️ Request completed in \(String(format: "%.2f", duration)) seconds")

            if let error = error {
                print("❌ Network Error: \(error.localizedDescription)")
                print("❌ Error Details: \(error)")
                return
            }

            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ Invalid response type")
                return
            }

            print("📊 HTTP Status Code: \(httpResponse.statusCode)")
            print("📋 Response Headers: \(httpResponse.allHeaderFields)")

            if let data = data {
                print("📦 Response Data Size: \(data.count) bytes")
                if let responseString = String(data: data, encoding: .utf8) {
                    print("📄 Response Body: \(responseString)")
                } else {
                    print("❌ Could not decode response as UTF-8")
                }
            } else {
                print("⚠️ No response data received")
            }

            if httpResponse.statusCode == 200 {
                print("✅ Location sent successfully!")
            } else {
                print("❌ Server returned error status: \(httpResponse.statusCode)")

                // Log common HTTP status codes
                switch httpResponse.statusCode {
                case 400:
                    print("❌ Bad Request - Check request format")
                case 401:
                    print("❌ Unauthorized - Check auth token")
                case 403:
                    print("❌ Forbidden - Access denied")
                case 404:
                    print("❌ Not Found - Check API endpoint")
                case 500:
                    print("❌ Internal Server Error")
                default:
                    print("❌ HTTP Error: \(httpResponse.statusCode)")
                }
            }

            print("🌐 ===== END SERVER REQUEST =====\n")

        }.resume()
    }

    public func stopLocationTracking() {
        print("🛑 Stopping location tracking...")
        locationManager?.stopUpdatingLocation()
        if #available(iOS 9.0, *) {
            locationManager?.allowsBackgroundLocationUpdates = false
        }

        // Clear all saved parameters
        UserDefaults.standard.removeObject(forKey: "auth_token")
        UserDefaults.standard.removeObject(forKey: "meeting_id")
        UserDefaults.standard.removeObject(forKey: "type")
        UserDefaults.standard.synchronize()

        print("✅ Location tracking stopped")
    }

    // Debug method to request a single location update
    public func requestSingleLocationUpdate() {
        guard let locationManager = locationManager else {
            print("❌ Location manager not available for single update")
            return
        }

        print("🎯 Requesting single location update...")
        debugLocationManagerState()
        locationManager.requestLocation()
    }
}

// MARK: - CLLocationManagerDelegate
extension TrackMyLocation: CLLocationManagerDelegate {
    public func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        print("🎯 didUpdateLocations called with \(locations.count) locations")

        guard let location = locations.last else {
            print("❌ No location in locations array")
            return
        }

        print("📍 Raw location: \(location.coordinate.latitude), \(location.coordinate.longitude)")
        print("📍 Location accuracy: \(location.horizontalAccuracy)m")
        print("📍 Location timestamp: \(location.timestamp)")

        // Throttle location updates to 30 seconds interval
        let now = Date()
        let timeSinceLastUpdate = now.timeIntervalSince(lastLocationUpdate)
        print("⏱️ Time since last update: \(timeSinceLastUpdate) seconds")

//        if timeSinceLastUpdate < locationUpdateInterval {
//            print("⏭️ Skipping update - too soon (need \(locationUpdateInterval) seconds)")
//            return
//        }
        lastLocationUpdate = now

        print("✅ Processing location update: \(location.coordinate.latitude), \(location.coordinate.longitude)")

        // Send location to server
        sendLocationToServer(location: location)

        // Show local notification with location (for testing)
       // showLocationUpdateNotification(location: location)
    }

    public func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("Location manager failed with error: \(error)")
    }

    public func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        print("🔐 Location authorization changed to: \(status.rawValue)")

        let statusString: String
        switch status {
        case .notDetermined:
            statusString = "notDetermined"
            print("📱 Requesting always authorization...")
            manager.requestAlwaysAuthorization()
        case .denied:
            statusString = "denied"
            print("❌ Location access denied")
        case .restricted:
            statusString = "restricted"
            print("❌ Location access restricted")
        case .authorizedWhenInUse:
            statusString = "authorizedWhenInUse"
            print("⚠️ Only when-in-use authorization - requesting always...")
            manager.requestAlwaysAuthorization()
            if isTracking {
                startLocationUpdates()
            }
        case .authorizedAlways:
            statusString = "authorizedAlways"
            print("✅ Always authorization granted")
            if isTracking {
                startLocationUpdates()
            }
        @unknown default:
            statusString = "unknown"
            print("❓ Unknown authorization status")
        }

        print("🔐 Authorization status: \(statusString)")
    }

    private func showLocationUpdateNotification(location: CLLocation) {
        let content = UNMutableNotificationContent()
        content.title = "Location Update"
        content.body = "Lat: \(String(format: "%.6f", location.coordinate.latitude)), Lng: \(String(format: "%.6f", location.coordinate.longitude))"
        content.sound = .default

        let request = UNNotificationRequest(identifier: "location_update_\(Date().timeIntervalSince1970)", content: content, trigger: nil)

        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Error showing location notification: \(error)")
            }
        }
    }
}
