import Foundation
import Capacitor
import CoreLocation

/**
 * Please read the Capacitor iOS Plugin Development Guide
 * here: https://capacitorjs.com/docs/plugins/ios
 */
@objc(TrackMyLocationPlugin)
public class TrackMyLocationPlugin: CAPPlugin, CAPBridgedPlugin {
    public let identifier = "TrackMyLocationPlugin"
    public let jsName = "TrackMyLocation"
    public let pluginMethods: [CAPPluginMethod] = [
        CAPPluginMethod(name: "sendObject", returnType: CAPPluginReturnPromise),
        CAPPluginMethod(name: "stopLocationTracking", returnType: CAPPluginReturnPromise),
        CAPPluginMethod(name: "requestSingleLocationUpdate", returnType: CAPPluginReturnPromise)
    ]
    private let implementation = TrackMyLocation()

    @objc func sendObject(_ call: CAPPluginCall) {
        guard let userPayload = call.getObject("userPayload") else {
            call.reject("userPayload is missing")
            return
        }

        guard let authToken = userPayload["authToken"] as? String, !authToken.isEmpty else {
            call.reject("Invalid authToken")
            return
        }

        let isTracking = userPayload["isTracking"] as? Bool ?? false
        let meetingId = userPayload["meetingId"] as? String
        let type = userPayload["type"] as? String

        // Start location tracking with the provided payload
        implementation.startLocationTracking(authToken: authToken, isTracking: isTracking, meetingId: meetingId, type: type) { [weak self] success, error in
            DispatchQueue.main.async {
                if success {
                    call.resolve([
                        "message": "Location tracking started with userPayload"
                    ])
                } else {
                    call.reject(error ?? "Failed to start location tracking")
                }
            }
        }
    }

    @objc func stopLocationTracking(_ call: CAPPluginCall) {
        implementation.stopLocationTracking()
        call.resolve([
            "message": "Location tracking stopped"
        ])
    }

    @objc func requestSingleLocationUpdate(_ call: CAPPluginCall) {
        implementation.requestSingleLocationUpdate()
        call.resolve([
            "message": "Single location update requested"
        ])
    }
}
