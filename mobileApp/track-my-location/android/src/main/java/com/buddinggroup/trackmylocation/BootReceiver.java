package com.buddinggroup.trackmylocation;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

public class BootReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent.getAction().equals(Intent.ACTION_BOOT_COMPLETED)) {
            Log.d("BootReceiver", "Boot completed. Restarting service.");
            Intent serviceIntent = new Intent(context, LocationService.class);
            context.startService(serviceIntent);  // Restart your service after reboot
        }
    }
}
