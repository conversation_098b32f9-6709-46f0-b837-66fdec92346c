package com.buddinggroup.trackmylocation;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationManager;
import android.os.Build;
import android.os.IBinder;
import android.provider.Settings;
import android.util.Log;
import android.widget.Toast;

import androidx.core.app.NotificationCompat;
import androidx.core.app.ActivityCompat;

import com.buddinggroup.trackmylocation.UserPayload;
import com.google.android.gms.location.*;

import java.io.IOException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class LocationService extends Service {

    private static final String TAG = "LocationService";
    private static final String CHANNEL_ID = "location_service_channel";
    private FusedLocationProviderClient fusedLocationClient;
    private LocationCallback locationCallback;
    private static String authToken = "";
    private static String meetingId = "";
    private static String type = "";

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "Service Created===="+isGPSEnabled());

        // Check if GPS is enabled
        if (!isGPSEnabled()) {
            showEnableLocationDialog(); // Show dialog if GPS is not enabled
            return;
        }

        // Setup location services
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
        locationCallback = new LocationCallback() {
            @Override
            public void onLocationResult(LocationResult locationResult) {
                if (locationResult == null) return;

                for (Location location : locationResult.getLocations()) {
                    Log.d(TAG, "Location: " + location.getLatitude() + ", " + location.getLongitude());

                    // Show location in Toast for testing
                    //showLocationToast(location.getLatitude(), location.getLongitude());

                    sendLocationToServer(location);
                }
            }
        };

        // Start location updates
        requestLocationUpdates();

        // Start foreground service
        startForegroundNotification();
    }

        @Override
        public int onStartCommand(Intent intent, int flags, int startId) {
            if (intent != null) {
                UserPayload userPayload = (UserPayload) intent.getSerializableExtra("userPayload");

                if (userPayload != null) {
                    authToken = userPayload.getAuthToken();
                    meetingId = userPayload.getMeetingId();
                    type = userPayload.getType();
                    saveTokenToPreferences(authToken); // Save token persistently
                    saveMeetingIdToPreferences(meetingId); // Save meetingId persistently
                    saveTypeToPreferences(type); // Save token persistently
                    Log.d(TAG, "Received authToken: " + authToken);
                    Log.d(TAG, "Received meetingId: " + meetingId);
                    Log.d(TAG, "Received type: " + type);
                } else {
                    authToken = getTokenFromPreferences(); // Retrieve token if null
                    meetingId = getMeetingIdFromPreferences(); // Retrieve meetingId if null
                    type = getTypeFromPreferences(); // Retrieve meetingId if null
                    Log.d(TAG, "Retrieved saved authToken: " + authToken);
                    Log.d(TAG, "Retrieved saved meetingId: " + meetingId);
                    Log.d(TAG, "Retrieved saved type: " + type);
                }
            } else {
                authToken = getTokenFromPreferences();
                meetingId = getMeetingIdFromPreferences();
                type = getTypeFromPreferences();
                Log.d(TAG, "Intent is null, using stored authToken: " + authToken);
            }

            return START_STICKY;
        }


    @SuppressLint("MissingPermission")
    private void requestLocationUpdates() {
        LocationRequest locationRequest = LocationRequest.create()
                .setInterval(30000) // Update every 10 seconds
                .setFastestInterval(30000)
                .setPriority(Priority.PRIORITY_BALANCED_POWER_ACCURACY);  // GPS Only (no High Accuracy mode)

        fusedLocationClient.requestLocationUpdates(locationRequest, locationCallback, null);
    }

    @SuppressLint("ForegroundServiceType")
    private void startForegroundNotification() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID, "Location Tracking", NotificationManager.IMPORTANCE_HIGH);
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }

        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("Location Tracking Active")
                .setContentText("Tracking your location in the background")
                .setSmallIcon(android.R.drawable.ic_menu_mylocation)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .build();

        startForeground(1, notification);
    }

    private void showLocationToast(double latitude, double longitude) {
        // Show a Toast with the current location for testing
        String message = "Latitude: " + latitude + ", Longitude: " + longitude;
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

private void sendLocationToServer(Location location) {
    Log.d(TAG, "Received sendLocationToServer authToken: " + authToken);
    Log.d(TAG, "Received sendLocationToServer meetingId: " + meetingId);
    Log.d(TAG, "Received sendLocationToServer type: " + type);

    OkHttpClient client = new OkHttpClient();

    MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    // Create JSON request body
  String jsonBody = "{"
        + "\"latitude\": \"" + String.valueOf(location.getLatitude()) + "\","
        + "\"longitude\": \"" + String.valueOf(location.getLongitude()) + "\","
        + "\"meetingId\": " + (meetingId == null || meetingId.isEmpty() ? "null" : "\"" + meetingId + "\"")
        + "}";

    // Log the JSON payload
    Log.d(TAG, "API Request Payload: " + jsonBody);
    RequestBody requestBody = RequestBody.create(jsonBody, JSON);

    Request request = new Request.Builder()
            .url("https://bees-express-dev.azurewebsites.net/api/driverlocation")
            .post(requestBody)
            .addHeader("Authorization", "Bearer " + authToken)  // Auth header
            .addHeader("Content-Type", "application/json")  // Set JSON content type
            .build();

    client.newCall(request).enqueue(new Callback() {
        @Override
        public void onFailure(Call call, IOException e) {
            Log.e(TAG, "Failed to send location", e);
        }

        @Override
        public void onResponse(Call call, Response response) {
            try {
                // Read response body only once
                String responseBody = response.body() != null ? response.body().string() : "No response body";

                Log.d(TAG, "API Response Code: " + response.code());
                Log.d(TAG, "API Response Headers: " + response.headers().toString());
                Log.d(TAG, "API Response Body: " + responseBody);

                if (response.isSuccessful()) {
                    Log.d(TAG, "Location sent successfully.");
                } else {
                    Log.e(TAG, "API Response Error: Code " + response.code());
                }
            } catch (IOException e) {
                Log.e(TAG, "Error reading API response", e);
            } finally {
                response.close(); // Ensure response is closed to prevent memory leaks
            }
        }
    });
}


    // Check if GPS is enabled
    private boolean isGPSEnabled() {
        LocationManager locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
    }

    // Show alert dialog to enable GPS
    private void showEnableLocationDialog() {
        new AlertDialog.Builder(this)
                .setTitle("GPS Disabled")
                .setMessage("GPS is not enabled. Please enable it to continue tracking your location.")
                .setPositiveButton("Go to Settings", (dialog, which) -> {
                    // Open location settings screen to enable GPS
                    Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                    startActivity(intent);
                })
                .setNegativeButton("Cancel", (dialog, which) -> {
                    stopSelf(); // Stop the service if the user cancels
                })
                .setCancelable(false)
                .show();
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        
        // Remove location updates
        if (fusedLocationClient != null && locationCallback != null) {
            fusedLocationClient.removeLocationUpdates(locationCallback);
        }
        
        // Clear stored preferences
        clearAllPreferences();
        
        Log.d(TAG, "LocationService destroyed and cleaned up.");
    }

    private void clearAllPreferences() {
        getSharedPreferences("LocationServicePrefs", MODE_PRIVATE)
                .edit()
                .clear()
                .apply();
    }

    private void saveTokenToPreferences(String token) {
        getSharedPreferences("LocationServicePrefs", MODE_PRIVATE)
                .edit()
                .putString("auth_token", token)
                .apply();
    }

     private void saveMeetingIdToPreferences(String meeting_id) {
        getSharedPreferences("LocationServicePrefs", MODE_PRIVATE)
                .edit()
                .putString("meeting_id", meeting_id)
                .apply();
    }

    private void saveTypeToPreferences(String type) {
        getSharedPreferences("LocationServicePrefs", MODE_PRIVATE)
                .edit()
                .putString("type", type)
                .apply();
    }

private String getTokenFromPreferences() {
    return getSharedPreferences("LocationServicePrefs", MODE_PRIVATE)
            .getString("auth_token", "");
}

private String getMeetingIdFromPreferences() {
    return getSharedPreferences("LocationServicePrefs", MODE_PRIVATE)
            .getString("meeting_id", "");
}

private String getTypeFromPreferences() {
    return getSharedPreferences("LocationServicePrefs", MODE_PRIVATE)
            .getString("type", "");
}


private void clearTokenFromPreferences() {
    getSharedPreferences("LocationServicePrefs", MODE_PRIVATE)
            .edit()
            .remove("auth_token")
            .apply();
}

public static void stopService(Context context) {
    Intent intent = new Intent(context, LocationService.class);
    context.stopService(intent);
    Log.d(TAG, "LocationService has been stopped.");
}

public static boolean isServiceRunning(Context context) {
    android.app.ActivityManager manager = (android.app.ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
    for (android.app.ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
        if (LocationService.class.getName().equals(service.service.getClassName())) {
            return true;
        }
    }
    return false;
}
}
