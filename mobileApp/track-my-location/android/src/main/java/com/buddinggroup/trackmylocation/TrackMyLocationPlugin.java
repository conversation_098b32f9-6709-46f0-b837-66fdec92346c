package com.buddinggroup.trackmylocation;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

@CapacitorPlugin(name = "TrackMyLocation")
public class TrackMyLocationPlugin extends Plugin {

    @PluginMethod
    public void sendObject(PluginCall call) {
        JSObject userPayloadJson = call.getObject("userPayload");

        if (userPayloadJson == null) {
            call.reject("userPayload is missing");
            return;
        }

        String authToken = userPayloadJson.getString("authToken", "");
        boolean isTracking = userPayloadJson.getBoolean("isTracking", false);
        String meetingId = userPayloadJson.getString("meetingId", "");
        String type = userPayloadJson.getString("type", "");


        if (authToken.isEmpty()) {
            call.reject("Invalid authToken");
            return;
        }

        UserPayload userPayload = new UserPayload(authToken, isTracking, meetingId, type);
        Context context = getContext();
        Intent intent = new Intent(context, LocationService.class);
        intent.putExtra("userPayload", userPayload);  // Pass the Serializable object

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }

        JSObject ret = new JSObject();
        ret.put("message", "Location tracking started with userPayload");
        call.resolve(ret);
    }

    @PluginMethod
    public void stopService(PluginCall call) {
        try {
            Context context = getContext();
            LocationService.stopService(context);
            
            JSObject ret = new JSObject();
            ret.put("message", "Location tracking stopped successfully");
            call.resolve(ret);
        } catch (Exception e) {
            Log.e("TrackMyLocationPlugin", "Error stopping service: " + e.getMessage());
            call.reject("Error stopping location service: " + e.getMessage());
        }
    }

    @PluginMethod
    public void isServiceRunning(PluginCall call) {
        try {
            Context context = getContext();
            boolean isRunning = LocationService.isServiceRunning(context);
            
            JSObject ret = new JSObject();
            ret.put("isRunning", isRunning);
            call.resolve(ret);
        } catch (Exception e) {
            Log.e("TrackMyLocationPlugin", "Error checking service status: " + e.getMessage());
            call.reject("Error checking location service status: " + e.getMessage());
        }
    }
    
    
}
