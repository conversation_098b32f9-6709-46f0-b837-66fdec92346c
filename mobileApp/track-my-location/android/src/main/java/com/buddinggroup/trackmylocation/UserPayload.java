package com.buddinggroup.trackmylocation;
import java.io.Serializable;

public class UserPayload implements Serializable {
  private String authToken;
  private boolean isTracking;
  private String meetingId;
  private String type;

  public UserPayload(String authToken, boolean isTracking, String meetingId, String type) {
    this.authToken = authToken;
    this.isTracking = isTracking;
    this.meetingId = meetingId;
    this.type = type;
  }

  public String getAuthToken() { return authToken; }
  public boolean isTracking() { return isTracking; }
  public String getMeetingId() { return meetingId; }
  public String getType() { return type; }

}
