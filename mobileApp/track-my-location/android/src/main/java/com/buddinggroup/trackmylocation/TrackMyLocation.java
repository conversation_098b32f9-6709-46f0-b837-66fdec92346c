package com.buddinggroup.trackmylocation;

import static android.content.ContentValues.TAG;

import android.content.Intent;
import android.util.Log;

import com.getcapacitor.BridgeActivity;

public class TrackMyLocation extends BridgeActivity {


  public void sendObject(Object value) {
        //Log.i("trackMyLocation -------", String.valueOf(value));
    Log.d(TAG, "sendObject ------- : " + value);


  }



}
