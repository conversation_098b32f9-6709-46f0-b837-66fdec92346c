{"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["../../src/web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAI5C,MAAM,OAAO,kBAAmB,SAAQ,SAAS;IAC/C,uEAAuE;IACvE,kCAAkC;IAClC,oBAAoB;IACpB,IAAI;IAEJ,8EAA8E;IAC9E,2BAA2B;IAC3B,kCAAkC;IAClC,IAAI;IAEJ,2FAA2F;IAC3F,2BAA2B;IAC3B,uCAAuC;IACvC,IAAI;IAEJ,KAAK,CAAC,UAAU,CAAC,OAAiC;QAChD,OAAO,EAAC,KAAK,EAAE,OAAO,CAAC,WAAW,EAAC,CAAA;IACrC,CAAC;IAED,KAAK,CAAC,WAAW;QACf,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAC7D,OAAO,EAAE,OAAO,EAAE,uCAAuC,EAAE,CAAC;IAC9D,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;QAC5E,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;IAC9B,CAAC;CAMF", "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type { TrackMyLocationPlugin } from './definitions';\n\nexport class TrackMyLocationWeb extends WebPlugin implements TrackMyLocationPlugin {\n  // async echo(options: { value: string }): Promise<{ value: string }> {\n  //   console.log('ECHO', options);\n  //   return options;\n  // }\n\n  // async sendToken(options: { token: string; }): Promise<{ value: string; }> {\n  //   // alert(options.msg);\n  //   return {value: options.token}\n  // }\n\n  // async trackMyLocation(options: { isTracking: boolean; }): Promise<{ value: boolean; }> {\n  //   // alert(options.msg);\n  //   return {value: options.isTracking}\n  // }\n\n  async sendObject(options: { userPayload: object; }): Promise<{ value: object; }> {\n    return {value: options.userPayload}\n  }\n\n  async stopService(): Promise<{ message: string }> {\n    console.log('Location service stopped (web implementation)');\n    return { message: 'Location service stopped successfully' };\n  }\n\n  async isServiceRunning(): Promise<{ isRunning: boolean }> {\n    console.log('Checking if location service is running (web implementation)');\n    return { isRunning: false };\n  }\n  \n\n  // sendToken(options: { token: string; }): Promise<{ value: string; }> {\n  //   return {value: options.token}\n  // }\n}\n"]}