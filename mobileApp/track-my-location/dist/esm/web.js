import { WebPlugin } from '@capacitor/core';
export class TrackMyLocationWeb extends WebPlugin {
    // async echo(options: { value: string }): Promise<{ value: string }> {
    //   console.log('ECHO', options);
    //   return options;
    // }
    // async sendToken(options: { token: string; }): Promise<{ value: string; }> {
    //   // alert(options.msg);
    //   return {value: options.token}
    // }
    // async trackMyLocation(options: { isTracking: boolean; }): Promise<{ value: boolean; }> {
    //   // alert(options.msg);
    //   return {value: options.isTracking}
    // }
    async sendObject(options) {
        return { value: options.userPayload };
    }
    async stopService() {
        console.log('Location service stopped (web implementation)');
        return { message: 'Location service stopped successfully' };
    }
    async isServiceRunning() {
        console.log('Checking if location service is running (web implementation)');
        return { isRunning: false };
    }
}
//# sourceMappingURL=web.js.map