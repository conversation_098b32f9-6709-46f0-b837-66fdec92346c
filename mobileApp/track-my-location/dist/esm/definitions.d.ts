declare module "@capacitor/core" {
    interface PluginRegistery {
        TrackMyLocationPlugin: TrackMyLocationPlugin;
    }
}
export interface TrackMyLocationPlugin {
    sendObject(options: {
        userPayload: object;
    }): Promise<{
        value: object;
    }>;
    stopService(): Promise<{
        message: string;
    }>;
    isServiceRunning(): Promise<{
        isRunning: boolean;
    }>;
}
