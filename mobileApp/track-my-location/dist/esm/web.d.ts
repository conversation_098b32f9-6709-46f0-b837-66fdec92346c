import { WebPlugin } from '@capacitor/core';
import type { TrackMyLocationPlugin } from './definitions';
export declare class TrackMyLocationWeb extends WebPlugin implements TrackMyLocationPlugin {
    sendObject(options: {
        userPayload: object;
    }): Promise<{
        value: object;
    }>;
    stopService(): Promise<{
        message: string;
    }>;
    isServiceRunning(): Promise<{
        isRunning: boolean;
    }>;
}
