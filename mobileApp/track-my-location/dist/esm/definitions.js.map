{"version": 3, "file": "definitions.js", "sourceRoot": "", "sources": ["../../src/definitions.ts"], "names": [], "mappings": "", "sourcesContent": ["declare module \"@capacitor/core\" {\n  interface PluginRegistery {\n    TrackMyLocationPlugin: TrackMyLocationPlugin \n  }\n}\n\nexport interface TrackMyLocationPlugin {\n  // echo(options: { value: string }): Promise<{ value: string }>;\n  // sendToken(options: {token: string}): Promise<{ value: string }>;\n  // trackMyLocation(options: {isTracking: boolean}): Promise<{ value: boolean }>;\n  sendObject(options: {userPayload: object}): Promise<{ value: object }>;\n  stopService(): Promise<{ message: string }>;\n  isServiceRunning(): Promise<{ isRunning: boolean }>;\n}\n"]}