{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAIjD,MAAM,eAAe,GAAG,cAAc,CAAwB,iBAAiB,EAAE;IAC/E,GAAG,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,kBAAkB,EAAE,CAAC;CACnE,CAAC,CAAC;AAEH,cAAc,eAAe,CAAC;AAC9B,OAAO,EAAE,eAAe,EAAE,CAAC", "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { TrackMyLocationPlugin } from './definitions';\n\nconst TrackMyLocation = registerPlugin<TrackMyLocationPlugin>('TrackMyLocation', {\n  web: () => import('./web').then((m) => new m.TrackMyLocationWeb()),\n});\n\nexport * from './definitions';\nexport { TrackMyLocation };\n"]}