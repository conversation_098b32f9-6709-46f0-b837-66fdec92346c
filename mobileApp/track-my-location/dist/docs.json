{"api": {"name": "TrackMyLocationPlugin", "slug": "trackmylocationplugin", "docs": "", "tags": [], "methods": [{"name": "sendObject", "signature": "(options: { userPayload: object; }) => Promise<{ value: object; }>", "parameters": [{"name": "options", "docs": "", "type": "{ userPayload: object; }"}], "returns": "Promise<{ value: object; }>", "tags": [], "docs": "", "complexTypes": [], "slug": "sendobject"}, {"name": "stopService", "signature": "() => Promise<{ message: string; }>", "parameters": [], "returns": "Promise<{ message: string; }>", "tags": [], "docs": "", "complexTypes": [], "slug": "stopservice"}, {"name": "isServiceRunning", "signature": "() => Promise<{ isRunning: boolean; }>", "parameters": [], "returns": "Promise<{ isRunning: boolean; }>", "tags": [], "docs": "", "complexTypes": [], "slug": "isservicerunning"}], "properties": []}, "interfaces": [], "enums": [], "typeAliases": [], "pluginConfigs": []}