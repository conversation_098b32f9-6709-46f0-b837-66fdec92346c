'use strict';

var core = require('@capacitor/core');

const TrackMyLocation = core.registerPlugin('TrackMyLocation', {
    web: () => Promise.resolve().then(function () { return web; }).then((m) => new m.TrackMyLocationWeb()),
});

class TrackMyLocationWeb extends core.WebPlugin {
    // async echo(options: { value: string }): Promise<{ value: string }> {
    //   console.log('ECHO', options);
    //   return options;
    // }
    // async sendToken(options: { token: string; }): Promise<{ value: string; }> {
    //   // alert(options.msg);
    //   return {value: options.token}
    // }
    // async trackMyLocation(options: { isTracking: boolean; }): Promise<{ value: boolean; }> {
    //   // alert(options.msg);
    //   return {value: options.isTracking}
    // }
    async sendObject(options) {
        return { value: options.userPayload };
    }
    async stopService() {
        console.log('Location service stopped (web implementation)');
        return { message: 'Location service stopped successfully' };
    }
    async isServiceRunning() {
        console.log('Checking if location service is running (web implementation)');
        return { isRunning: false };
    }
}

var web = /*#__PURE__*/Object.freeze({
    __proto__: null,
    TrackMyLocationWeb: TrackMyLocationWeb
});

exports.TrackMyLocation = TrackMyLocation;
//# sourceMappingURL=plugin.cjs.js.map
