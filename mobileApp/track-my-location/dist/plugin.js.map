{"version": 3, "file": "plugin.js", "sources": ["esm/index.js", "esm/web.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst TrackMyLocation = registerPlugin('TrackMyLocation', {\n    web: () => import('./web').then((m) => new m.TrackMyLocationWeb()),\n});\nexport * from './definitions';\nexport { TrackMyLocation };\n//# sourceMappingURL=index.js.map", "import { WebPlugin } from '@capacitor/core';\nexport class TrackMyLocationWeb extends WebPlugin {\n    // async echo(options: { value: string }): Promise<{ value: string }> {\n    //   console.log('ECHO', options);\n    //   return options;\n    // }\n    // async sendToken(options: { token: string; }): Promise<{ value: string; }> {\n    //   // alert(options.msg);\n    //   return {value: options.token}\n    // }\n    // async trackMyLocation(options: { isTracking: boolean; }): Promise<{ value: boolean; }> {\n    //   // alert(options.msg);\n    //   return {value: options.isTracking}\n    // }\n    async sendObject(options) {\n        return { value: options.userPayload };\n    }\n    async stopService() {\n        console.log('Location service stopped (web implementation)');\n        return { message: 'Location service stopped successfully' };\n    }\n    async isServiceRunning() {\n        console.log('Checking if location service is running (web implementation)');\n        return { isRunning: false };\n    }\n}\n//# sourceMappingURL=web.js.map"], "names": ["registerPlugin", "WebPlugin"], "mappings": ";;;AACK,UAAC,eAAe,GAAGA,mBAAc,CAAC,iBAAiB,EAAE;IAC1D,IAAI,GAAG,EAAE,MAAM,mDAAe,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,kBAAkB,EAAE,CAAC;IACtE,CAAC;;ICFM,MAAM,kBAAkB,SAASC,cAAS,CAAC;IAClD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,MAAM,UAAU,CAAC,OAAO,EAAE;IAC9B,QAAQ,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,WAAW,EAAE;IAC7C;IACA,IAAI,MAAM,WAAW,GAAG;IACxB,QAAQ,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC;IACpE,QAAQ,OAAO,EAAE,OAAO,EAAE,uCAAuC,EAAE;IACnE;IACA,IAAI,MAAM,gBAAgB,GAAG;IAC7B,QAAQ,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC;IACnF,QAAQ,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;IACnC;IACA;;;;;;;;;;;;;;;"}