import Foundation
import Capacitor
import FirebaseMessaging

@objc(FCMTokenPlugin)
public class FCMTokenPlugin: CAPPlugin {
    
    override public func load() {
        // Listen for FCM token notifications from AppDelegate
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleFCMToken(_:)),
            name: NSNotification.Name("FCMTokenReceived"),
            object: nil
        )
    }
    
    @objc func handleFCMToken(_ notification: Notification) {
        if let fcmToken = notification.object as? String {
            print("🔌 FCMTokenPlugin received token: \(fcmToken)")
            
            // Send token to JavaScript/Ionic
            self.notifyListeners("fcmTokenReceived", data: [
                "token": fcmToken
            ])
        }
    }
    
    @objc func getFCMToken(_ call: CAPPluginCall) {
        Messaging.messaging().token { token, error in
            if let error = error {
                call.reject("Error getting FCM token: \(error.localizedDescription)")
            } else if let token = token {
                call.resolve([
                    "token": token
                ])
            } else {
                call.reject("No FCM token available")
            }
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
