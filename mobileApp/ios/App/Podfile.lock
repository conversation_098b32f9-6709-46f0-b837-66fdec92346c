PODS:
  - Capacitor (7.2.0):
    - Capac<PERSON><PERSON>ordova
  - CapacitorCamera (7.0.1):
    - Capacitor
  - CapacitorCordova (7.2.0)
  - CapacitorDevice (7.0.1):
    - Capacitor
  - CapacitorGeolocation (7.1.2):
    - Capacitor
    - IONGeolocationLib (~> 1.0)
  - CapacitorMlkitBarcodeScanning (7.2.1):
    - Capacitor
    - GoogleMLKit/BarcodeScanning (= 7.0.0)
  - CapacitorNativeSettings (7.0.2):
    - Capacitor
  - CapacitorPushNotifications (7.0.2):
    - Capacitor
  - CapacitorSplashScreen (7.0.1):
    - Capacitor
  - CapacitorStatusBar (7.0.1):
    - Capacitor
  - CapacitorToast (7.0.1):
    - Capacitor
  - CapawesomeCapacitorBadge (7.0.1):
    - Capacitor
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMLKit/BarcodeScanning (7.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 6.0.0)
  - GoogleMLKit/MLKitCore (7.0.0):
    - MLKitCommon (~> 12.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMSessionFetcher/Core (3.5.0)
  - IONGeolocationLib (1.0.0)
  - JcesarmobileSslSkip (0.4.0):
    - Capacitor
  - MLImage (1.0.0-beta6)
  - MLKitBarcodeScanning (6.0.0):
    - MLKitCommon (~> 12.0)
    - MLKitVision (~> 8.0)
  - MLKitCommon (12.0.0):
    - GoogleDataTransport (~> 10.0)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/Logger (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (8.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta6)
    - MLKitCommon (~> 12.0)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - PantristCapacitorDatePicker (7.0.0):
    - Capacitor
  - PromisesObjC (2.4.0)
  - TrackMyLocation (0.0.1):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorCamera (from `../../node_modules/@capacitor/camera`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorGeolocation (from `../../node_modules/@capacitor/geolocation`)"
  - "CapacitorMlkitBarcodeScanning (from `../../node_modules/@capacitor-mlkit/barcode-scanning`)"
  - CapacitorNativeSettings (from `../../node_modules/capacitor-native-settings`)
  - "CapacitorPushNotifications (from `../../node_modules/@capacitor/push-notifications`)"
  - "CapacitorSplashScreen (from `../../node_modules/@capacitor/splash-screen`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"
  - "CapacitorToast (from `../../node_modules/@capacitor/toast`)"
  - "CapawesomeCapacitorBadge (from `../../node_modules/@capawesome/capacitor-badge`)"
  - "JcesarmobileSslSkip (from `../../node_modules/@jcesarmobile/ssl-skip`)"
  - "PantristCapacitorDatePicker (from `../../node_modules/@pantrist/capacitor-date-picker`)"
  - TrackMyLocation (from `../../track-my-location`)

SPEC REPOS:
  trunk:
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GTMSessionFetcher
    - IONGeolocationLib
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorCamera:
    :path: "../../node_modules/@capacitor/camera"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorGeolocation:
    :path: "../../node_modules/@capacitor/geolocation"
  CapacitorMlkitBarcodeScanning:
    :path: "../../node_modules/@capacitor-mlkit/barcode-scanning"
  CapacitorNativeSettings:
    :path: "../../node_modules/capacitor-native-settings"
  CapacitorPushNotifications:
    :path: "../../node_modules/@capacitor/push-notifications"
  CapacitorSplashScreen:
    :path: "../../node_modules/@capacitor/splash-screen"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"
  CapacitorToast:
    :path: "../../node_modules/@capacitor/toast"
  CapawesomeCapacitorBadge:
    :path: "../../node_modules/@capawesome/capacitor-badge"
  JcesarmobileSslSkip:
    :path: "../../node_modules/@jcesarmobile/ssl-skip"
  PantristCapacitorDatePicker:
    :path: "../../node_modules/@pantrist/capacitor-date-picker"
  TrackMyLocation:
    :path: "../../track-my-location"

SPEC CHECKSUMS:
  Capacitor: 03bc7cbdde6a629a8b910a9d7d78c3cc7ed09ea7
  CapacitorCamera: 6e73f1fc6c629a672658705a02409b60854bc0f1
  CapacitorCordova: 5967b9ba03915ef1d585469d6e31f31dc49be96f
  CapacitorDevice: c6f6d587dd310527f8a48bf09c4e7b4a4cf14329
  CapacitorGeolocation: b005744861676af9abd19a0682f0b1af32cc2c99
  CapacitorMlkitBarcodeScanning: 147c172f6e423618b156e050f715cfb963680ef4
  CapacitorNativeSettings: 3cd2e2ef431fe471674f381609cfb29154ddfcfa
  CapacitorPushNotifications: 7c0659b349b149ee3936a682da31a9d269de9582
  CapacitorSplashScreen: 1d67815a422a9b61539c94f283c08ed56667c0fc
  CapacitorStatusBar: 6e7af040d8fc4dd655999819625cae9c2d74c36f
  CapacitorToast: ddfc4b36080dbe3634c9faf510b20f6ddcff9330
  CapawesomeCapacitorBadge: 7fc1fa00c920b88eabb8451ddc92bdc75da2cffa
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMLKit: eff9e23ec1d90ea4157a1ee2e32a4f610c5b3318
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  IONGeolocationLib: 81f33f88d025846946de2cf63b0c7628e7c6bc9d
  JcesarmobileSslSkip: 5fa98636a64c36faa50f32ab4daf34e38f4d45b9
  MLImage: 0ad1c5f50edd027672d8b26b0fee78a8b4a0fc56
  MLKitBarcodeScanning: 0a3064da0a7f49ac24ceb3cb46a5bc67496facd2
  MLKitCommon: 07c2c33ae5640e5380beaaa6e4b9c249a205542d
  MLKitVision: 45e79d68845a2de77e2dd4d7f07947f0ed157b0e
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  PantristCapacitorDatePicker: e4088c0f514b46d46bd910df5ce00f1e2cdd4d9f
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  TrackMyLocation: 7de0ca4e1c870db06d4be0018b88a719c24b21e2

PODFILE CHECKSUM: 127d057469e3d5e779a87f862d72c65fba48c46f

COCOAPODS: 1.16.2
