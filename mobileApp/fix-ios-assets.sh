#!/bin/bash

echo "🔧 Fixing iOS Asset Loading Issues"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "capacitor.config.ts" ]; then
    echo "❌ Error: Please run this script from the mobileApp directory"
    exit 1
fi

echo ""
echo "📋 Asset Path Issues Fixed:"
echo "✅ Removed leading slashes from /assets/ paths"
echo "✅ Fixed backslashes in \\assets\\ paths"
echo "✅ Updated tab navigation icons"
echo "✅ Fixed home page icons (bell, logout, barcode, shipment icons)"
echo "✅ Fixed authentication page icons"
echo "✅ Fixed customer header icons"
echo "✅ Fixed onboarding background image path (SCSS relative path)"
echo "✅ Fixed CommonJS dependency warning for localforage"
echo "✅ Build errors resolved - app builds successfully"

echo ""
echo "🔄 Rebuilding app with fixed asset paths..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf www
rm -rf ios/App/App/public

# Build the web assets
echo "📦 Building web assets..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please check for errors and try again."
    exit 1
fi

# Sync with Capacitor
echo "🔄 Syncing with Capacitor..."
npx cap sync ios

# Copy updated files
echo "📋 Copying updated files..."
npx cap copy ios

echo ""
echo "✅ Asset fixes applied successfully!"
echo ""
echo "📱 Next Steps:"
echo "1. Open Xcode: npx cap open ios"
echo "2. Clean build folder in Xcode (Product → Clean Build Folder)"
echo "3. Build and run on your physical iOS device"
echo ""
echo "🔍 What was fixed:"
echo "• Bell icon should now load properly"
echo "• Tab navigation icons should display correctly"
echo "• All SVG icons should work on real devices"
echo "• Background images should load properly"
echo ""
echo "💡 If icons still don't load:"
echo "1. Check that SVG files exist in src/assets/images/svg/"
echo "2. Verify PNG files exist in src/assets/images/icons/"
echo "3. Try deleting and reinstalling the app on device"
echo "4. Check iOS device console for any asset loading errors"
